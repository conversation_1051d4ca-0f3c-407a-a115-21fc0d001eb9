<?php
// 数据库连接配置
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 如果直接访问config.php，返回状态信息
if (basename($_SERVER['PHP_SELF']) == 'config.php') {
    echo json_encode([
        'success' => true,
        'message' => 'API配置文件工作正常',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 数据库配置 - 请根据您的宝塔面板配置修改
$host = 'localhost';
$dbname = 'sales_management'; // 您创建的数据库名
$username = 'sales_management'; // 数据库用户名
$password = 'ZnykMPWCW6j74hGi'; // 数据库密码，请填入实际密码

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => '数据库连接失败: ' . $e->getMessage()]);
    exit;
}

// 通用响应函数
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit;
}

// 错误响应函数
function errorResponse($message, $status = 400) {
    http_response_code($status);
    echo json_encode(['error' => $message]);
    exit;
}
?> 