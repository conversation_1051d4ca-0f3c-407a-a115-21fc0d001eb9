// =====================================================
// 修复现有记录ID映射脚本
// 在浏览器控制台中运行此脚本
// =====================================================

console.log('🔧 启动现有记录ID映射修复...');

async function 修复现有记录ID映射() {
    console.log('\n🔍 === 开始修复现有记录ID映射 ===');
    
    if (!salesData || salesData.length === 0) {
        console.log('❌ 没有销售数据需要修复');
        return;
    }
    
    console.log(`📊 总共有 ${salesData.length} 条记录需要检查`);
    
    let 修复成功数 = 0;
    let 修复失败数 = 0;
    let 无需修复数 = 0;
    
    for (let i = 0; i < salesData.length; i++) {
        const 记录 = salesData[i];
        console.log(`\n🔍 检查记录 ${i + 1}: 客户=${记录.customer}`);
        
        // 检查是否需要修复
        if (记录.databaseId && 记录.databaseId !== 记录.id && 记录.databaseId.includes('-')) {
            console.log(`  ✅ ID映射正常: ${记录.databaseId}`);
            无需修复数++;
            continue;
        }
        
        console.log(`  ⚠️ 需要修复ID映射，当前数据库ID: ${记录.databaseId}`);
        
        try {
            // 方法1: 通过frontend_id查询
            let 查询结果 = await supabase
                .from('sales_records')
                .select('id')
                .eq('frontend_id', 记录.id)
                .single();
            
            if (查询结果.error || !查询结果.data) {
                console.log(`  方法1失败，尝试方法2: 通过客户名和金额查询`);
                
                // 方法2: 通过客户名和合同金额查询
                查询结果 = await supabase
                    .from('sales_records')
                    .select('id')
                    .eq('customer', 记录.customer)
                    .eq('contract_amount', 记录.contractAmount || 0);
                
                if (查询结果.error || !查询结果.data || 查询结果.data.length === 0) {
                    console.log(`  方法2也失败，尝试方法3: 仅通过客户名查询`);
                    
                    // 方法3: 仅通过客户名查询
                    查询结果 = await supabase
                        .from('sales_records')
                        .select('id, contract_amount, date')
                        .eq('customer', 记录.customer);
                    
                    if (查询结果.error || !查询结果.data || 查询结果.data.length === 0) {
                        console.log(`  ❌ 所有查询方法都失败，可能记录不存在于数据库中`);
                        修复失败数++;
                        continue;
                    }
                    
                    // 如果有多条记录，选择最匹配的
                    if (查询结果.data.length > 1) {
                        console.log(`  🔍 找到多条记录，尝试精确匹配...`);
                        const 精确匹配 = 查询结果.data.find(r => 
                            r.contract_amount === 记录.contractAmount && r.date === 记录.date
                        );
                        
                        if (精确匹配) {
                            查询结果.data = 精确匹配;
                            console.log(`  ✅ 找到精确匹配记录`);
                        } else {
                            查询结果.data = 查询结果.data[0];
                            console.log(`  ⚠️ 使用第一条记录作为匹配`);
                        }
                    } else {
                        查询结果.data = 查询结果.data[0];
                    }
                } else {
                    // 方法2成功，处理多条结果
                    if (Array.isArray(查询结果.data)) {
                        查询结果.data = 查询结果.data[0];
                    }
                }
            }
            
            const 数据库ID = 查询结果.data.id;
            console.log(`  ✅ 找到数据库ID: ${数据库ID}`);
            
            // 更新前端记录
            记录.databaseId = 数据库ID;
            修复成功数++;
            
            console.log(`  ✅ 修复成功: ${记录.customer} -> ${数据库ID}`);
            
        } catch (error) {
            console.log(`  ❌ 修复失败: ${error.message}`);
            修复失败数++;
        }
    }
    
    // 保存修复后的数据
    if (修复成功数 > 0) {
        localStorage.setItem('salesData', JSON.stringify(salesData));
        console.log('\n💾 已保存修复后的数据到本地存储');
    }
    
    console.log('\n📊 === 修复统计 ===');
    console.log(`✅ 修复成功: ${修复成功数} 条`);
    console.log(`❌ 修复失败: ${修复失败数} 条`);
    console.log(`⭕ 无需修复: ${无需修复数} 条`);
    console.log(`📊 总计: ${salesData.length} 条`);
    
    if (修复成功数 > 0) {
        console.log('\n🎉 ID映射修复完成！现在可以正常使用收款功能了');
        console.log('建议刷新页面以确保所有功能正常工作');
    }
    
    return {
        修复成功数,
        修复失败数,
        无需修复数,
        总数: salesData.length
    };
}

// 快速测试当前记录的收款功能
async function 测试当前记录收款功能() {
    console.log('\n🧪 === 测试当前记录收款功能 ===');
    
    // 查找测试记录
    const 测试记录 = salesData.find(r => r.customer === '测试测试12');
    if (!测试记录) {
        console.log('❌ 未找到测试记录');
        return;
    }
    
    console.log('测试记录:', 测试记录);
    
    if (!测试记录.databaseId || 测试记录.databaseId === 测试记录.id) {
        console.log('❌ 测试记录的数据库ID映射有问题，请先运行修复');
        return;
    }
    
    try {
        // 测试查询收款记录
        const 收款查询 = await supabase
            .from('payment_records')
            .select('*')
            .eq('sales_record_id', 测试记录.databaseId);
        
        if (收款查询.error) {
            console.log('❌ 收款记录查询失败:', 收款查询.error.message);
        } else {
            console.log('✅ 收款记录查询成功:', 收款查询.data.length, '条记录');
            
            // 测试添加收款记录
            const 测试收款数据 = {
                sales_record_id: 测试记录.databaseId,
                payment_date: new Date().toISOString().split('T')[0],
                amount: 500,
                payment_method: '银行转账',
                bank_info: '测试银行',
                transaction_id: 'TEST' + Date.now(),
                notes: '测试收款记录 - ' + new Date().toLocaleString(),
                created_by: currentUser?.email || '<EMAIL>'
            };
            
            const 插入结果 = await supabase
                .from('payment_records')
                .insert([测试收款数据]);
            
            if (插入结果.error) {
                console.log('❌ 测试添加收款记录失败:', 插入结果.error.message);
            } else {
                console.log('✅ 测试添加收款记录成功！');
                console.log('🎉 收款功能测试通过，现在可以正常使用了');
                
                // 刷新收款记录显示
                if (typeof loadPaymentRecords === 'function') {
                    await loadPaymentRecords(测试记录.databaseId);
                    console.log('✅ 已刷新收款记录显示');
                }
            }
        }
    } catch (error) {
        console.log('❌ 测试异常:', error.message);
    }
}

// 导出函数到全局
window.修复现有记录ID映射 = 修复现有记录ID映射;
window.测试当前记录收款功能 = 测试当前记录收款功能;

console.log('\n🎯 修复脚本已加载！');
console.log('可用命令:');
console.log('  修复现有记录ID映射()     - 修复所有记录的ID映射');
console.log('  测试当前记录收款功能()   - 测试收款功能是否正常');
console.log('\n推荐执行顺序:');
console.log('1. 修复现有记录ID映射()');
console.log('2. 测试当前记录收款功能()');

// 自动开始修复
修复现有记录ID映射();
