// =====================================================
// 实时诊断系统 - 将浏览器诊断信息写入本地文件
// 在浏览器控制台中运行此脚本
// =====================================================

// 诊断日志文件路径
const LOG_FILE_PATH = 'diagnostic_log.txt';

// 创建诊断日志写入函数
function 写入诊断日志(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    // 使用localStorage暂存日志（因为浏览器无法直接写文件）
    const existingLogs = localStorage.getItem('diagnosticLogs') || '';
    const newLogs = existingLogs + logEntry;
    
    // 保持最近1000行日志
    const lines = newLogs.split('\n');
    if (lines.length > 1000) {
        lines.splice(0, lines.length - 1000);
    }
    
    localStorage.setItem('diagnosticLogs', lines.join('\n'));
    
    // 同时输出到控制台
    console.log(`[诊断] ${message}`);
}

// 导出日志到文件的函数
function 导出诊断日志() {
    const logs = localStorage.getItem('diagnosticLogs') || '';
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'diagnostic_log.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('✅ 诊断日志已导出到 diagnostic_log.txt');
}

// 清空诊断日志
function 清空诊断日志() {
    localStorage.removeItem('diagnosticLogs');
    console.log('✅ 诊断日志已清空');
}

// 获取当前诊断状态
async function 获取诊断状态() {
    const status = {
        timestamp: new Date().toISOString(),
        frontend_records: salesData?.length || 0,
        database_records: 0,
        sync_status: 'unknown',
        current_user: currentUser?.email || 'unknown',
        user_permissions: userPermissions?.role || 'unknown',
        latest_record: null,
        payment_records_count: 0,
        errors: []
    };
    
    try {
        // 查询数据库记录
        const dbQuery = await supabase
            .from('sales_records')
            .select('*')
            .order('created_at', { ascending: false });
            
        if (dbQuery.error) {
            status.errors.push(`数据库查询失败: ${dbQuery.error.message}`);
        } else {
            status.database_records = dbQuery.data.length;
            status.sync_status = status.frontend_records === status.database_records ? 'synced' : 'out_of_sync';
            
            if (dbQuery.data.length > 0) {
                status.latest_record = {
                    id: dbQuery.data[0].id,
                    customer: dbQuery.data[0].customer,
                    created_at: dbQuery.data[0].created_at,
                    created_by: dbQuery.data[0].created_by
                };
                
                // 查询最新记录的收款信息
                const paymentQuery = await supabase
                    .from('payment_records')
                    .select('*')
                    .eq('sales_record_id', dbQuery.data[0].id);
                    
                if (paymentQuery.error) {
                    status.errors.push(`收款记录查询失败: ${paymentQuery.error.message}`);
                } else {
                    status.payment_records_count = paymentQuery.data.length;
                }
            }
        }
        
    } catch (error) {
        status.errors.push(`诊断过程异常: ${error.message}`);
    }
    
    return status;
}

// 持续监控函数
let 监控定时器 = null;

async function 开始持续监控() {
    if (监控定时器) {
        clearInterval(监控定时器);
    }
    
    写入诊断日志('🚀 开始持续监控数据同步状态...');
    
    监控定时器 = setInterval(async () => {
        const status = await 获取诊断状态();
        const statusMsg = `状态检查 - 前端:${status.frontend_records} 数据库:${status.database_records} 同步:${status.sync_status} 用户:${status.current_user} 权限:${status.user_permissions}`;
        
        if (status.errors.length > 0) {
            写入诊断日志(`❌ ${statusMsg} 错误:${status.errors.join(', ')}`);
        } else {
            写入诊断日志(`✅ ${statusMsg}`);
        }
        
        if (status.latest_record) {
            写入诊断日志(`📋 最新记录: ${status.latest_record.customer} (${status.latest_record.created_at}) 收款记录:${status.payment_records_count}条`);
        }
        
    }, 10000); // 每10秒检查一次
    
    console.log('✅ 持续监控已启动，每10秒检查一次状态');
}

function 停止持续监控() {
    if (监控定时器) {
        clearInterval(监控定时器);
        监控定时器 = null;
        写入诊断日志('⏹️ 持续监控已停止');
        console.log('✅ 持续监控已停止');
    }
}

// 监控关键函数调用
function 监控关键函数() {
    // 监控数据加载函数
    if (typeof loadTableData === 'function') {
        const originalLoadTableData = loadTableData;
        window.loadTableData = async function(...args) {
            写入诊断日志('📊 开始执行 loadTableData...');
            try {
                const result = await originalLoadTableData.apply(this, args);
                写入诊断日志(`✅ loadTableData 完成，当前记录数: ${salesData?.length || 0}`);
                return result;
            } catch (error) {
                写入诊断日志(`❌ loadTableData 失败: ${error.message}`);
                throw error;
            }
        };
    }
    
    // 监控收款记录加载
    if (typeof loadPaymentRecords === 'function') {
        const originalLoadPaymentRecords = loadPaymentRecords;
        window.loadPaymentRecords = async function(salesRecordId, ...args) {
            写入诊断日志(`💰 开始查询收款记录，销售ID: ${salesRecordId}`);
            try {
                const result = await originalLoadPaymentRecords.apply(this, [salesRecordId, ...args]);
                写入诊断日志(`✅ 收款记录查询完成`);
                return result;
            } catch (error) {
                写入诊断日志(`❌ 收款记录查询失败: ${error.message}`);
                throw error;
            }
        };
    }
    
    写入诊断日志('🔧 关键函数监控已启用');
}

// 页面事件监控
function 监控页面事件() {
    // 监控页面刷新
    window.addEventListener('beforeunload', () => {
        写入诊断日志('🔄 页面即将刷新/关闭');
    });
    
    // 监控错误
    window.addEventListener('error', (event) => {
        写入诊断日志(`❌ 页面错误: ${event.message} at ${event.filename}:${event.lineno}`);
    });
    
    // 监控未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
        写入诊断日志(`❌ 未处理的Promise拒绝: ${event.reason}`);
    });
    
    写入诊断日志('📱 页面事件监控已启用');
}

// 初始化实时诊断系统
async function 初始化实时诊断() {
    写入诊断日志('🚀 实时诊断系统初始化...');
    
    // 记录初始状态
    const initialStatus = await 获取诊断状态();
    写入诊断日志(`📊 初始状态 - 前端:${initialStatus.frontend_records} 数据库:${initialStatus.database_records} 同步:${initialStatus.sync_status}`);
    
    // 启用各种监控
    监控关键函数();
    监控页面事件();
    开始持续监控();
    
    // 添加全局函数
    window.导出诊断日志 = 导出诊断日志;
    window.清空诊断日志 = 清空诊断日志;
    window.停止持续监控 = 停止持续监控;
    window.开始持续监控 = 开始持续监控;
    
    console.log('🎯 实时诊断系统已启动！');
    console.log('📋 可用命令:');
    console.log('- 导出诊断日志() - 导出日志文件');
    console.log('- 清空诊断日志() - 清空所有日志');
    console.log('- 停止持续监控() - 停止自动监控');
    console.log('- 开始持续监控() - 重新开始监控');
}

// 自动启动
初始化实时诊断();
