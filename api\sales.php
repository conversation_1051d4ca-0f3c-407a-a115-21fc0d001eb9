<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// 响应函数
function sendResponse($success, $message, $data = null) {
    $response = array(
        'success' => $success,
        'message' => $message
    );
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? 'list';
$path = $_GET['path'] ?? '';

try {
    switch ($action) {
        case 'list':
        case 'records':
        case 'get_sales_records':
            // 获取销售记录 - 根据用户角色进行权限过滤
            $user_id = $_GET['user_id'] ?? '';
            $user_email = $_GET['user_email'] ?? '';
            
            if (empty($user_id) && empty($user_email)) {
                sendResponse(false, '缺少用户身份信息');
            }
            
            // 获取用户信息和角色
            $user_stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = ? OR email = ?");
            $user_stmt->execute([$user_id, $user_email]);
            $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                sendResponse(false, '用户不存在');
            }
            
            $user_role = $user['role'];
            
            // 根据角色权限查询不同的数据
            if ($user_role === 'admin' || $user_role === 'finance') {
                // 管理员和财务可以看到所有记录
                $stmt = $pdo->prepare("SELECT * FROM sales_records ORDER BY created_at DESC");
                $stmt->execute();
            } elseif ($user_role === 'manager') {
                // 经理可以看到团队的记录（这里简化为看到所有，实际应该根据团队关系过滤）
                $stmt = $pdo->prepare("SELECT * FROM sales_records ORDER BY created_at DESC");
                $stmt->execute();
            } else {
                // 销售人员只能看到自己的记录
                $stmt = $pdo->prepare("SELECT * FROM sales_records WHERE created_by = ? OR user_id = ? ORDER BY created_at DESC");
                $stmt->execute([$user['user_id'], $user['user_id']]);
            }
            
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取记录成功', ['records' => $records, 'user_role' => $user_role, 'total_count' => count($records)]);
            break;
            
        case 'add_sales_record':
            // 添加销售记录
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 验证用户身份
            $created_by = $input['created_by'] ?? '';
            if (empty($created_by)) {
                sendResponse(false, '缺少创建者信息');
            }
            
            // 获取创建者信息
            $user_stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = ? OR email = ?");
            $user_stmt->execute([$input['user_id'] ?? '', $created_by]);
            $creator = $user_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$creator) {
                sendResponse(false, '创建者用户不存在');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO sales_records 
                (frontend_id, date, customer, customer_source, business_type, amount, income_type, salesperson, contract_amount, remarks, user_id, created_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $result = $stmt->execute([
                $input['frontend_id'],
                $input['date'],
                $input['customer'],
                $input['customer_source'],
                $input['business_type'],
                $input['amount'],
                $input['income_type'],
                $input['salesperson'],
                $input['contract_amount'],
                $input['remarks'],
                $creator['user_id'],
                $creator['email']
            ]);
            
            if ($result) {
                sendResponse(true, '记录添加成功', ['created_by' => $creator['email'], 'user_role' => $creator['role']]);
            } else {
                sendResponse(false, '记录添加失败');
            }
            break;
            
        case 'delete_sales_record':
            // 删除销售记录 - 权限控制：只有创建者或管理员可以删除
            $id = $_GET['id'] ?? '';
            $user_id = $_GET['user_id'] ?? '';
            $user_email = $_GET['user_email'] ?? '';
            
            if (empty($id)) {
                sendResponse(false, '缺少记录ID');
            }
            
            if (empty($user_id) && empty($user_email)) {
                sendResponse(false, '缺少用户身份信息');
            }
            
            // 获取用户信息
            $user_stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = ? OR email = ?");
            $user_stmt->execute([$user_id, $user_email]);
            $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                sendResponse(false, '用户不存在');
            }
            
            // 获取要删除的记录
            $record_stmt = $pdo->prepare("SELECT * FROM sales_records WHERE frontend_id = ? OR id = ?");
            $record_stmt->execute([$id, $id]);
            $record = $record_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$record) {
                sendResponse(false, '记录不存在');
            }
            
            // 权限检查：管理员可删除所有，其他人只能删除自己的
            $canDelete = false;
            if ($user['role'] === 'admin' || $user['role'] === 'finance') {
                $canDelete = true; // 管理员和财务可以删除所有记录
            } elseif ($record['created_by'] === $user['email'] || $record['user_id'] === $user['user_id']) {
                $canDelete = true; // 创建者可以删除自己的记录
            }
            
            if (!$canDelete) {
                sendResponse(false, '权限不足，只能删除自己创建的记录');
            }
            
            // 执行删除
            $stmt = $pdo->prepare("DELETE FROM sales_records WHERE frontend_id = ? OR id = ?");
            $result = $stmt->execute([$id, $id]);
            
            if ($result) {
                sendResponse(true, '记录删除成功');
            } else {
                sendResponse(false, '记录删除失败');
            }
            break;
            
        case 'get_user_profile':
            // 获取用户资料
            $user_id = $_GET['user_id'] ?? '';
            if (empty($user_id)) {
                sendResponse(false, '缺少用户ID');
            }
            
            $stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = ? OR email = ?");
            $stmt->execute([$user_id, $user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                sendResponse(true, '获取用户资料成功', $user);
            } else {
                sendResponse(false, '用户不存在');
            }
            break;
            
        case 'get_system_settings':
            // 获取系统设置
            $stmt = $pdo->prepare("SELECT * FROM system_settings");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $result = array();
            foreach ($settings as $setting) {
                $result[$setting['setting_type']] = json_decode($setting['setting_data'], true);
            }
            
            sendResponse(true, '获取系统设置成功', $result);
            break;
            
        case 'get_salespeople':
            // 获取销售人员列表
            $stmt = $pdo->prepare("SELECT * FROM salesperson ORDER BY name");
            $stmt->execute();
            $salespeople = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取销售人员成功', $salespeople);
            break;
            
        case 'get_teams':
            // 获取团队列表
            $stmt = $pdo->prepare("SELECT * FROM sales_teams ORDER BY team_name");
            $stmt->execute();
            $teams = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取团队列表成功', $teams);
            break;
            
        case 'get_sales_summary':
            // 获取销售汇总
            $stmt = $pdo->prepare("SELECT * FROM sales_summary");
            $stmt->execute();
            $summary = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取销售汇总成功', $summary);
            break;
            
        // === 选项数据管理 ===
        
        case 'get_customer_sources':
            // 获取客户来源列表
            $stmt = $pdo->prepare("SELECT * FROM customer_sources WHERE is_active = 1 ORDER BY display_order, name");
            $stmt->execute();
            $sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取客户来源成功', $sources);
            break;
            
        case 'get_business_types':
            // 获取业务类型列表
            $stmt = $pdo->prepare("SELECT * FROM business_types WHERE is_active = 1 ORDER BY display_order, name");
            $stmt->execute();
            $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取业务类型成功', $types);
            break;
            
        case 'get_income_types':
            // 获取收入类型列表
            $stmt = $pdo->prepare("SELECT * FROM income_types WHERE is_active = 1 ORDER BY display_order, name");
            $stmt->execute();
            $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取收入类型成功', $types);
            break;
            
        case 'get_salespeople_list':
            // 获取销售人员列表（新版本）
            $stmt = $pdo->prepare("SELECT * FROM salespeople WHERE is_active = 1 ORDER BY display_order, name");
            $stmt->execute();
            $salespeople = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(true, '获取销售人员成功', $salespeople);
            break;
            
        case 'get_all_options':
            // 一次性获取所有选项数据
            try {
                $options = [];
                
                // 客户来源
                $stmt = $pdo->prepare("SELECT name FROM customer_sources WHERE is_active = 1 ORDER BY display_order, name");
                $stmt->execute();
                $options['customerSources'] = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'name');
                
                // 业务类型
                $stmt = $pdo->prepare("SELECT name FROM business_types WHERE is_active = 1 ORDER BY display_order, name");
                $stmt->execute();
                $options['businessTypes'] = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'name');
                
                // 收入类型
                $stmt = $pdo->prepare("SELECT name FROM income_types WHERE is_active = 1 ORDER BY display_order, name");
                $stmt->execute();
                $options['incomeTypes'] = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'name');
                
                // 销售人员（返回完整信息）
                $stmt = $pdo->prepare("SELECT * FROM salespeople WHERE is_active = 1 ORDER BY display_order, name");
                $stmt->execute();
                $options['salespeople'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                sendResponse(true, '获取所有选项成功', $options);
            } catch (Exception $e) {
                sendResponse(false, '获取选项数据失败: ' . $e->getMessage());
            }
            break;
            
        // === 选项数据添加 ===
        
        case 'add_customer_source':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '客户来源名称不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("INSERT INTO customer_sources (name) VALUES (?)");
                $result = $stmt->execute([$name]);
                sendResponse(true, '客户来源添加成功');
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '该客户来源已存在');
                } else {
                    sendResponse(false, '添加失败: ' . $e->getMessage());
                }
            }
            break;
            
        case 'add_business_type':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '业务类型名称不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("INSERT INTO business_types (name) VALUES (?)");
                $result = $stmt->execute([$name]);
                sendResponse(true, '业务类型添加成功');
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '该业务类型已存在');
                } else {
                    sendResponse(false, '添加失败: ' . $e->getMessage());
                }
            }
            break;
            
        case 'add_income_type':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '收入类型名称不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("INSERT INTO income_types (name) VALUES (?)");
                $result = $stmt->execute([$name]);
                sendResponse(true, '收入类型添加成功');
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '该收入类型已存在');
                } else {
                    sendResponse(false, '添加失败: ' . $e->getMessage());
                }
            }
            break;
            
        case 'add_salesperson':
            $name = trim($_POST['name'] ?? '');
            $baseSalary = floatval($_POST['base_salary'] ?? 5500);
            $commissionType = $_POST['commission_type'] ?? 'fixed';
            $fixedRate = floatval($_POST['fixed_rate'] ?? 0.06);
            
            if (empty($name)) {
                sendResponse(false, '销售人员姓名不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("INSERT INTO salespeople (name, base_salary, commission_type, fixed_rate) VALUES (?, ?, ?, ?)");
                $result = $stmt->execute([$name, $baseSalary, $commissionType, $fixedRate]);
                sendResponse(true, '销售人员添加成功');
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '该销售人员已存在');
                } else {
                    sendResponse(false, '添加失败: ' . $e->getMessage());
                }
            }
            break;
            
        case 'update_salesperson':
            // 更新销售人员信息（包括阶梯式提成）
            $id = $_POST['id'] ?? '';
            $name = trim($_POST['name'] ?? '');
            $oldName = trim($_POST['old_name'] ?? '');
            $baseSalary = floatval($_POST['base_salary'] ?? 5500);
            $commissionType = $_POST['commission_type'] ?? 'fixed';
            $fixedRate = floatval($_POST['fixed_rate'] ?? 0.06);
            $ladderRates = $_POST['ladder_rates'] ?? '';
            
            if (empty($id) && empty($name) && empty($oldName)) {
                sendResponse(false, '缺少销售人员标识');
            }
            
            try {
                // 处理阶梯提成数据
                $ladderRatesJson = null;
                if (!empty($ladderRates)) {
                    // 如果传入的是JSON字符串，解析后重新编码确保格式正确
                    $ladderData = is_string($ladderRates) ? json_decode($ladderRates, true) : $ladderRates;
                    if (is_array($ladderData)) {
                        $ladderRatesJson = json_encode($ladderData);
                    }
                }
                
                // 根据ID或名称更新
                if (!empty($id)) {
                    $stmt = $pdo->prepare("UPDATE salespeople SET name = ?, base_salary = ?, commission_type = ?, fixed_rate = ?, ladder_rates = ?, updated_at = NOW() WHERE id = ?");
                    $result = $stmt->execute([$name, $baseSalary, $commissionType, $fixedRate, $ladderRatesJson, $id]);
                } elseif (!empty($oldName)) {
                    $stmt = $pdo->prepare("UPDATE salespeople SET name = ?, base_salary = ?, commission_type = ?, fixed_rate = ?, ladder_rates = ?, updated_at = NOW() WHERE name = ?");
                    $result = $stmt->execute([$name, $baseSalary, $commissionType, $fixedRate, $ladderRatesJson, $oldName]);
                } else {
                    $stmt = $pdo->prepare("UPDATE salespeople SET base_salary = ?, commission_type = ?, fixed_rate = ?, ladder_rates = ?, updated_at = NOW() WHERE name = ?");
                    $result = $stmt->execute([$baseSalary, $commissionType, $fixedRate, $ladderRatesJson, $name]);
                }
                
                if ($result) {
                    sendResponse(true, '销售人员信息更新成功', [
                        'commission_type' => $commissionType,
                        'ladder_rates' => $ladderData ?? []
                    ]);
                } else {
                    sendResponse(false, '更新失败，请检查数据');
                }
            } catch (PDOException $e) {
                sendResponse(false, '数据库更新失败: ' . $e->getMessage());
            }
            break;
            
        // === 选项数据删除 ===
        
        case 'delete_business_type':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '业务类型名称不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("DELETE FROM business_types WHERE name = ?");
                $result = $stmt->execute([$name]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '业务类型删除成功');
                } else {
                    sendResponse(false, '未找到要删除的业务类型');
                }
            } catch (PDOException $e) {
                sendResponse(false, '删除失败: ' . $e->getMessage());
            }
            break;
            
        case 'delete_income_type':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '收入类型名称不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("DELETE FROM income_types WHERE name = ?");
                $result = $stmt->execute([$name]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '收入类型删除成功');
                } else {
                    sendResponse(false, '未找到要删除的收入类型');
                }
            } catch (PDOException $e) {
                sendResponse(false, '删除失败: ' . $e->getMessage());
            }
            break;
            
        case 'delete_customer_source':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '客户来源名称不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("DELETE FROM customer_sources WHERE name = ?");
                $result = $stmt->execute([$name]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '客户来源删除成功');
                } else {
                    sendResponse(false, '未找到要删除的客户来源');
                }
            } catch (PDOException $e) {
                sendResponse(false, '删除失败: ' . $e->getMessage());
            }
            break;
            
        case 'delete_salesperson':
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                sendResponse(false, '销售人员姓名不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("DELETE FROM salespeople WHERE name = ?");
                $result = $stmt->execute([$name]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '销售人员删除成功');
                } else {
                    sendResponse(false, '未找到要删除的销售人员');
                }
            } catch (PDOException $e) {
                sendResponse(false, '删除失败: ' . $e->getMessage());
            }
            break;
            
        // === 选项数据更新 ===
        
        case 'update_business_type':
            $oldName = trim($_POST['old_name'] ?? '');
            $newName = trim($_POST['new_name'] ?? '');
            
            if (empty($oldName) || empty($newName)) {
                sendResponse(false, '原名称和新名称都不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("UPDATE business_types SET name = ?, updated_at = NOW() WHERE name = ?");
                $result = $stmt->execute([$newName, $oldName]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '业务类型更新成功');
                } else {
                    sendResponse(false, '未找到要更新的业务类型');
                }
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '新的业务类型名称已存在');
                } else {
                    sendResponse(false, '更新失败: ' . $e->getMessage());
                }
            }
            break;
            
        case 'update_income_type':
            $oldName = trim($_POST['old_name'] ?? '');
            $newName = trim($_POST['new_name'] ?? '');
            
            if (empty($oldName) || empty($newName)) {
                sendResponse(false, '原名称和新名称都不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("UPDATE income_types SET name = ?, updated_at = NOW() WHERE name = ?");
                $result = $stmt->execute([$newName, $oldName]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '收入类型更新成功');
                } else {
                    sendResponse(false, '未找到要更新的收入类型');
                }
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '新的收入类型名称已存在');
                } else {
                    sendResponse(false, '更新失败: ' . $e->getMessage());
                }
            }
            break;
            
        case 'update_customer_source':
            $oldName = trim($_POST['old_name'] ?? '');
            $newName = trim($_POST['new_name'] ?? '');
            
            if (empty($oldName) || empty($newName)) {
                sendResponse(false, '原名称和新名称都不能为空');
            }
            
            try {
                $stmt = $pdo->prepare("UPDATE customer_sources SET name = ?, updated_at = NOW() WHERE name = ?");
                $result = $stmt->execute([$newName, $oldName]);
                if ($result && $stmt->rowCount() > 0) {
                    sendResponse(true, '客户来源更新成功');
                } else {
                    sendResponse(false, '未找到要更新的客户来源');
                }
            } catch (PDOException $e) {
                if ($e->getCode() == '23000') {
                    sendResponse(false, '新的客户来源名称已存在');
                } else {
                    sendResponse(false, '更新失败: ' . $e->getMessage());
                }
            }
            break;
            
        default:
            sendResponse(false, '未知操作: ' . $action);
    }
    
} catch (PDOException $e) {
    error_log('数据库错误: ' . $e->getMessage());
    sendResponse(false, '数据库操作失败: ' . $e->getMessage());
} catch (Exception $e) {
    error_log('系统错误: ' . $e->getMessage());
    sendResponse(false, '系统错误: ' . $e->getMessage());
}
?> 