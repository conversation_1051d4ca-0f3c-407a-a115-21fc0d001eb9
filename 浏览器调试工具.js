// =====================================================
// 浏览器调试工具 - 直接在控制台运行
// 复制此代码到浏览器控制台执行
// =====================================================

console.log('🔧 启动浏览器调试工具...');

// 快速诊断函数
async function 快速诊断() {
    console.log('\n🔍 === 快速诊断开始 ===');
    
    // 1. 检查基础变量
    console.log('1. 检查基础变量:');
    console.log('   salesData:', typeof salesData, salesData?.length);
    console.log('   currentDatabaseRecordId:', currentDatabaseRecordId);
    console.log('   currentUser:', currentUser?.email);
    console.log('   supabase:', typeof supabase);
    
    // 2. 检查当前记录
    console.log('\n2. 检查当前记录:');
    if (currentDatabaseRecordId) {
        const 当前记录 = salesData?.find(r => r.databaseId === currentDatabaseRecordId || r.id === currentDatabaseRecordId);
        if (当前记录) {
            console.log('   ✅ 找到当前记录:', {
                客户: 当前记录.customer,
                前端ID: 当前记录.id,
                数据库ID: 当前记录.databaseId,
                合同金额: 当前记录.contractAmount
            });
        } else {
            console.log('   ❌ 未找到当前记录');
        }
    } else {
        console.log('   ❌ currentDatabaseRecordId为空');
    }
    
    // 3. 测试数据库查询
    console.log('\n3. 测试数据库查询:');
    try {
        if (currentDatabaseRecordId) {
            const 收款查询 = await supabase
                .from('payment_records')
                .select('*')
                .eq('sales_record_id', currentDatabaseRecordId);
            
            if (收款查询.error) {
                console.log('   ❌ 收款查询失败:', 收款查询.error.message);
            } else {
                console.log('   ✅ 收款查询成功:', 收款查询.data.length, '条记录');
                if (收款查询.data.length > 0) {
                    console.log('   收款记录示例:', 收款查询.data[0]);
                }
            }
        }
    } catch (error) {
        console.log('   ❌ 数据库查询异常:', error.message);
    }
    
    console.log('\n🔍 === 快速诊断完成 ===');
}

// 修复ID映射
async function 修复ID映射() {
    console.log('\n🔧 === 开始修复ID映射 ===');
    
    if (!salesData || salesData.length === 0) {
        console.log('❌ 没有销售数据');
        return;
    }
    
    let 修复计数 = 0;
    
    for (let i = 0; i < salesData.length; i++) {
        const 记录 = salesData[i];
        
        // 如果没有数据库ID或数据库ID等于前端ID，尝试修复
        if (!记录.databaseId || 记录.databaseId === 记录.id) {
            try {
                console.log(`修复记录: ${记录.customer}`);
                
                // 查询数据库中的实际ID
                const 查询结果 = await supabase
                    .from('sales_records')
                    .select('id')
                    .eq('customer', 记录.customer)
                    .eq('contract_amount', 记录.contractAmount)
                    .single();
                
                if (查询结果.error) {
                    console.log(`  ❌ 查询失败: ${查询结果.error.message}`);
                } else {
                    记录.databaseId = 查询结果.data.id;
                    console.log(`  ✅ 修复成功: ${查询结果.data.id}`);
                    修复计数++;
                }
            } catch (error) {
                console.log(`  ❌ 修复异常: ${error.message}`);
            }
        }
    }
    
    console.log(`🔧 ID映射修复完成，修复了 ${修复计数} 条记录`);
}

// 强制刷新收款记录
async function 强制刷新收款() {
    console.log('\n🔄 === 强制刷新收款记录 ===');
    
    if (!currentDatabaseRecordId) {
        console.log('❌ currentDatabaseRecordId为空');
        return;
    }
    
    try {
        // 直接调用loadPaymentRecords
        if (typeof loadPaymentRecords === 'function') {
            console.log('调用loadPaymentRecords...');
            await loadPaymentRecords(currentDatabaseRecordId);
            console.log('✅ loadPaymentRecords调用完成');
        } else {
            console.log('❌ loadPaymentRecords函数不存在');
        }
        
        // 手动查询并更新显示
        console.log('手动查询收款记录...');
        const 收款查询 = await supabase
            .from('payment_records')
            .select('*')
            .eq('sales_record_id', currentDatabaseRecordId)
            .order('payment_date', { ascending: true });
        
        if (收款查询.error) {
            console.log('❌ 手动查询失败:', 收款查询.error.message);
        } else {
            console.log('✅ 手动查询成功:', 收款查询.data.length, '条记录');
            
            // 更新全局变量
            window.currentPaymentRecords = 收款查询.data;
            
            // 手动更新表格显示
            const tbody = document.querySelector('#paymentRecordsTable tbody');
            if (tbody) {
                tbody.innerHTML = '';
                
                if (收款查询.data.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #999;">暂无收款记录</td></tr>';
                } else {
                    收款查询.data.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${record.payment_date}</td>
                            <td>¥${Number(record.amount || 0).toLocaleString()}</td>
                            <td>${record.payment_method || '-'}</td>
                            <td>${record.bank_info || '-'}</td>
                            <td>${record.transaction_id || '-'}</td>
                            <td title="${record.notes || '-'}">${record.notes || '-'}</td>
                            <td>
                                <button class="delete-btn" onclick="deletePaymentRecord('${record.id}')" title="删除收款记录">🗑️</button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                }
                
                // 更新汇总信息
                const totalReceived = 收款查询.data.reduce((sum, record) => sum + Number(record.amount || 0), 0);
                const contractAmount = Number(document.getElementById('contractAmount')?.textContent?.replace(/[¥,]/g, '')) || 0;
                const remainingAmount = contractAmount - totalReceived;
                
                if (typeof updatePaymentSummary === 'function') {
                    updatePaymentSummary(totalReceived, remainingAmount, 收款查询.data.length);
                }
                
                console.log('✅ 表格显示已更新');
            }
        }
    } catch (error) {
        console.log('❌ 强制刷新异常:', error.message);
    }
}

// 添加测试收款记录
async function 添加测试收款() {
    console.log('\n➕ === 添加测试收款记录 ===');
    
    if (!currentDatabaseRecordId) {
        console.log('❌ currentDatabaseRecordId为空');
        return;
    }
    
    const 测试数据 = {
        sales_record_id: currentDatabaseRecordId,
        payment_date: new Date().toISOString().split('T')[0],
        amount: 1000,
        payment_method: '银行转账',
        bank_info: '测试银行',
        transaction_id: 'TEST' + Date.now(),
        notes: '测试收款记录 - ' + new Date().toLocaleString(),
        created_by: currentUser?.email || '<EMAIL>'
    };
    
    try {
        const 插入结果 = await supabase
            .from('payment_records')
            .insert([测试数据]);
        
        if (插入结果.error) {
            console.log('❌ 添加测试收款失败:', 插入结果.error.message);
        } else {
            console.log('✅ 添加测试收款成功');
            
            // 刷新显示
            await 强制刷新收款();
        }
    } catch (error) {
        console.log('❌ 添加测试收款异常:', error.message);
    }
}

// 一键修复
async function 一键修复() {
    console.log('\n🚀 === 开始一键修复 ===');
    
    await 快速诊断();
    await 修复ID映射();
    await 强制刷新收款();
    
    console.log('\n🎉 === 一键修复完成 ===');
    console.log('如果问题仍然存在，请运行: 添加测试收款()');
}

// 导出函数到全局
window.快速诊断 = 快速诊断;
window.修复ID映射 = 修复ID映射;
window.强制刷新收款 = 强制刷新收款;
window.添加测试收款 = 添加测试收款;
window.一键修复 = 一键修复;

console.log('\n🎯 浏览器调试工具已加载！');
console.log('可用命令:');
console.log('  快速诊断()     - 诊断当前状态');
console.log('  修复ID映射()   - 修复ID映射问题');
console.log('  强制刷新收款() - 刷新收款记录显示');
console.log('  添加测试收款() - 添加测试收款记录');
console.log('  一键修复()     - 执行完整修复流程');
console.log('\n推荐先运行: 一键修复()');

// 自动执行快速诊断
快速诊断();
