{"mcpServers": {"playwright-official": {"command": "npx", "args": ["@playwright/mcp", "--headless", "--port", "3001"], "env": {}, "description": "官方Playwright MCP服务器 - 浏览器自动化"}, "playwright-executeautomation": {"command": "npx", "args": ["@executeautomation/playwright-mcp-server"], "env": {}, "description": "ExecuteAutomation Playwright MCP - 增强的自动化功能"}, "browser-tools": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp"], "env": {}, "description": "AgentDesk Browser Tools - 浏览器调试和工具集成"}, "frontend-dev-tools": {"command": "npx", "args": ["@winds-ai/frontend-development-mcp-tools"], "env": {}, "description": "Winds AI Frontend Development - 完整的前端开发工具包"}}, "settings": {"autoStart": ["playwright-official"], "defaultBrowser": "chromium", "headless": true, "viewport": {"width": 1280, "height": 720}}}