// =====================================================
// 全面测试修复脚本 - 系统性测试和修复所有功能
// 在浏览器控制台中运行此脚本
// =====================================================

class 系统测试修复器 {
    constructor() {
        this.测试结果 = [];
        this.修复记录 = [];
        this.错误日志 = [];
    }

    // 记录测试结果
    记录测试(测试名称, 状态, 详情 = '') {
        const 结果 = {
            测试名称,
            状态, // 'PASS', 'FAIL', 'FIXED'
            详情,
            时间: new Date().toISOString()
        };
        this.测试结果.push(结果);
        
        const 状态图标 = {
            'PASS': '✅',
            'FAIL': '❌', 
            'FIXED': '🔧'
        };
        
        console.log(`${状态图标[状态]} ${测试名称}: ${详情}`);
    }

    // 记录修复操作
    记录修复(修复描述, 修复代码 = '') {
        const 修复 = {
            描述: 修复描述,
            代码: 修复代码,
            时间: new Date().toISOString()
        };
        this.修复记录.push(修复);
        console.log(`🔧 修复: ${修复描述}`);
    }

    // 1. 测试基础环境
    async 测试基础环境() {
        console.log('\n🔍 1. 测试基础环境...');
        
        // 检查关键变量
        if (typeof supabase !== 'undefined') {
            this.记录测试('Supabase连接', 'PASS', 'Supabase客户端已初始化');
        } else {
            this.记录测试('Supabase连接', 'FAIL', 'Supabase客户端未找到');
        }

        if (typeof salesData !== 'undefined') {
            this.记录测试('销售数据变量', 'PASS', `salesData存在，长度: ${salesData.length}`);
        } else {
            this.记录测试('销售数据变量', 'FAIL', 'salesData变量未定义');
        }

        if (typeof currentUser !== 'undefined') {
            this.记录测试('用户认证', 'PASS', `当前用户: ${currentUser?.email || '未知'}`);
        } else {
            this.记录测试('用户认证', 'FAIL', 'currentUser变量未定义');
        }

        // 检查关键函数
        const 关键函数 = [
            'loadTableData',
            'loadPaymentRecords', 
            'addSalesRecord',
            'openPaymentModal'
        ];

        关键函数.forEach(函数名 => {
            if (typeof window[函数名] === 'function') {
                this.记录测试(`函数${函数名}`, 'PASS', '函数存在');
            } else {
                this.记录测试(`函数${函数名}`, 'FAIL', '函数不存在');
            }
        });
    }

    // 2. 测试数据加载
    async 测试数据加载() {
        console.log('\n🔍 2. 测试数据加载...');
        
        try {
            // 测试数据库连接
            const 测试查询 = await supabase.from('sales_records').select('count', { count: 'exact' });
            if (测试查询.error) {
                this.记录测试('数据库连接', 'FAIL', `查询失败: ${测试查询.error.message}`);
            } else {
                this.记录测试('数据库连接', 'PASS', `数据库记录总数: ${测试查询.count}`);
            }

            // 测试销售记录加载
            if (typeof loadTableData === 'function') {
                await loadTableData();
                this.记录测试('销售记录加载', 'PASS', `加载了 ${salesData?.length || 0} 条记录`);
            } else {
                this.记录测试('销售记录加载', 'FAIL', 'loadTableData函数不存在');
            }

        } catch (error) {
            this.记录测试('数据加载异常', 'FAIL', error.message);
        }
    }

    // 3. 测试收款记录功能
    async 测试收款记录功能() {
        console.log('\n🔍 3. 测试收款记录功能...');
        
        if (!salesData || salesData.length === 0) {
            this.记录测试('收款测试前提', 'FAIL', '没有销售记录可测试');
            return;
        }

        const 测试记录 = salesData[0];
        console.log(`测试记录: ${测试记录.customer} (ID: ${测试记录.id})`);

        try {
            // 测试收款记录查询
            const 数据库ID = 测试记录.databaseId || 测试记录.id;
            const 收款查询 = await supabase
                .from('payment_records')
                .select('*')
                .eq('sales_record_id', 数据库ID);

            if (收款查询.error) {
                this.记录测试('收款记录查询', 'FAIL', `查询失败: ${收款查询.error.message}`);
                
                // 尝试修复：检查ID映射问题
                await this.修复ID映射问题(测试记录);
            } else {
                this.记录测试('收款记录查询', 'PASS', `找到 ${收款查询.data.length} 条收款记录`);
            }

            // 测试收款弹窗打开
            if (typeof openPaymentModal === 'function') {
                // 模拟点击收款管理按钮
                const 收款按钮 = document.querySelector(`[onclick*="openPaymentModal('${测试记录.id}')"]`);
                if (收款按钮) {
                    this.记录测试('收款按钮存在', 'PASS', '找到收款管理按钮');
                    
                    // 测试弹窗打开
                    try {
                        openPaymentModal(测试记录.id);
                        
                        // 检查弹窗是否打开
                        setTimeout(() => {
                            const 弹窗 = document.getElementById('paymentModal');
                            if (弹窗 && 弹窗.style.display !== 'none') {
                                this.记录测试('收款弹窗打开', 'PASS', '弹窗成功打开');
                            } else {
                                this.记录测试('收款弹窗打开', 'FAIL', '弹窗未打开');
                            }
                        }, 1000);
                        
                    } catch (error) {
                        this.记录测试('收款弹窗打开', 'FAIL', `打开失败: ${error.message}`);
                    }
                } else {
                    this.记录测试('收款按钮存在', 'FAIL', '未找到收款管理按钮');
                }
            } else {
                this.记录测试('收款弹窗函数', 'FAIL', 'openPaymentModal函数不存在');
            }

        } catch (error) {
            this.记录测试('收款功能测试异常', 'FAIL', error.message);
        }
    }

    // 4. 修复ID映射问题
    async 修复ID映射问题(销售记录) {
        console.log('\n🔧 修复ID映射问题...');
        
        try {
            // 检查记录的ID字段
            const 前端ID = 销售记录.id;
            const 数据库ID = 销售记录.databaseId;
            
            console.log(`前端ID: ${前端ID}, 数据库ID: ${数据库ID}`);
            
            if (!数据库ID) {
                // 尝试通过前端ID查找数据库记录
                const 查找结果 = await supabase
                    .from('sales_records')
                    .select('*')
                    .eq('frontend_id', 前端ID);
                    
                if (查找结果.data && 查找结果.data.length > 0) {
                    const 数据库记录 = 查找结果.data[0];
                    
                    // 更新前端记录的databaseId
                    销售记录.databaseId = 数据库记录.id;
                    
                    this.记录修复('ID映射修复', `为记录 ${前端ID} 设置数据库ID: ${数据库记录.id}`);
                    this.记录测试('ID映射修复', 'FIXED', '成功修复ID映射');
                    
                    return true;
                } else {
                    this.记录测试('ID映射修复', 'FAIL', '未找到对应的数据库记录');
                    return false;
                }
            }
            
            return true;
        } catch (error) {
            this.记录测试('ID映射修复异常', 'FAIL', error.message);
            return false;
        }
    }

    // 5. 测试新记录添加
    async 测试新记录添加() {
        console.log('\n🔍 5. 测试新记录添加...');
        
        // 创建测试数据
        const 测试数据 = {
            date: new Date().toISOString().split('T')[0],
            customer: '测试客户_' + Date.now(),
            customerSource: '客户转介绍',
            businessType: '新客户',
            incomeAmount: 1000,
            incomeType: '首付款',
            salesperson: '测试销售',
            contractAmount: 5000,
            notes: '自动化测试记录'
        };

        try {
            const 添加前记录数 = salesData?.length || 0;
            
            // 测试添加记录
            if (typeof addSalesRecord === 'function') {
                await addSalesRecord(测试数据);
                
                // 等待数据更新
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const 添加后记录数 = salesData?.length || 0;
                
                if (添加后记录数 > 添加前记录数) {
                    this.记录测试('新记录添加', 'PASS', `成功添加记录，总数从 ${添加前记录数} 增加到 ${添加后记录数}`);
                    
                    // 测试新记录的收款功能
                    const 新记录 = salesData[0]; // 假设最新记录在第一位
                    if (新记录.customer === 测试数据.customer) {
                        await this.测试新记录收款功能(新记录);
                    }
                } else {
                    this.记录测试('新记录添加', 'FAIL', '记录数量未增加');
                }
            } else {
                this.记录测试('新记录添加', 'FAIL', 'addSalesRecord函数不存在');
            }
            
        } catch (error) {
            this.记录测试('新记录添加异常', 'FAIL', error.message);
        }
    }

    // 6. 测试新记录的收款功能
    async 测试新记录收款功能(新记录) {
        console.log('\n🔍 6. 测试新记录收款功能...');
        
        try {
            // 确保记录有正确的databaseId
            if (!新记录.databaseId) {
                const 修复成功 = await this.修复ID映射问题(新记录);
                if (!修复成功) {
                    this.记录测试('新记录收款测试', 'FAIL', '无法获取数据库ID');
                    return;
                }
            }

            // 测试收款记录查询
            const 收款查询 = await supabase
                .from('payment_records')
                .select('*')
                .eq('sales_record_id', 新记录.databaseId);

            if (收款查询.error) {
                this.记录测试('新记录收款查询', 'FAIL', `查询失败: ${收款查询.error.message}`);
            } else {
                this.记录测试('新记录收款查询', 'PASS', `新记录收款查询成功，找到 ${收款查询.data.length} 条记录`);
            }

            // 测试添加收款记录
            const 测试收款 = {
                sales_record_id: 新记录.databaseId,
                payment_date: new Date().toISOString().split('T')[0],
                amount: 1000,
                payment_method: '银行转账',
                notes: '测试收款记录'
            };

            const 添加收款结果 = await supabase
                .from('payment_records')
                .insert([测试收款]);

            if (添加收款结果.error) {
                this.记录测试('新记录添加收款', 'FAIL', `添加失败: ${添加收款结果.error.message}`);
            } else {
                this.记录测试('新记录添加收款', 'PASS', '成功为新记录添加收款');
                
                // 验证收款记录
                const 验证查询 = await supabase
                    .from('payment_records')
                    .select('*')
                    .eq('sales_record_id', 新记录.databaseId);
                    
                this.记录测试('收款记录验证', 'PASS', `验证成功，共 ${验证查询.data.length} 条收款记录`);
            }

        } catch (error) {
            this.记录测试('新记录收款功能异常', 'FAIL', error.message);
        }
    }

    // 7. 生成测试报告
    生成测试报告() {
        console.log('\n📋 ===== 测试报告 =====');
        
        const 通过数 = this.测试结果.filter(r => r.状态 === 'PASS').length;
        const 失败数 = this.测试结果.filter(r => r.状态 === 'FAIL').length;
        const 修复数 = this.测试结果.filter(r => r.状态 === 'FIXED').length;
        
        console.log(`总测试数: ${this.测试结果.length}`);
        console.log(`✅ 通过: ${通过数}`);
        console.log(`❌ 失败: ${失败数}`);
        console.log(`🔧 已修复: ${修复数}`);
        
        console.log('\n详细结果:');
        this.测试结果.forEach(结果 => {
            const 状态图标 = { 'PASS': '✅', 'FAIL': '❌', 'FIXED': '🔧' };
            console.log(`${状态图标[结果.状态]} ${结果.测试名称}: ${结果.详情}`);
        });
        
        if (this.修复记录.length > 0) {
            console.log('\n🔧 修复记录:');
            this.修复记录.forEach(修复 => {
                console.log(`- ${修复.描述}`);
            });
        }
        
        return {
            总数: this.测试结果.length,
            通过: 通过数,
            失败: 失败数,
            修复: 修复数,
            详细结果: this.测试结果,
            修复记录: this.修复记录
        };
    }

    // 主测试流程
    async 开始全面测试() {
        console.log('🚀 开始全面系统测试和修复...');
        console.log('=====================================');
        
        await this.测试基础环境();
        await this.测试数据加载();
        await this.测试收款记录功能();
        await this.测试新记录添加();
        
        const 报告 = this.生成测试报告();
        
        console.log('\n🎯 测试完成！');
        if (报告.失败 === 0) {
            console.log('🎉 所有功能正常！');
        } else {
            console.log(`⚠️ 发现 ${报告.失败} 个问题，已尝试修复 ${报告.修复} 个`);
        }
        
        return 报告;
    }
}

// 创建测试器实例并开始测试
const 测试器 = new 系统测试修复器();
console.log('🔧 全面测试修复脚本已加载');
console.log('运行命令: 测试器.开始全面测试()');

// 自动开始测试
测试器.开始全面测试();
