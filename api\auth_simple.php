<?php
// 简化版用户认证API - 使用数据库
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 简单的响应函数
function sendResponse($success, $message, $data = null) {
    $response = array(
        'success' => $success,
        'message' => $message
    );
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? 'test';

// 路由处理
switch ($action) {
    case 'test':
        sendResponse(true, 'API工作正常', array('timestamp' => date('Y-m-d H:i:s')));
        break;
        
    case 'login':
        // 数据库登录验证
        require_once 'config.php';
        
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        if (empty($email) || empty($password)) {
            sendResponse(false, '请输入邮箱和密码');
        }
        
        try {
            $stmt = $pdo->prepare("SELECT user_id, email, full_name, role, password FROM user_profiles WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && $user['password'] === $password) {
                sendResponse(true, '登录成功', array(
                    'user' => array(
                        'id' => $user['user_id'],
                        'email' => $user['email'],
                        'name' => $user['full_name'],
                        'role' => $user['role']
                    )
                ));
            } else {
                sendResponse(false, '邮箱或密码错误');
            }
            
        } catch (PDOException $e) {
            error_log('登录数据库错误: ' . $e->getMessage());
            sendResponse(false, '登录失败，请稍后重试');
        }
        break;
        
    case 'register':
        // 数据库注册功能
        require_once 'config.php';
        
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $name = $_POST['name'] ?? '';
        
        if (empty($email) || empty($password) || empty($name)) {
            sendResponse(false, '请填写完整信息');
        }
        
        try {
            // 检查邮箱是否已被注册
            $stmt = $pdo->prepare("SELECT id FROM user_profiles WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                sendResponse(false, '该邮箱已被注册');
            }
            
            // 生成用户ID
            $userId = 'user' . date('YmdHis') . rand(100, 999);
            
            // 插入新用户 - 注册默认为销售人员
            $stmt = $pdo->prepare("INSERT INTO user_profiles (user_id, email, full_name, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, 'sales', NOW(), NOW())");
            $result = $stmt->execute([$userId, $email, $name, $password]);
            
            if ($result) {
                sendResponse(true, '注册成功', array(
                    'user' => array(
                        'id' => $userId,
                        'email' => $email,
                        'name' => $name,
                        'role' => 'user'
                    )
                ));
            } else {
                sendResponse(false, '注册失败，请稍后重试');
            }
            
        } catch (PDOException $e) {
            error_log('注册数据库错误: ' . $e->getMessage());
            sendResponse(false, '注册失败: ' . $e->getMessage());
        }
        break;
        
    default:
        sendResponse(false, '未知操作');
}
?>

