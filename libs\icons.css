/* SVG图标系统样式 */
.icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
    vertical-align: middle;
}

/* 图标大小变体 */
.icon-xs { width: 0.75em; height: 0.75em; }
.icon-sm { width: 1em; height: 1em; }
.icon-md { width: 1.5em; height: 1.5em; }
.icon-lg { width: 2em; height: 2em; }
.icon-xl { width: 3em; height: 3em; }

/* 按钮中的图标 */
.btn .icon {
    margin-right: 0.5em;
    margin-left: -0.25em;
}

.btn .icon:only-child {
    margin: 0;
}

/* KPI图标样式 */
.kpi-icon-svg {
    width: 2.5rem;
    height: 2.5rem;
    color: #fff;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

/* 卡片图标样式 */
.card-icon-svg {
    width: 1.5rem;
    height: 1.5rem;
    color: #667eea;
    margin-right: 0.5rem;
}

/* 状态图标颜色 */
.icon-success { color: #28a745; }
.icon-warning { color: #ffc107; }
.icon-error { color: #dc3545; }
.icon-info { color: #17a2b8; }
.icon-primary { color: #007bff; }

/* 动画效果 */
.icon-spin {
    animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.icon-pulse {
    animation: icon-pulse 1.5s ease-in-out infinite;
}

@keyframes icon-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 响应式图标 */
@media (max-width: 768px) {
    .icon-responsive {
        width: 1.2em;
        height: 1.2em;
    }
} 