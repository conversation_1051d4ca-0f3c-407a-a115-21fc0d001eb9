-- =====================================================
-- 数据库升级回滚脚本
-- 用于回滚分期收款功能升级
-- 警告：此操作将删除所有收款记录数据！
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 1. 备份当前payment_records数据（可选）
-- 如果需要保留数据，请先执行以下语句创建备份表
-- CREATE TABLE payment_records_backup AS SELECT * FROM payment_records;

-- 2. 删除触发器
DROP TRIGGER IF EXISTS update_sales_record_payment_stats;
DROP TRIGGER IF EXISTS update_sales_record_payment_stats_on_update;
DROP TRIGGER IF EXISTS update_sales_record_payment_stats_on_delete;

-- 3. 删除视图
DROP VIEW IF EXISTS payment_details_view;
DROP VIEW IF EXISTS payment_summary;

-- 4. 删除payment_records表
DROP TABLE IF EXISTS payment_records;

-- 5. 删除sales_records表的新增字段
ALTER TABLE sales_records 
DROP COLUMN IF EXISTS total_received,
DROP COLUMN IF EXISTS payment_status,
DROP COLUMN IF EXISTS last_payment_date,
DROP COLUMN IF EXISTS payment_count;

-- 6. 删除新增的索引
DROP INDEX IF EXISTS idx_sales_records_payment_status ON sales_records;
DROP INDEX IF EXISTS idx_sales_records_contract_amount ON sales_records;
DROP INDEX IF EXISTS idx_sales_records_total_received ON sales_records;

-- 提交事务
COMMIT;

-- 验证回滚结果
SELECT '数据库已回滚到升级前状态' as message;

-- 检查表结构是否恢复
SELECT 
    table_name,
    CASE 
        WHEN table_name = 'sales_records' THEN '✅ 保留'
        WHEN table_name = 'payment_records' THEN '❌ 应该已删除'
        ELSE '其他表'
    END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('sales_records', 'payment_records');

-- 检查sales_records字段是否恢复
SELECT 
    column_name,
    CASE 
        WHEN column_name IN ('total_received', 'payment_status', 'last_payment_date', 'payment_count') 
        THEN '❌ 应该已删除'
        ELSE '✅ 正常字段'
    END as status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'sales_records'
ORDER BY ordinal_position;
