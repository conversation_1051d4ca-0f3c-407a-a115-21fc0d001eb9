// =====================================================
// 测试自动收款记录功能
// 在浏览器控制台中运行此脚本
// =====================================================

console.log('🧪 启动自动收款记录测试...');

async function 测试自动收款记录功能() {
    console.log('\n🔍 === 测试自动收款记录功能 ===');
    
    // 1. 检查当前用户和Supabase状态
    if (!isSupabaseReady || !currentUser) {
        console.log('❌ Supabase未就绪或用户未登录');
        return false;
    }
    
    console.log('✅ Supabase就绪，当前用户:', currentUser.email);
    
    // 2. 创建测试销售记录数据
    const 测试时间戳 = Date.now();
    const 测试记录 = {
        id: 测试时间戳,
        date: new Date().toISOString().split('T')[0],
        customer: `测试客户_${测试时间戳}`,
        customerSource: '客户转介绍',
        businessType: '其他',
        incomeType: '陪跑收款',
        salesperson: '庞锦媚',
        amount: 5000, // 🔑 关键：有收款金额
        contractAmount: 20000,
        remarks: '测试自动收款记录功能'
    };
    
    console.log('📝 测试记录数据:', 测试记录);
    
    try {
        // 3. 调用保存函数
        console.log('💾 调用saveSalesRecordToDatabase...');
        const 数据库ID = await saveSalesRecordToDatabase(测试记录);
        
        if (!数据库ID || typeof 数据库ID !== 'string') {
            console.log('❌ 保存销售记录失败，未返回有效的数据库ID');
            return false;
        }
        
        console.log('✅ 销售记录保存成功，数据库ID:', 数据库ID);
        
        // 4. 等待一下，然后查询收款记录
        console.log('⏳ 等待2秒后查询收款记录...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const 收款查询 = await supabase
            .from('payment_records')
            .select('*')
            .eq('sales_record_id', 数据库ID);
        
        if (收款查询.error) {
            console.log('❌ 查询收款记录失败:', 收款查询.error.message);
            return false;
        }
        
        console.log('📊 收款记录查询结果:', 收款查询.data);
        
        if (收款查询.data.length === 0) {
            console.log('❌ 未找到自动创建的收款记录');
            return false;
        }
        
        if (收款查询.data.length === 1) {
            const 收款记录 = 收款查询.data[0];
            console.log('✅ 找到自动创建的收款记录:', {
                收款日期: 收款记录.payment_date,
                收款金额: 收款记录.amount,
                收款方式: 收款记录.payment_method,
                备注: 收款记录.notes
            });
            
            // 验证收款记录的数据是否正确
            if (收款记录.amount === 测试记录.amount) {
                console.log('✅ 收款金额正确');
            } else {
                console.log('❌ 收款金额不正确');
            }
            
            if (收款记录.payment_date === 测试记录.date) {
                console.log('✅ 收款日期正确');
            } else {
                console.log('❌ 收款日期不正确');
            }
            
            if (收款记录.notes === '销售时收款') {
                console.log('✅ 收款备注正确');
            } else {
                console.log('❌ 收款备注不正确');
            }
        } else {
            console.log('⚠️ 找到多条收款记录，可能有重复');
        }
        
        // 5. 测试收款弹窗功能
        console.log('\n🔍 测试收款弹窗功能...');
        
        // 先将测试记录添加到salesData
        salesData.push({
            ...测试记录,
            databaseId: 数据库ID
        });
        
        // 调用收款弹窗
        await openPaymentModal(测试记录.id);
        
        // 检查弹窗是否正确显示收款记录
        const 弹窗 = document.getElementById('paymentModal');
        if (弹窗 && 弹窗.style.display === 'block') {
            console.log('✅ 收款弹窗已打开');
            
            // 检查收款记录表格
            const 表格体 = document.getElementById('paymentRecordsBody');
            if (表格体) {
                const 行数 = 表格体.children.length;
                console.log(`📊 收款记录表格显示 ${行数} 行`);
                
                if (行数 > 0) {
                    const 第一行 = 表格体.children[0];
                    if (第一行.textContent.includes('5,000')) {
                        console.log('✅ 收款记录在表格中正确显示');
                    } else {
                        console.log('❌ 收款记录在表格中显示不正确');
                    }
                }
            }
            
            // 检查汇总信息
            const 合同金额元素 = document.getElementById('contractAmount');
            const 累计收款元素 = document.querySelector('.payment-summary .summary-item:nth-child(1) .summary-value');
            
            if (合同金额元素 && 合同金额元素.textContent.includes('20,000')) {
                console.log('✅ 合同金额显示正确');
            }
            
            if (累计收款元素 && 累计收款元素.textContent.includes('5,000')) {
                console.log('✅ 累计收款显示正确');
            }
        }
        
        console.log('\n🎉 === 自动收款记录功能测试完成 ===');
        console.log('✅ 测试通过！新的销售记录会自动创建对应的收款记录');
        
        return true;
        
    } catch (error) {
        console.log('❌ 测试过程中发生异常:', error.message);
        return false;
    }
}

// 清理测试数据的函数
async function 清理测试数据() {
    console.log('\n🧹 === 清理测试数据 ===');
    
    try {
        // 查找测试记录
        const 测试记录查询 = await supabase
            .from('sales_records')
            .select('id')
            .like('customer', '测试客户_%');
        
        if (测试记录查询.error) {
            console.log('❌ 查询测试记录失败:', 测试记录查询.error.message);
            return;
        }
        
        if (测试记录查询.data.length === 0) {
            console.log('✅ 没有需要清理的测试记录');
            return;
        }
        
        console.log(`🔍 找到 ${测试记录查询.data.length} 条测试记录需要清理`);
        
        for (const 记录 of 测试记录查询.data) {
            // 先删除收款记录
            const 删除收款 = await supabase
                .from('payment_records')
                .delete()
                .eq('sales_record_id', 记录.id);
            
            if (删除收款.error) {
                console.log('❌ 删除收款记录失败:', 删除收款.error.message);
            } else {
                console.log('✅ 已删除收款记录');
            }
            
            // 再删除销售记录
            const 删除销售 = await supabase
                .from('sales_records')
                .delete()
                .eq('id', 记录.id);
            
            if (删除销售.error) {
                console.log('❌ 删除销售记录失败:', 删除销售.error.message);
            } else {
                console.log('✅ 已删除销售记录');
            }
        }
        
        // 清理本地salesData中的测试记录
        const 原始长度 = salesData.length;
        salesData = salesData.filter(r => !r.customer.startsWith('测试客户_'));
        localStorage.setItem('salesData', JSON.stringify(salesData));
        
        console.log(`✅ 已从本地数据中清理 ${原始长度 - salesData.length} 条测试记录`);
        
        console.log('🎉 测试数据清理完成');
        
    } catch (error) {
        console.log('❌ 清理测试数据异常:', error.message);
    }
}

// 导出函数到全局
window.测试自动收款记录功能 = 测试自动收款记录功能;
window.清理测试数据 = 清理测试数据;

console.log('\n🎯 测试脚本已加载！');
console.log('可用命令:');
console.log('  测试自动收款记录功能()  - 测试新记录是否自动创建收款记录');
console.log('  清理测试数据()          - 清理测试过程中创建的数据');
console.log('\n推荐执行: 测试自动收款记录功能()');

// 自动开始测试
测试自动收款记录功能();
