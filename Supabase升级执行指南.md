# Supabase数据库升级执行指南 - 分期收款功能

## 📋 升级概述

本次升级将为您的Supabase销售管理系统添加分期收款功能，支持：
- ✅ 一个合同多次收款记录
- ✅ 自动计算收款状态（未付款/部分付款/已付清/超额付款）
- ✅ 详细的收款明细追踪
- ✅ RLS（行级安全）权限控制
- ✅ 实时数据同步

## ⚠️ 重要提醒

**在执行升级前，请务必：**
1. **备份数据库** - 在Supabase控制台创建备份
2. **在测试环境先执行** - 确保升级脚本正常工作
3. **通知相关人员** - 升级期间系统可能短暂不可用
4. **准备回滚方案** - 如果出现问题可以快速恢复

## 🔧 执行步骤

### 步骤1：Supabase数据库备份
```bash
# 方法1：使用Supabase CLI
supabase db dump --db-url "your-database-url" > backup_$(date +%Y%m%d_%H%M%S).sql

# 方法2：在Supabase控制台
# 1. 登录 https://supabase.com/dashboard
# 2. 选择您的项目
# 3. 进入 Settings > Database
# 4. 点击 "Create backup" 按钮
```

### 步骤2：执行升级脚本
```bash
# 方法1：使用Supabase SQL编辑器（推荐）
# 1. 登录Supabase控制台
# 2. 进入SQL编辑器
# 3. 复制supabase_upgrade_partial_payments.sql内容
# 4. 点击"Run"执行

# 方法2：使用psql命令行
psql "your-supabase-database-url" < supabase_upgrade_partial_payments.sql
```

### 步骤3：验证升级结果
```bash
# 在Supabase SQL编辑器中执行验证脚本
# 复制verify_supabase_upgrade.sql内容并运行

# 检查关键指标：
# - payment_records表是否创建成功
# - sales_records新字段是否添加
# - 视图和触发器是否正常工作
# - RLS策略是否正确配置
# - 历史数据是否正确迁移
```

### 步骤4：测试功能
在升级完成后，建议测试以下功能：
1. 添加新的销售记录
2. 为现有合同添加收款记录
3. 查看收款统计和分析
4. 验证RLS权限控制是否正常

## 📊 升级内容详解

### 新增表结构

#### 1. payment_records（收款记录表）
```sql
- id: UUID (主键)
- sales_record_id: UUID (外键，关联sales_records)
- payment_date: DATE (收款日期)
- payment_amount: DECIMAL(12,2) (收款金额)
- payment_method: TEXT (收款方式)
- payment_status: TEXT (收款状态)
- bank_info: TEXT (银行信息)
- transaction_no: TEXT (交易流水号)
- remarks: TEXT (收款备注)
- created_by: TEXT (创建人)
- created_at/updated_at: TIMESTAMPTZ (时间戳)
- is_deleted: BOOLEAN (软删除标记)
```

#### 2. sales_records新增字段
```sql
- total_received: DECIMAL(12,2) (累计收款金额)
- payment_status: TEXT (付款状态)
- last_payment_date: DATE (最后收款日期)
- payment_count: INTEGER (收款次数)
```

### 新增视图

#### 1. payment_summary
- **用途**：提供每个销售记录的收款汇总信息
- **包含**：合同金额、累计收款、剩余金额、付款状态等

#### 2. payment_details_view
- **用途**：提供详细的收款记录视图
- **包含**：每笔收款的完整信息和累计计算

### 自动化功能

#### PostgreSQL函数和触发器
- `update_sales_record_payment_stats()` - 自动更新统计字段
- 触发器自动在收款记录变化时重新计算
- `update_updated_at_column()` - 自动更新时间戳

#### RLS（行级安全）策略
- **查看权限**：用户只能查看自己创建的记录，管理员可查看所有
- **插入权限**：用户只能插入自己的收款记录
- **更新权限**：用户只能更新自己的记录，管理员和财务可更新所有
- **删除权限**：只有管理员可以删除收款记录

## 🔄 数据迁移说明

升级脚本会自动将现有的`amount`字段数据迁移到`payment_records`表：
- 每个现有销售记录的`amount`值会作为一笔收款记录
- 收款日期使用原记录的日期
- 收款方式标记为"历史数据迁移"
- 保留原有的备注信息
- 自动触发统计字段更新

## 📈 业务影响

### 对销售人员的影响
- ✅ 可以为同一个客户/合同添加多次收款记录
- ✅ 清晰看到每笔收款的时间和金额
- ✅ 自动计算剩余应收金额
- ✅ 实时同步收款状态

### 对财务人员的影响
- ✅ 详细的收款流水记录
- ✅ 按日期查看收款情况
- ✅ 更准确的应收账款统计
- ✅ 银行信息和交易流水号记录

### 对管理层的影响
- ✅ 更精确的收款分析
- ✅ 客户付款行为分析
- ✅ 现金流预测改进
- ✅ 权限控制和数据安全

## 🚨 故障排除

### 常见问题及解决方案

#### 1. RLS策略错误
**问题**：权限策略配置失败
**解决**：检查user_profiles表是否存在，确保auth.uid()和auth.email()函数可用

#### 2. 触发器创建失败
**问题**：PostgreSQL函数语法错误
**解决**：确保使用PostgreSQL 12+版本，检查函数语法

#### 3. 数据迁移不完整
**问题**：部分历史数据未迁移
**解决**：检查原数据中amount和contract_amount字段的值

#### 4. 视图查询缓慢
**问题**：payment_summary视图查询性能差
**解决**：确保相关索引已创建，考虑添加更多索引

### 回滚操作
如果升级出现严重问题，可以在Supabase SQL编辑器中执行：
```sql
-- 删除新增的表、视图、函数
DROP TABLE IF EXISTS payment_records CASCADE;
DROP VIEW IF EXISTS payment_summary CASCADE;
DROP VIEW IF EXISTS payment_details_view CASCADE;
DROP FUNCTION IF EXISTS update_sales_record_payment_stats() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- 删除新增字段
ALTER TABLE sales_records 
DROP COLUMN IF EXISTS total_received,
DROP COLUMN IF EXISTS payment_status,
DROP COLUMN IF EXISTS last_payment_date,
DROP COLUMN IF EXISTS payment_count;
```

## 🔗 前端集成

升级完成后，您需要更新JavaScript代码来支持新功能：

### 1. 添加收款记录函数
```javascript
async function addPaymentRecord(salesRecordId, paymentData) {
    const { data, error } = await supabase
        .from('payment_records')
        .insert([{
            sales_record_id: salesRecordId,
            payment_date: paymentData.date,
            payment_amount: paymentData.amount,
            payment_method: paymentData.method,
            bank_info: paymentData.bankInfo,
            transaction_no: paymentData.transactionNo,
            remarks: paymentData.remarks,
            created_by: currentUser.email
        }]);
    
    if (error) throw error;
    return data;
}
```

### 2. 查询收款记录
```javascript
async function getPaymentRecords(salesRecordId) {
    const { data, error } = await supabase
        .from('payment_details_view')
        .select('*')
        .eq('sales_record_id', salesRecordId)
        .order('payment_date', { ascending: true });
    
    if (error) throw error;
    return data;
}
```

### 3. 查询收款汇总
```javascript
async function getPaymentSummary() {
    const { data, error } = await supabase
        .from('payment_summary')
        .select('*')
        .order('contract_date', { ascending: false });
    
    if (error) throw error;
    return data;
}
```

## ✅ 升级检查清单

升级完成后，请确认以下项目：

- [ ] Supabase数据库备份已完成
- [ ] payment_records表创建成功
- [ ] sales_records新字段添加成功
- [ ] payment_summary视图可正常查询
- [ ] payment_details_view视图可正常查询
- [ ] PostgreSQL函数和触发器正常工作
- [ ] RLS策略配置正确
- [ ] 历史数据迁移完整
- [ ] 索引创建成功
- [ ] 验证脚本执行无错误
- [ ] 前端功能测试通过
- [ ] 用户权限正常
- [ ] 实时同步功能正常

## 📅 后续工作

数据库升级完成后，还需要：
1. **更新前端界面** - 添加分期收款录入功能
2. **更新JavaScript代码** - 支持收款记录的增删改查
3. **更新统计分析** - 使用新的付款状态进行分析
4. **测试实时同步** - 验证Supabase实时功能
5. **用户培训** - 向销售和财务人员介绍新功能
6. **文档更新** - 更新系统使用手册

---

**升级完成时间**：_____________  
**执行人员**：_____________  
**验证人员**：_____________  
**备注**：_____________
