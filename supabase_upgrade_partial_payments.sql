-- =====================================================
-- Supabase数据库升级脚本：支持分期收款功能
-- 版本：v2.0 - Supabase PostgreSQL版本
-- 创建日期：2025-06-30
-- 说明：为销售管理系统添加分期收款支持
-- =====================================================

-- 1. 创建收款记录表（核心表）
CREATE TABLE IF NOT EXISTS payment_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_record_id UUID NOT NULL REFERENCES sales_records(id) ON DELETE CASCADE,
    payment_date DATE NOT NULL,
    payment_amount DECIMAL(12,2) NOT NULL,
    payment_method TEXT DEFAULT '转账',
    payment_status TEXT DEFAULT 'confirmed' CHECK (payment_status IN ('pending', 'confirmed', 'cancelled')),
    bank_info TEXT,
    transaction_no TEXT,
    remarks TEXT,
    created_by TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_payment_records_sales_record_id ON payment_records(sales_record_id);
CREATE INDEX IF NOT EXISTS idx_payment_records_payment_date ON payment_records(payment_date);
CREATE INDEX IF NOT EXISTS idx_payment_records_created_by ON payment_records(created_by);
CREATE INDEX IF NOT EXISTS idx_payment_records_payment_status ON payment_records(payment_status);
CREATE INDEX IF NOT EXISTS idx_payment_records_is_deleted ON payment_records(is_deleted);

-- 2. 为sales_records表添加新字段
ALTER TABLE sales_records 
ADD COLUMN IF NOT EXISTS total_received DECIMAL(12,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS payment_status TEXT DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'overpaid')),
ADD COLUMN IF NOT EXISTS last_payment_date DATE,
ADD COLUMN IF NOT EXISTS payment_count INTEGER DEFAULT 0;

-- 3. 创建收款统计视图
CREATE OR REPLACE VIEW payment_summary AS
SELECT 
    sr.id as sales_record_id,
    sr.frontend_id,
    sr.customer,
    sr.date as contract_date,
    sr.contract_amount,
    COALESCE(SUM(pr.payment_amount), 0) as total_received,
    COUNT(pr.id) as payment_count,
    MAX(pr.payment_date) as last_payment_date,
    (sr.contract_amount - COALESCE(SUM(pr.payment_amount), 0)) as remaining_amount,
    CASE 
        WHEN COALESCE(SUM(pr.payment_amount), 0) = 0 THEN 'unpaid'
        WHEN COALESCE(SUM(pr.payment_amount), 0) < sr.contract_amount THEN 'partial'
        WHEN COALESCE(SUM(pr.payment_amount), 0) = sr.contract_amount THEN 'paid'
        ELSE 'overpaid'
    END as payment_status,
    sr.created_by,
    sr.created_at
FROM sales_records sr
LEFT JOIN payment_records pr ON sr.id = pr.sales_record_id AND pr.is_deleted = FALSE
WHERE sr.contract_amount > 0
GROUP BY sr.id, sr.frontend_id, sr.customer, sr.date, sr.contract_amount, sr.created_by, sr.created_at;

-- 4. 创建详细收款记录视图
CREATE OR REPLACE VIEW payment_details_view AS
SELECT 
    pr.id as payment_id,
    pr.sales_record_id,
    sr.frontend_id,
    sr.customer,
    sr.contract_amount,
    sr.date as contract_date,
    pr.payment_date,
    pr.payment_amount,
    pr.payment_method,
    pr.payment_status,
    pr.bank_info,
    pr.transaction_no,
    pr.remarks as payment_remarks,
    sr.remarks as contract_remarks,
    pr.created_by as payment_created_by,
    pr.created_at as payment_created_at,
    -- 计算累计收款
    (SELECT COALESCE(SUM(p2.payment_amount), 0) 
     FROM payment_records p2 
     WHERE p2.sales_record_id = pr.sales_record_id 
     AND p2.payment_date <= pr.payment_date 
     AND p2.is_deleted = FALSE) as cumulative_received,
    -- 计算剩余金额
    (sr.contract_amount - (SELECT COALESCE(SUM(p3.payment_amount), 0) 
                          FROM payment_records p3 
                          WHERE p3.sales_record_id = pr.sales_record_id 
                          AND p3.payment_date <= pr.payment_date 
                          AND p3.is_deleted = FALSE)) as remaining_after_payment
FROM payment_records pr
JOIN sales_records sr ON pr.sales_record_id = sr.id
WHERE pr.is_deleted = FALSE
ORDER BY pr.sales_record_id, pr.payment_date;

-- 5. 创建函数：更新sales_records统计字段
CREATE OR REPLACE FUNCTION update_sales_record_payment_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新相关的sales_record统计信息
    UPDATE sales_records 
    SET 
        total_received = (
            SELECT COALESCE(SUM(payment_amount), 0) 
            FROM payment_records 
            WHERE sales_record_id = COALESCE(NEW.sales_record_id, OLD.sales_record_id) 
            AND is_deleted = FALSE
        ),
        payment_count = (
            SELECT COUNT(*) 
            FROM payment_records 
            WHERE sales_record_id = COALESCE(NEW.sales_record_id, OLD.sales_record_id) 
            AND is_deleted = FALSE
        ),
        last_payment_date = (
            SELECT MAX(payment_date) 
            FROM payment_records 
            WHERE sales_record_id = COALESCE(NEW.sales_record_id, OLD.sales_record_id) 
            AND is_deleted = FALSE
        ),
        updated_at = NOW()
    WHERE id = COALESCE(NEW.sales_record_id, OLD.sales_record_id);
    
    -- 更新付款状态
    UPDATE sales_records 
    SET payment_status = CASE 
        WHEN total_received = 0 THEN 'unpaid'
        WHEN total_received < contract_amount THEN 'partial'
        WHEN total_received = contract_amount THEN 'paid'
        ELSE 'overpaid'
    END,
    updated_at = NOW()
    WHERE id = COALESCE(NEW.sales_record_id, OLD.sales_record_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 6. 创建触发器
DROP TRIGGER IF EXISTS trigger_update_payment_stats_insert ON payment_records;
CREATE TRIGGER trigger_update_payment_stats_insert
    AFTER INSERT ON payment_records
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_record_payment_stats();

DROP TRIGGER IF EXISTS trigger_update_payment_stats_update ON payment_records;
CREATE TRIGGER trigger_update_payment_stats_update
    AFTER UPDATE ON payment_records
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_record_payment_stats();

DROP TRIGGER IF EXISTS trigger_update_payment_stats_delete ON payment_records;
CREATE TRIGGER trigger_update_payment_stats_delete
    AFTER DELETE ON payment_records
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_record_payment_stats();

-- 7. 创建updated_at自动更新触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_payment_records_updated_at ON payment_records;
CREATE TRIGGER trigger_payment_records_updated_at
    BEFORE UPDATE ON payment_records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 8. 设置RLS（行级安全）策略
ALTER TABLE payment_records ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略：用户只能查看自己创建的收款记录或管理员可以查看所有
CREATE POLICY "payment_records_select_policy" ON payment_records
    FOR SELECT USING (
        created_by = auth.email() OR 
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'finance', 'manager')
        )
    );

-- 创建RLS策略：用户只能插入自己的收款记录
CREATE POLICY "payment_records_insert_policy" ON payment_records
    FOR INSERT WITH CHECK (created_by = auth.email());

-- 创建RLS策略：用户只能更新自己创建的收款记录或管理员可以更新所有
CREATE POLICY "payment_records_update_policy" ON payment_records
    FOR UPDATE USING (
        created_by = auth.email() OR 
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'finance')
        )
    );

-- 创建RLS策略：只有管理员可以删除收款记录
CREATE POLICY "payment_records_delete_policy" ON payment_records
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_id = auth.uid() 
            AND role = 'admin'
        )
    );

-- 9. 数据迁移：将现有的amount字段数据迁移到payment_records表
INSERT INTO payment_records (
    sales_record_id, 
    payment_date, 
    payment_amount, 
    payment_method, 
    payment_status,
    remarks, 
    created_by, 
    created_at
)
SELECT 
    id,
    date,
    amount,
    '历史数据迁移',
    'confirmed',
    CONCAT('从原amount字段迁移: ', COALESCE(remarks, '')),
    created_by,
    created_at
FROM sales_records 
WHERE amount > 0 AND contract_amount > 0;

-- 10. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_sales_records_payment_status ON sales_records(payment_status);
CREATE INDEX IF NOT EXISTS idx_sales_records_contract_amount ON sales_records(contract_amount);
CREATE INDEX IF NOT EXISTS idx_sales_records_total_received ON sales_records(total_received);

-- 显示升级完成信息
SELECT '✅ Supabase数据库升级完成！支持分期收款功能已启用。' as message;

-- 验证升级结果
SELECT 
    COUNT(*) as total_sales_records,
    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_records,
    SUM(CASE WHEN payment_status = 'partial' THEN 1 ELSE 0 END) as partial_records,
    SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_records
FROM sales_records;

SELECT COUNT(*) as total_payment_records FROM payment_records WHERE is_deleted = FALSE;
