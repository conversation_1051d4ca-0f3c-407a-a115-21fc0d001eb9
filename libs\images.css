/* 优化的图片资源 - Base64编码，无需额外HTTP请求 */

/* 加载动画背景 */
.loading-spinner {
    width: 40px;
    height: 40px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzY2N2VlYTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNzY0YmEyO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMTgiIGZpbGw9Im5vbmUiIHN0cm9rZT0idXJsKCNncmFkaWVudCkiIHN0cm9rZS13aWR0aD0iNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtZGFzaGFycmF5PSI4MCIgc3Ryb2tlLWRhc2hvZmZzZXQ9IjgwIj4KICAgIDxhbmltYXRlVHJhbnNmb3JtIGF0dHJpYnV0ZU5hbWU9InRyYW5zZm9ybSIgdHlwZT0icm90YXRlIiBkdXI9IjJzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgdmFsdWVzPSIwIDIwIDIwOzM2MCAyMCAyMCIvPgogIDwvY2lyY2xlPgo8L3N2Zz4=');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

/* 成功状态图标 */
.status-success::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsPSIjMjhhNzQ1IiBkPSJNMTMuODU0IDMuNjQ2YS41LjUgMCAwIDEtLjcwOC0uNzA4bC0xMC01YS41LjUgMCAwIDEgLjcwOC43MDhMMTMuODU0IDMuNjQ2eiIvPgogIDxwYXRoIGZpbGw9IiMyOGE3NDUiIGQ9Im0yLjUgNy41IDMgMyA3LTciIHN0cm9rZT0iIzI4YTc0NSIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPg==');
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 4px;
}

/* 错误状态图标 */
.status-error::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsPSIjZGMzNTQ1IiBkPSJNOCAwQzMuNTggMCAwIDMuNTggMCA4czMuNTggOCA4IDggOC0zLjU4IDgtOFMxMi40MiAwIDggMHptMCAxNGMtMy4zMSAwLTYtMi42OS02LTZzMi42OS02IDYtNiA2IDIuNjkgNiA2LTIuNjkgNi02IDZ6bTMuNS00LjVMMTAuNSA4bDEtMWMuMjgtLjI4LjI4LS43MiAwLTEtLjI4LS4yOC0uNzItLjI4LTEgMGwtMSAxLTEtMWMtLjI4LS4yOC0uNzItLjI4LTEgMC0uMjguMjgtLjI4LjcyIDAgMWwxIDEtMSAxYy0uMjguMjgtLjI4LjcyIDAgMSAuMjguMjguNzIuMjggMSAwbDEtMSAxIDFjLjI4LjI4LjcyLjI4IDEgMCAuMjgtLjI4LjI4LS43MiAwLTF6Ii8+Cjwvc3ZnPg==');
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 4px;
}

/* 警告状态图标 */
.status-warning::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsPSIjZmZjMTA3IiBkPSJNOC45ODIgMS41NjZhMS4xMyAxLjEzIDAgMCAwLTEuOTY0IDBMtjE3IDE0YTEuMTMgMS4xMyAwIDAgMCAuOTgyIDEuNzVoMTAuNDM2YTEuMTMgMS4xMyAwIDAgMCAuOTgyLTEuNzVMOC45ODIgMS41NjZ6TTggNWMuNTUgMCAxIC40NSAxIDFzLS40NSAxLTEgMS0xLS40NS0xLTEgLjQ1LTEgMS0xem0wIDljLS41NSAwLTEtLjQ1LTEtMVY5YzAtLjU1LjQ1LTEgMS0xczEgLjQ1IDEgMXY0YzAgLjU1LS40NSAxLTEgMXoiLz4KPC9zdmc+');
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 4px;
}

/* 信息状态图标 */
.status-info::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsPSIjMTdhMmI4IiBkPSJNOCAwQzMuNTggMCAwIDMuNTggMCA4czMuNTggOCA4IDggOC0zLjU4IDgtOFMxMi40MiAwIDggMHptMCAxNGMtMy4zMSAwLTYtMi42OS02LTZzMi42OS02IDYtNiA2IDIuNjkgNiA2LTIuNjkgNi02IDZ6bTAgNmMuNTUgMCAxLS40NSAxLTFWOWMwLS41NS0uNDUtMS0xLTFzLTEgLjQ1LTEgMXY0YzAgLjU1LjQ1IDEgMSAxem0wLTljLjU1IDAgMS0uNDUgMS0xcy0uNDUtMS0xLTEtMSAuNDUtMSAxIC40NSAxIDEgMXoiLz4KPC9zdmc+');
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 4px;
}

/* 渐变背景图案 */
.gradient-bg-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.gradient-bg-3 {
    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
}

.gradient-bg-4 {
    background: linear-gradient(135deg, #ff9500 0%, #ff9f0a 100%);
}

/* 图案背景 */
.pattern-dots {
    background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

.pattern-grid {
    background-image: 
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* 无图片时的占位符 */
.image-placeholder {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 14px;
}

/* 卡片装饰图案 */
.card-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJjYXJkUGF0dGVybiIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIj4KICAgICAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJub25lIi8+CiAgICAgIDxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjMiIGZpbGw9InJnYmEoMjU1LDI1NSwyNTUsMC4wNSkiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9InVybCgjY2FyZFBhdHRlcm4pIi8+Cjwvc3ZnPg==');
    opacity: 0.3;
    pointer-events: none;
}

/* 响应式图片 */
.img-responsive {
    max-width: 100%;
    height: auto;
    display: block;
}

/* 图片懒加载占位 */
.img-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 图标按钮背景 */
.icon-btn {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    padding: 8px;
    transition: all 0.3s ease;
}

.icon-btn:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

/* 打印时隐藏装饰图案 */
@media print {
    .pattern-dots,
    .pattern-grid,
    .card-pattern::before {
        display: none;
    }
} 