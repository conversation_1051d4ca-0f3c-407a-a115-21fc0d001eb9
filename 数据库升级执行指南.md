# 数据库升级执行指南 - 分期收款功能

## 📋 升级概述

本次升级将为销售管理系统添加分期收款功能，支持：
- 一个合同多次收款记录
- 自动计算收款状态（未付款/部分付款/已付清/超额付款）
- 详细的收款明细追踪
- 财务和销售人员的收款历史查看

## ⚠️ 重要提醒

**在执行升级前，请务必：**
1. **备份数据库** - 这是最重要的安全措施
2. **在测试环境先执行** - 确保升级脚本正常工作
3. **通知相关人员** - 升级期间系统可能短暂不可用
4. **准备回滚方案** - 如果出现问题可以快速恢复

## 🔧 执行步骤

### 步骤1：数据库备份
```bash
# 使用mysqldump备份整个数据库
mysqldump -u [用户名] -p[密码] sales_management > backup_$(date +%Y%m%d_%H%M%S).sql

# 或者在宝塔面板中：
# 1. 进入数据库管理
# 2. 选择sales_management数据库
# 3. 点击"备份"按钮
```

### 步骤2：执行升级脚本
```bash
# 方法1：命令行执行
mysql -u [用户名] -p[密码] sales_management < database_upgrade_partial_payments.sql

# 方法2：在宝塔面板phpMyAdmin中：
# 1. 选择sales_management数据库
# 2. 点击"SQL"标签
# 3. 复制database_upgrade_partial_payments.sql内容并执行
```

### 步骤3：验证升级结果
```bash
# 执行验证脚本
mysql -u [用户名] -p[密码] sales_management < verify_database_upgrade.sql

# 检查关键指标：
# - payment_records表是否创建成功
# - sales_records新字段是否添加
# - 视图和触发器是否正常工作
# - 历史数据是否正确迁移
```

### 步骤4：测试功能
在升级完成后，建议测试以下功能：
1. 添加新的销售记录
2. 为现有合同添加收款记录
3. 查看收款统计和分析
4. 验证权限控制是否正常

## 📊 升级内容详解

### 新增表结构

#### 1. payment_records（收款记录表）
- **用途**：记录每笔收款的详细信息
- **关键字段**：
  - `sales_record_id`：关联的销售记录
  - `payment_date`：收款日期
  - `payment_amount`：收款金额
  - `payment_method`：收款方式
  - `payment_status`：收款状态

#### 2. sales_records新增字段
- `total_received`：累计收款金额
- `payment_status`：付款状态（未付款/部分付款/已付清/超额付款）
- `last_payment_date`：最后收款日期
- `payment_count`：收款次数

### 新增视图

#### 1. payment_summary
- **用途**：提供每个销售记录的收款汇总信息
- **包含**：合同金额、累计收款、剩余金额、付款状态等

#### 2. payment_details_view
- **用途**：提供详细的收款记录视图
- **包含**：每笔收款的完整信息和累计计算

### 自动化功能

#### 触发器
- 自动更新sales_records表的统计字段
- 当添加、修改或删除收款记录时自动重新计算
- 确保数据一致性

## 🔄 数据迁移说明

升级脚本会自动将现有的`amount`字段数据迁移到`payment_records`表：
- 每个现有销售记录的`amount`值会作为一笔收款记录
- 收款日期使用原记录的日期
- 收款方式标记为"历史数据迁移"
- 保留原有的备注信息

## 📈 业务影响

### 对销售人员的影响
- ✅ 可以为同一个客户/合同添加多次收款记录
- ✅ 清晰看到每笔收款的时间和金额
- ✅ 自动计算剩余应收金额

### 对财务人员的影响
- ✅ 详细的收款流水记录
- ✅ 按日期查看收款情况
- ✅ 更准确的应收账款统计

### 对管理层的影响
- ✅ 更精确的收款分析
- ✅ 客户付款行为分析
- ✅ 现金流预测改进

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 外键约束错误
**问题**：创建payment_records表时外键约束失败
**解决**：检查sales_records表是否存在，确保引擎为InnoDB

#### 2. 触发器创建失败
**问题**：权限不足或语法错误
**解决**：确保数据库用户有CREATE TRIGGER权限

#### 3. 数据迁移不完整
**问题**：部分历史数据未迁移
**解决**：检查原数据中amount和contract_amount字段的值

#### 4. 视图查询缓慢
**问题**：payment_summary视图查询性能差
**解决**：确保相关索引已创建，考虑添加更多索引

### 回滚操作
如果升级出现严重问题，可以执行回滚：
```bash
# 执行回滚脚本
mysql -u [用户名] -p[密码] sales_management < rollback_database_upgrade.sql

# 然后恢复备份
mysql -u [用户名] -p[密码] sales_management < backup_[时间戳].sql
```

## 📞 技术支持

如果在升级过程中遇到问题，请：
1. 保存错误信息和日志
2. 不要继续执行其他操作
3. 联系技术支持团队
4. 准备提供数据库版本、错误信息等详细信息

## ✅ 升级检查清单

升级完成后，请确认以下项目：

- [ ] 数据库备份已完成
- [ ] payment_records表创建成功
- [ ] sales_records新字段添加成功
- [ ] payment_summary视图可正常查询
- [ ] payment_details_view视图可正常查询
- [ ] 触发器正常工作
- [ ] 历史数据迁移完整
- [ ] 索引创建成功
- [ ] 验证脚本执行无错误
- [ ] 前端功能测试通过
- [ ] 用户权限正常
- [ ] 性能测试通过

## 📅 后续工作

数据库升级完成后，还需要：
1. **更新前端界面** - 添加分期收款录入功能
2. **更新API接口** - 支持收款记录的增删改查
3. **更新统计分析** - 使用新的付款状态进行分析
4. **用户培训** - 向销售和财务人员介绍新功能
5. **文档更新** - 更新系统使用手册

---

**升级完成时间**：_____________  
**执行人员**：_____________  
**验证人员**：_____________  
**备注**：_____________
