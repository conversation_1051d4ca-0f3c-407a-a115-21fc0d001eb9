-- =====================================================
-- 简化的修复结果测试脚本
-- 避免列名错误，专注于核心检查
-- =====================================================

-- 1. 检查基础表是否有数据
SELECT '=== 基础表数据检查 ===' as info;

SELECT 'customer_sources' as table_name, COUNT(*) as total_count
FROM customer_sources WHERE is_active = true;

SELECT 'business_types' as table_name, COUNT(*) as total_count
FROM business_types WHERE is_active = true;

SELECT 'income_types' as table_name, COUNT(*) as total_count
FROM income_types WHERE is_active = true;

SELECT 'salespeople' as table_name, COUNT(*) as total_count
FROM salespeople WHERE is_active = true;

-- 2. 检查sales_records表的关联字段
SELECT '=== sales_records关联字段检查 ===' as info;

SELECT 
    COUNT(*) as total_records,
    COUNT(customer_source_id) as has_customer_source_id,
    COUNT(business_type_id) as has_business_type_id,
    COUNT(income_type_id) as has_income_type_id,
    COUNT(salesperson_id) as has_salesperson_id
FROM sales_records;

-- 3. 检查缺失的关联ID
SELECT 
    COUNT(CASE WHEN customer_source_id IS NULL THEN 1 END) as missing_customer_source_id,
    COUNT(CASE WHEN business_type_id IS NULL THEN 1 END) as missing_business_type_id,
    COUNT(CASE WHEN income_type_id IS NULL THEN 1 END) as missing_income_type_id,
    COUNT(CASE WHEN salesperson_id IS NULL THEN 1 END) as missing_salesperson_id
FROM sales_records;

-- 4. 显示sales_records的原始数据
SELECT '=== sales_records原始数据样本 ===' as info;

SELECT
    id,
    customer,
    customer_source_id,
    business_type_id,
    income_type_id,
    salesperson_id,
    amount,
    contract_amount
FROM sales_records
ORDER BY created_at DESC
LIMIT 5;

-- 5. 测试关联查询
SELECT '=== 关联查询测试 ===' as info;

SELECT
    sr.id,
    sr.customer,
    sr.customer_source_id,
    cs.name as linked_customer_source,
    sr.business_type_id,
    bt.name as linked_business_type,
    sr.income_type_id,
    it.name as linked_income_type,
    sr.salesperson_id,
    sp.name as linked_salesperson
FROM sales_records sr
LEFT JOIN customer_sources cs ON sr.customer_source_id = cs.id
LEFT JOIN business_types bt ON sr.business_type_id = bt.id
LEFT JOIN income_types it ON sr.income_type_id = it.id
LEFT JOIN salespeople sp ON sr.salesperson_id = sp.id
ORDER BY sr.created_at DESC
LIMIT 5;

-- 6. 检查哪些记录仍然有问题
SELECT '=== 问题记录检查 ===' as info;

SELECT 
    sr.id,
    sr.customer,
    CASE WHEN sr.customer_source_id IS NULL THEN '❌ 缺失ID' ELSE '✅ 有ID' END as customer_source_status,
    CASE WHEN sr.business_type_id IS NULL THEN '❌ 缺失ID' ELSE '✅ 有ID' END as business_type_status,
    CASE WHEN sr.income_type_id IS NULL THEN '❌ 缺失ID' ELSE '✅ 有ID' END as income_type_status,
    CASE WHEN sr.salesperson_id IS NULL THEN '❌ 缺失ID' ELSE '✅ 有ID' END as salesperson_status
FROM sales_records sr
WHERE sr.customer_source_id IS NULL 
   OR sr.business_type_id IS NULL 
   OR sr.income_type_id IS NULL 
   OR sr.salesperson_id IS NULL;

-- 7. 生成修复状态报告
SELECT '=== 修复状态报告 ===' as info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sales_records WHERE customer_source_id IS NULL) = 0 
        THEN '✅ 客户来源ID全部修复完成'
        ELSE '❌ 还有 ' || (SELECT COUNT(*) FROM sales_records WHERE customer_source_id IS NULL) || ' 条记录缺少客户来源ID'
    END as customer_source_fix_status;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sales_records WHERE business_type_id IS NULL) = 0 
        THEN '✅ 业务类型ID全部修复完成'
        ELSE '❌ 还有 ' || (SELECT COUNT(*) FROM sales_records WHERE business_type_id IS NULL) || ' 条记录缺少业务类型ID'
    END as business_type_fix_status;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sales_records WHERE income_type_id IS NULL) = 0 
        THEN '✅ 收入类型ID全部修复完成'
        ELSE '❌ 还有 ' || (SELECT COUNT(*) FROM sales_records WHERE income_type_id IS NULL) || ' 条记录缺少收入类型ID'
    END as income_type_fix_status;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sales_records WHERE salesperson_id IS NULL) = 0 
        THEN '✅ 销售人员ID全部修复完成'
        ELSE '❌ 还有 ' || (SELECT COUNT(*) FROM sales_records WHERE salesperson_id IS NULL) || ' 条记录缺少销售人员ID'
    END as salesperson_fix_status;

-- 8. 最终验证
SELECT '=== 最终验证 ===' as info;

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM sales_records 
            WHERE customer_source_id IS NULL 
               OR business_type_id IS NULL 
               OR income_type_id IS NULL 
               OR salesperson_id IS NULL
        ) = 0 
        THEN '🎉 所有关联ID问题已完全解决！前端应该不再显示"未定义"'
        ELSE '⚠️ 还有 ' || (
            SELECT COUNT(*) FROM sales_records 
            WHERE customer_source_id IS NULL 
               OR business_type_id IS NULL 
               OR income_type_id IS NULL 
               OR salesperson_id IS NULL
        ) || ' 条记录需要修复'
    END as final_fix_status;

-- 9. 显示基础数据样本
SELECT '=== 基础数据样本 ===' as info;

SELECT 'customer_sources' as table_type, id, name FROM customer_sources WHERE is_active = true ORDER BY id LIMIT 3;
SELECT 'business_types' as table_type, id, name FROM business_types WHERE is_active = true ORDER BY id LIMIT 3;
SELECT 'income_types' as table_type, id, name FROM income_types WHERE is_active = true ORDER BY id LIMIT 3;
SELECT 'salespeople' as table_type, id, name FROM salespeople WHERE is_active = true ORDER BY id LIMIT 3;
