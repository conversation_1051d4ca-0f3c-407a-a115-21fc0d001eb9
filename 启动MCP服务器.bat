@echo off
echo 🚀 启动MCP浏览器工具服务器...
echo.

echo 📋 可用的MCP服务器:
echo 1. Playwright MCP - 浏览器自动化
echo 2. ExecuteAutomation Playwright MCP - 增强的Playwright工具
echo 3. AgentDesk Browser Tools MCP - 浏览器工具集成
echo 4. Winds AI Frontend Development MCP - 前端开发工具
echo.

echo 请选择要启动的服务器 (1-4):
set /p choice=

if "%choice%"=="1" (
    echo 🎭 启动 Playwright MCP...
    npx @playwright/mcp
) else if "%choice%"=="2" (
    echo 🎯 启动 ExecuteAutomation Playwright MCP...
    npx @executeautomation/playwright-mcp-server
) else if "%choice%"=="3" (
    echo 🔧 启动 AgentDesk Browser Tools MCP...
    npx @agentdeskai/browser-tools-mcp
) else if "%choice%"=="4" (
    echo 🌪️ 启动 Winds AI Frontend Development MCP...
    npx @winds-ai/frontend-development-mcp-tools
) else (
    echo ❌ 无效选择，请重新运行脚本
    pause
    exit /b 1
)

echo.
echo ✅ MCP服务器已启动！
echo 💡 保持此窗口打开以维持服务器运行
echo 📝 在另一个终端中，您可以连接到此MCP服务器
pause
