<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.success { background: #28a745; }
        button.warning { background: #ffc107; color: #212529; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 销售管理系统功能测试</h1>
        <p>此页面用于测试和修复销售管理系统的各项功能</p>
        
        <div class="test-section">
            <h3>🔗 连接到主系统</h3>
            <button onclick="connectToMainSystem()">连接到主系统</button>
            <button onclick="checkSystemStatus()">检查系统状态</button>
            <div id="connectionStatus" class="status info">等待连接...</div>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>📊 系统诊断</h2>
            
            <div class="test-section">
                <h3>基础环境检查</h3>
                <button onclick="checkEnvironment()">检查环境</button>
                <div id="envStatus"></div>
            </div>
            
            <div class="test-section">
                <h3>数据库连接测试</h3>
                <button onclick="testDatabase()">测试数据库</button>
                <div id="dbStatus"></div>
            </div>
            
            <div class="test-section">
                <h3>数据加载测试</h3>
                <button onclick="testDataLoading()">测试数据加载</button>
                <div id="dataStatus"></div>
            </div>
        </div>

        <div class="container">
            <h2>🔧 功能修复</h2>
            
            <div class="test-section">
                <h3>ID映射修复</h3>
                <button onclick="fixIdMapping()">修复ID映射</button>
                <div id="idFixStatus"></div>
            </div>
            
            <div class="test-section">
                <h3>收款记录修复</h3>
                <button onclick="fixPaymentRecords()">修复收款功能</button>
                <div id="paymentFixStatus"></div>
            </div>
            
            <div class="test-section">
                <h3>数据同步修复</h3>
                <button onclick="fixDataSync()">修复数据同步</button>
                <div id="syncFixStatus"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 功能测试</h2>
        
        <div class="grid">
            <div class="test-section">
                <h3>新记录添加测试</h3>
                <button onclick="testAddRecord()">测试添加记录</button>
                <button onclick="testPaymentModal()">测试收款弹窗</button>
                <div id="addRecordStatus"></div>
            </div>
            
            <div class="test-section">
                <h3>收款功能测试</h3>
                <button onclick="testPaymentQuery()">测试收款查询</button>
                <button onclick="testAddPayment()">测试添加收款</button>
                <div id="paymentTestStatus"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📋 操作日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <button onclick="exportLog()">导出日志</button>
        <div id="logOutput" class="log">等待操作...</div>
    </div>

    <script>
        let mainWindow = null;
        let logOutput = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logOutput += logEntry;
            document.getElementById('logOutput').textContent = logOutput;
            document.getElementById('logOutput').scrollTop = document.getElementById('logOutput').scrollHeight;
            
            console.log(message);
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function connectToMainSystem() {
            log('🔗 尝试连接到主系统...');
            
            try {
                // 尝试打开主系统窗口
                mainWindow = window.open('http://127.0.0.1:5500/index.html', 'mainSystem');
                
                if (mainWindow) {
                    updateStatus('connectionStatus', '✅ 已打开主系统窗口', 'pass');
                    log('✅ 主系统窗口已打开');
                    
                    // 等待窗口加载
                    setTimeout(() => {
                        checkSystemStatus();
                    }, 3000);
                } else {
                    updateStatus('connectionStatus', '❌ 无法打开主系统窗口', 'fail');
                    log('❌ 无法打开主系统窗口，可能被弹窗阻止');
                }
            } catch (error) {
                updateStatus('connectionStatus', '❌ 连接失败: ' + error.message, 'fail');
                log('❌ 连接失败: ' + error.message);
            }
        }

        function checkSystemStatus() {
            log('🔍 检查系统状态...');
            
            if (!mainWindow || mainWindow.closed) {
                updateStatus('connectionStatus', '❌ 主系统窗口未打开', 'fail');
                log('❌ 主系统窗口未打开');
                return;
            }

            try {
                // 检查主窗口的关键变量
                const hasSupabase = typeof mainWindow.supabase !== 'undefined';
                const hasSalesData = typeof mainWindow.salesData !== 'undefined';
                const hasCurrentUser = typeof mainWindow.currentUser !== 'undefined';
                
                if (hasSupabase && hasSalesData && hasCurrentUser) {
                    updateStatus('connectionStatus', '✅ 系统连接正常', 'pass');
                    log('✅ 系统连接正常，可以进行测试');
                } else {
                    updateStatus('connectionStatus', '⚠️ 系统未完全加载', 'warning');
                    log('⚠️ 系统未完全加载，等待初始化...');
                }
            } catch (error) {
                updateStatus('connectionStatus', '❌ 无法访问主系统: ' + error.message, 'fail');
                log('❌ 无法访问主系统: ' + error.message);
            }
        }

        function checkEnvironment() {
            log('🔍 检查基础环境...');
            
            if (!mainWindow || mainWindow.closed) {
                updateStatus('envStatus', '❌ 请先连接主系统', 'fail');
                return;
            }

            try {
                const checks = [
                    { name: 'Supabase', check: () => typeof mainWindow.supabase !== 'undefined' },
                    { name: 'SalesData', check: () => typeof mainWindow.salesData !== 'undefined' },
                    { name: 'CurrentUser', check: () => typeof mainWindow.currentUser !== 'undefined' },
                    { name: 'UserPermissions', check: () => typeof mainWindow.userPermissions !== 'undefined' },
                    { name: 'LoadTableData', check: () => typeof mainWindow.loadTableData === 'function' },
                    { name: 'OpenPaymentModal', check: () => typeof mainWindow.openPaymentModal === 'function' }
                ];

                let passCount = 0;
                let results = [];

                checks.forEach(check => {
                    try {
                        const passed = check.check();
                        if (passed) passCount++;
                        results.push(`${passed ? '✅' : '❌'} ${check.name}`);
                        log(`${passed ? '✅' : '❌'} ${check.name}: ${passed ? '正常' : '缺失'}`);
                    } catch (error) {
                        results.push(`❌ ${check.name}: 错误`);
                        log(`❌ ${check.name}: ${error.message}`);
                    }
                });

                const status = passCount === checks.length ? 'pass' : (passCount > checks.length / 2 ? 'warning' : 'fail');
                updateStatus('envStatus', `${passCount}/${checks.length} 项检查通过`, status);
                
            } catch (error) {
                updateStatus('envStatus', '❌ 检查失败: ' + error.message, 'fail');
                log('❌ 环境检查失败: ' + error.message);
            }
        }

        function testDatabase() {
            log('🔍 测试数据库连接...');
            
            if (!mainWindow || mainWindow.closed) {
                updateStatus('dbStatus', '❌ 请先连接主系统', 'fail');
                return;
            }

            try {
                // 在主窗口中执行数据库测试
                mainWindow.eval(`
                    (async () => {
                        try {
                            const { data, error } = await supabase.from('sales_records').select('count', { count: 'exact' });
                            if (error) {
                                window.testResult = { success: false, message: error.message };
                            } else {
                                window.testResult = { success: true, count: data.length, message: '数据库连接正常' };
                            }
                        } catch (error) {
                            window.testResult = { success: false, message: error.message };
                        }
                    })();
                `);

                // 等待结果
                setTimeout(() => {
                    try {
                        const result = mainWindow.testResult;
                        if (result) {
                            if (result.success) {
                                updateStatus('dbStatus', `✅ ${result.message}`, 'pass');
                                log(`✅ 数据库测试成功: ${result.message}`);
                            } else {
                                updateStatus('dbStatus', `❌ ${result.message}`, 'fail');
                                log(`❌ 数据库测试失败: ${result.message}`);
                            }
                        } else {
                            updateStatus('dbStatus', '⚠️ 测试超时', 'warning');
                            log('⚠️ 数据库测试超时');
                        }
                    } catch (error) {
                        updateStatus('dbStatus', '❌ 获取结果失败', 'fail');
                        log('❌ 获取数据库测试结果失败: ' + error.message);
                    }
                }, 2000);

            } catch (error) {
                updateStatus('dbStatus', '❌ 测试失败: ' + error.message, 'fail');
                log('❌ 数据库测试失败: ' + error.message);
            }
        }

        function testDataLoading() {
            log('🔍 测试数据加载...');
            
            if (!mainWindow || mainWindow.closed) {
                updateStatus('dataStatus', '❌ 请先连接主系统', 'fail');
                return;
            }

            try {
                const beforeCount = mainWindow.salesData ? mainWindow.salesData.length : 0;
                log(`当前销售记录数: ${beforeCount}`);

                // 执行数据加载
                mainWindow.eval(`
                    (async () => {
                        try {
                            await loadTableData();
                            window.loadResult = { 
                                success: true, 
                                count: salesData ? salesData.length : 0,
                                message: '数据加载成功'
                            };
                        } catch (error) {
                            window.loadResult = { success: false, message: error.message };
                        }
                    })();
                `);

                // 等待结果
                setTimeout(() => {
                    try {
                        const result = mainWindow.loadResult;
                        if (result) {
                            if (result.success) {
                                updateStatus('dataStatus', `✅ 加载了 ${result.count} 条记录`, 'pass');
                                log(`✅ 数据加载成功: ${result.count} 条记录`);
                            } else {
                                updateStatus('dataStatus', `❌ ${result.message}`, 'fail');
                                log(`❌ 数据加载失败: ${result.message}`);
                            }
                        } else {
                            updateStatus('dataStatus', '⚠️ 加载超时', 'warning');
                            log('⚠️ 数据加载超时');
                        }
                    } catch (error) {
                        updateStatus('dataStatus', '❌ 获取结果失败', 'fail');
                        log('❌ 获取数据加载结果失败: ' + error.message);
                    }
                }, 3000);

            } catch (error) {
                updateStatus('dataStatus', '❌ 测试失败: ' + error.message, 'fail');
                log('❌ 数据加载测试失败: ' + error.message);
            }
        }

        function clearLog() {
            logOutput = '';
            document.getElementById('logOutput').textContent = '日志已清空';
        }

        function exportLog() {
            const blob = new Blob([logOutput], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-test-log-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            log('📄 日志已导出');
        }

        // 其他测试函数的占位符
        function fixIdMapping() { log('🔧 ID映射修复功能开发中...'); }
        function fixPaymentRecords() { log('🔧 收款记录修复功能开发中...'); }
        function fixDataSync() { log('🔧 数据同步修复功能开发中...'); }
        function testAddRecord() { log('🧪 添加记录测试功能开发中...'); }
        function testPaymentModal() { log('🧪 收款弹窗测试功能开发中...'); }
        function testPaymentQuery() { log('🧪 收款查询测试功能开发中...'); }
        function testAddPayment() { log('🧪 添加收款测试功能开发中...'); }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('🚀 测试页面已加载');
            log('请点击"连接到主系统"开始测试');
        };
    </script>
</body>
</html>
