// =====================================================
// 控制台诊断脚本 - 检查新添加销售记录的数据同步问题
// 在浏览器控制台中运行此脚本
// =====================================================

async function 诊断数据同步问题() {
    console.log('🔍 开始诊断数据同步问题...');
    
    try {
        // 1. 检查当前加载的销售数据
        console.log('\n=== 1. 当前前端销售数据 ===');
        console.log('salesData数组长度:', salesData?.length || 0);
        
        if (salesData && salesData.length > 0) {
            console.log('最新的3条前端记录:');
            salesData.slice(0, 3).forEach((record, index) => {
                console.log(`记录${index + 1}:`, {
                    前端ID: record.id,
                    数据库ID: record.databaseId,
                    客户: record.customer,
                    合同金额: record.contractAmount,
                    创建时间: record.createdAt,
                    创建者: record.createdBy
                });
            });
        }

        // 2. 直接查询数据库中的销售记录
        console.log('\n=== 2. 数据库中的销售记录 ===');
        const dbQuery = await supabase
            .from('sales_records')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(5);

        if (dbQuery.error) {
            console.error('❌ 数据库查询失败:', dbQuery.error);
        } else {
            console.log('数据库中最新的5条记录:');
            dbQuery.data.forEach((record, index) => {
                console.log(`数据库记录${index + 1}:`, {
                    ID: record.id,
                    客户: record.customer,
                    合同金额: record.contract_amount,
                    创建时间: record.created_at,
                    创建者: record.created_by
                });
            });
        }

        // 3. 比较前端和数据库数据
        console.log('\n=== 3. 数据同步对比 ===');
        if (salesData && dbQuery.data) {
            const frontendIds = new Set(salesData.map(r => r.databaseId || r.id));
            const dbIds = new Set(dbQuery.data.map(r => r.id));
            
            console.log('前端记录数:', salesData.length);
            console.log('数据库记录数:', dbQuery.data.length);
            
            // 找出数据库中有但前端没有的记录
            const missingInFrontend = dbQuery.data.filter(dbRecord => 
                !frontendIds.has(dbRecord.id)
            );
            
            if (missingInFrontend.length > 0) {
                console.warn('⚠️ 数据库中有但前端缺失的记录:');
                missingInFrontend.forEach(record => {
                    console.log('缺失记录:', {
                        ID: record.id,
                        客户: record.customer,
                        创建时间: record.created_at
                    });
                });
            } else {
                console.log('✅ 前端数据与数据库同步');
            }
        }

        // 4. 检查权限过滤
        console.log('\n=== 4. 权限过滤检查 ===');
        console.log('当前用户:', currentUser?.email);
        console.log('用户权限:', userPermissions);
        
        if (userPermissions?.role !== 'admin') {
            console.log('🔒 非管理员用户，检查权限过滤...');
            
            const userRecords = await supabase
                .from('sales_records')
                .select('*')
                .eq('created_by', currentUser?.email)
                .order('created_at', { ascending: false });
                
            if (userRecords.error) {
                console.error('❌ 用户记录查询失败:', userRecords.error);
            } else {
                console.log('用户自己的记录数:', userRecords.data.length);
                console.log('用户最新记录:', userRecords.data[0]);
            }
        }

        // 5. 测试关联查询
        console.log('\n=== 5. 关联查询测试 ===');
        const relationQuery = await supabase
            .from('sales_records')
            .select(`
                *,
                customer_sources(name),
                business_types(name),
                income_types(name),
                salespeople(name)
            `)
            .order('created_at', { ascending: false })
            .limit(3);

        if (relationQuery.error) {
            console.error('❌ 关联查询失败:', relationQuery.error);
            console.log('尝试备用查询...');
            
            const fallbackQuery = await supabase
                .from('sales_records')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(3);
                
            if (fallbackQuery.error) {
                console.error('❌ 备用查询也失败:', fallbackQuery.error);
            } else {
                console.log('✅ 备用查询成功，记录数:', fallbackQuery.data.length);
            }
        } else {
            console.log('✅ 关联查询成功，记录数:', relationQuery.data.length);
            console.log('关联查询示例:', relationQuery.data[0]);
        }

        // 6. 检查数据加载函数
        console.log('\n=== 6. 数据加载函数检查 ===');
        console.log('loadSalesRecordsFromDatabase函数存在:', typeof loadSalesRecordsFromDatabase === 'function');
        console.log('loadTableData函数存在:', typeof loadTableData === 'function');

        // 7. 建议的修复方案
        console.log('\n=== 7. 建议的修复方案 ===');
        console.log('1. 手动重新加载数据: loadTableData()');
        console.log('2. 检查权限设置是否正确');
        console.log('3. 确认新记录的created_by字段是否正确');
        console.log('4. 检查RLS策略是否阻止了数据访问');

    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error);
    }
}

// 快速修复函数
async function 强制重新加载数据() {
    console.log('🔄 强制重新加载数据...');
    try {
        await loadTableData();
        console.log('✅ 数据重新加载完成');
    } catch (error) {
        console.error('❌ 重新加载失败:', error);
    }
}

// 检查特定记录的收款数据
async function 检查收款数据(salesRecordId) {
    console.log(`🔍 检查销售记录 ${salesRecordId} 的收款数据...`);
    
    try {
        // 查找销售记录
        const salesRecord = salesData?.find(r => r.id == salesRecordId);
        if (!salesRecord) {
            console.error('❌ 在前端数据中找不到该销售记录');
            return;
        }
        
        console.log('销售记录信息:', {
            前端ID: salesRecord.id,
            数据库ID: salesRecord.databaseId,
            客户: salesRecord.customer
        });
        
        // 查询收款记录
        const databaseId = salesRecord.databaseId || salesRecord.id;
        const paymentQuery = await supabase
            .from('payment_records')
            .select('*')
            .eq('sales_record_id', databaseId);
            
        if (paymentQuery.error) {
            console.error('❌ 收款记录查询失败:', paymentQuery.error);
        } else {
            console.log('✅ 收款记录查询成功:', paymentQuery.data);
        }
        
    } catch (error) {
        console.error('❌ 检查收款数据时发生错误:', error);
    }
}

// 运行诊断
console.log('🚀 数据同步诊断脚本已加载');
console.log('运行命令:');
console.log('- 诊断数据同步问题()');
console.log('- 强制重新加载数据()');
console.log('- 检查收款数据(销售记录ID)');

// 创建页面诊断面板
function 创建诊断面板() {
    const panel = document.createElement('div');
    panel.id = 'diagnosticPanel';
    panel.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        width: 400px;
        max-height: 500px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        border: 2px solid #007acc;
    `;

    panel.innerHTML = `
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <h3 style="margin: 0; color: #00ff00;">🔍 数据同步诊断</h3>
            <button onclick="this.parentElement.parentElement.remove()" style="background: #ff4444; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer;">✕</button>
        </div>
        <div id="diagnosticContent">正在诊断...</div>
    `;

    document.body.appendChild(panel);
    return panel;
}

// 自动诊断并显示结果
async function 自动诊断并显示() {
    const panel = 创建诊断面板();
    const content = document.getElementById('diagnosticContent');

    let report = '';

    try {
        // 检查基本数据
        report += `📊 前端记录数: ${salesData?.length || 0}\n`;

        // 查询数据库
        const dbQuery = await supabase.from('sales_records').select('*').order('created_at', { ascending: false });
        report += `🗄️ 数据库记录数: ${dbQuery.data?.length || 0}\n`;

        if (dbQuery.error) {
            report += `❌ 数据库查询错误: ${dbQuery.error.message}\n`;
        }

        // 检查同步状态
        if (salesData && dbQuery.data) {
            const syncStatus = salesData.length === dbQuery.data.length ? '✅ 同步' : '⚠️ 不同步';
            report += `🔄 数据同步状态: ${syncStatus}\n`;

            // 检查最新记录
            if (salesData.length > 0 && dbQuery.data.length > 0) {
                const frontendLatest = salesData[0];
                const dbLatest = dbQuery.data[0];
                report += `\n📋 最新记录对比:\n`;
                report += `前端: ${frontendLatest.customer} (${frontendLatest.createdAt})\n`;
                report += `数据库: ${dbLatest.customer} (${dbLatest.created_at})\n`;

                // 检查收款记录
                const paymentQuery = await supabase
                    .from('payment_records')
                    .select('*')
                    .eq('sales_record_id', frontendLatest.databaseId || frontendLatest.id);

                report += `💰 该记录收款数: ${paymentQuery.data?.length || 0}\n`;
                if (paymentQuery.error) {
                    report += `❌ 收款查询错误: ${paymentQuery.error.message}\n`;
                }
            }
        }

        // 检查权限
        report += `\n👤 当前用户: ${currentUser?.email || '未知'}\n`;
        report += `🔒 用户权限: ${userPermissions?.role || '未知'}\n`;

        // 检查函数存在性
        report += `\n🔧 关键函数检查:\n`;
        report += `loadTableData: ${typeof loadTableData === 'function' ? '✅' : '❌'}\n`;
        report += `loadPaymentRecords: ${typeof loadPaymentRecords === 'function' ? '✅' : '❌'}\n`;

    } catch (error) {
        report += `❌ 诊断过程出错: ${error.message}\n`;
    }

    content.innerHTML = `<pre style="white-space: pre-wrap; margin: 0;">${report}</pre>`;

    // 5秒后自动刷新诊断
    setTimeout(() => {
        if (document.getElementById('diagnosticPanel')) {
            自动诊断并显示();
        }
    }, 5000);
}

// 自动运行诊断
自动诊断并显示();
