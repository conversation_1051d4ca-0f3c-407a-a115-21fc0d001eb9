# MCP Browser Tools Installation Verification Script
Write-Host "MCP Browser Tools Installation Verification" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

# Ensure Node.js is in PATH
$env:PATH += ";C:\Program Files\nodejs"

# 1. Check Node.js and npm
Write-Host "1. Checking Node.js and npm..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "   Node.js version: $nodeVersion" -ForegroundColor Green
    Write-Host "   npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "   ERROR: Node.js or npm not found" -ForegroundColor Red
    exit 1
}

# 2. Check installed MCP packages
Write-Host ""
Write-Host "2. Checking installed MCP packages..." -ForegroundColor Yellow

$mcpPackages = @(
    "@playwright/mcp",
    "@executeautomation/playwright-mcp-server", 
    "@agentdeskai/browser-tools-mcp",
    "@winds-ai/frontend-development-mcp-tools"
)

foreach ($package in $mcpPackages) {
    npm list -g $package 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   INSTALLED: $package" -ForegroundColor Green
    } else {
        Write-Host "   MISSING: $package" -ForegroundColor Red
    }
}

# 3. Check Playwright browsers
Write-Host ""
Write-Host "3. Checking Playwright browsers..." -ForegroundColor Yellow
$playwrightPath = "$env:LOCALAPPDATA\ms-playwright"
if (Test-Path $playwrightPath) {
    $browsers = Get-ChildItem $playwrightPath -Directory | Where-Object { $_.Name -like "*chromium*" }
    if ($browsers.Count -gt 0) {
        Write-Host "   INSTALLED: Chromium browser" -ForegroundColor Green
    } else {
        Write-Host "   MISSING: Chromium browser" -ForegroundColor Yellow
    }
} else {
    Write-Host "   MISSING: Playwright browser directory" -ForegroundColor Yellow
}

# 4. Test MCP server startup
Write-Host ""
Write-Host "4. Testing MCP server startup..." -ForegroundColor Yellow
Write-Host "   Testing @playwright/mcp..." -ForegroundColor Gray
npx @playwright/mcp --help 2>$null | Out-Null
if ($LASTEXITCODE -eq 0) {
    Write-Host "   SUCCESS: @playwright/mcp can start" -ForegroundColor Green
} else {
    Write-Host "   ERROR: @playwright/mcp startup failed" -ForegroundColor Red
}

# 5. Check configuration files
Write-Host ""
Write-Host "5. Checking configuration files..." -ForegroundColor Yellow
if (Test-Path "mcp-config.json") {
    Write-Host "   FOUND: mcp-config.json" -ForegroundColor Green
} else {
    Write-Host "   MISSING: mcp-config.json" -ForegroundColor Red
}

if (Test-Path "启动MCP服务器.ps1") {
    Write-Host "   FOUND: MCP server startup script" -ForegroundColor Green
} else {
    Write-Host "   MISSING: MCP server startup script" -ForegroundColor Red
}

# 6. Display usage instructions
Write-Host ""
Write-Host "USAGE INSTRUCTIONS:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "1. Start MCP server:" -ForegroundColor White
Write-Host "   .\启动MCP服务器.ps1" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Manual server startup:" -ForegroundColor White
Write-Host "   npx @playwright/mcp --headless --port 3001" -ForegroundColor Gray
Write-Host "   npx @executeautomation/playwright-mcp-server" -ForegroundColor Gray
Write-Host "   npx @agentdeskai/browser-tools-mcp" -ForegroundColor Gray
Write-Host "   npx @winds-ai/frontend-development-mcp-tools" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Connect in AI tools:" -ForegroundColor White
Write-Host "   - Use config file: mcp-config.json" -ForegroundColor Gray
Write-Host "   - Or connect directly to: localhost:3001" -ForegroundColor Gray
Write-Host ""

# 7. Display installation summary
Write-Host "INSTALLATION SUMMARY:" -ForegroundColor Magenta
Write-Host "====================" -ForegroundColor Magenta
Write-Host "✓ Node.js and npm installed and configured" -ForegroundColor Green
Write-Host "✓ 4 MCP browser tool packages installed:" -ForegroundColor Green
Write-Host "   - @playwright/mcp (Official Playwright)" -ForegroundColor Gray
Write-Host "   - @executeautomation/playwright-mcp-server (Enhanced)" -ForegroundColor Gray
Write-Host "   - @agentdeskai/browser-tools-mcp (Browser Tools)" -ForegroundColor Gray
Write-Host "   - @winds-ai/frontend-development-mcp-tools (Frontend Dev)" -ForegroundColor Gray
Write-Host "✓ Playwright Chromium browser installed" -ForegroundColor Green
Write-Host "✓ Configuration files and startup scripts created" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 MCP Browser Tools Installation Complete!" -ForegroundColor Green
Write-Host "You can now use these MCP servers in AI tools for browser automation!" -ForegroundColor Yellow

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
