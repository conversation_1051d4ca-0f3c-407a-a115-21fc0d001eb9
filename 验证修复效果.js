// =====================================================
// 验证修复效果脚本
// 在浏览器控制台中运行此脚本
// =====================================================

console.log('🔧 启动修复效果验证...');

async function 验证修复效果() {
    console.log('\n🔍 === 开始验证修复效果 ===');
    
    // 1. 检查salesData
    if (!salesData || salesData.length === 0) {
        console.log('❌ salesData为空');
        return false;
    }
    
    console.log(`📊 salesData包含 ${salesData.length} 条记录`);
    
    // 2. 查找测试记录
    const 测试记录 = salesData.find(r => r.customer === '测试测试12');
    if (!测试记录) {
        console.log('❌ 未找到测试记录');
        return false;
    }
    
    console.log('✅ 找到测试记录:', {
        客户: 测试记录.customer,
        前端ID: 测试记录.id,
        数据库ID: 测试记录.databaseId,
        合同金额: 测试记录.contractAmount
    });
    
    // 3. 检查数据库ID格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    
    if (测试记录.databaseId && uuidRegex.test(测试记录.databaseId)) {
        console.log('✅ 数据库ID格式正确（UUID）:', 测试记录.databaseId);
    } else {
        console.log('❌ 数据库ID格式不正确:', 测试记录.databaseId);
        
        // 尝试修复
        console.log('🔧 尝试修复数据库ID...');
        try {
            const 查询结果 = await supabase
                .from('sales_records')
                .select('id')
                .eq('frontend_id', 测试记录.id)
                .single();
            
            if (查询结果.error) {
                console.log('❌ 查询数据库ID失败:', 查询结果.error.message);
                return false;
            }
            
            测试记录.databaseId = 查询结果.data.id;
            localStorage.setItem('salesData', JSON.stringify(salesData));
            console.log('✅ 数据库ID修复成功:', 测试记录.databaseId);
            
        } catch (error) {
            console.log('❌ 修复数据库ID异常:', error.message);
            return false;
        }
    }
    
    // 4. 测试收款记录查询
    console.log('\n🔍 测试收款记录查询...');
    try {
        const 收款查询 = await supabase
            .from('payment_records')
            .select('*')
            .eq('sales_record_id', 测试记录.databaseId);
        
        if (收款查询.error) {
            console.log('❌ 收款记录查询失败:', 收款查询.error.message);
            return false;
        }
        
        console.log('✅ 收款记录查询成功:', 收款查询.data.length, '条记录');
        
        if (收款查询.data.length > 0) {
            console.log('收款记录详情:', 收款查询.data);
        }
        
    } catch (error) {
        console.log('❌ 收款记录查询异常:', error.message);
        return false;
    }
    
    // 5. 测试openPaymentModal函数
    console.log('\n🔍 测试openPaymentModal函数...');
    try {
        // 模拟调用openPaymentModal
        if (typeof openPaymentModal === 'function') {
            console.log('调用openPaymentModal...');
            await openPaymentModal(测试记录.id);
            
            // 检查全局变量是否正确设置
            if (currentDatabaseRecordId && uuidRegex.test(currentDatabaseRecordId)) {
                console.log('✅ currentDatabaseRecordId设置正确:', currentDatabaseRecordId);
            } else {
                console.log('❌ currentDatabaseRecordId设置错误:', currentDatabaseRecordId);
                return false;
            }
            
            // 检查弹窗是否显示
            const 弹窗 = document.getElementById('paymentModal');
            if (弹窗 &&弹窗.style.display === 'block') {
                console.log('✅ 收款弹窗已正确显示');
                
                // 检查弹窗内容
                const 客户元素 = document.getElementById('contractCustomer');
                const 金额元素 = document.getElementById('contractAmount');
                
                if (客户元素 && 客户元素.textContent === 测试记录.customer) {
                    console.log('✅ 弹窗客户信息正确');
                } else {
                    console.log('❌ 弹窗客户信息错误');
                }
                
                if (金额元素 && 金额元素.textContent.includes(测试记录.contractAmount)) {
                    console.log('✅ 弹窗金额信息正确');
                } else {
                    console.log('❌ 弹窗金额信息错误');
                }
                
            } else {
                console.log('❌ 收款弹窗未显示');
                return false;
            }
            
        } else {
            console.log('❌ openPaymentModal函数不存在');
            return false;
        }
        
    } catch (error) {
        console.log('❌ 测试openPaymentModal异常:', error.message);
        return false;
    }
    
    console.log('\n🎉 === 修复效果验证完成 ===');
    console.log('✅ 所有测试通过！收款功能应该可以正常使用了');
    console.log('现在可以尝试添加收款记录测试功能');
    
    return true;
}

// 快速测试添加收款记录
async function 测试添加收款记录() {
    console.log('\n🧪 === 测试添加收款记录 ===');
    
    if (!currentDatabaseRecordId) {
        console.log('❌ currentDatabaseRecordId为空，请先打开收款弹窗');
        return;
    }
    
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(currentDatabaseRecordId)) {
        console.log('❌ currentDatabaseRecordId格式不正确:', currentDatabaseRecordId);
        return;
    }
    
    try {
        const 测试收款数据 = {
            sales_record_id: currentDatabaseRecordId,
            payment_date: new Date().toISOString().split('T')[0],
            amount: 1000,
            payment_method: '银行转账',
            bank_info: '测试银行',
            transaction_id: 'TEST' + Date.now(),
            notes: '测试收款记录 - ' + new Date().toLocaleString(),
            created_by: currentUser?.email || '<EMAIL>'
        };
        
        console.log('添加测试收款记录:', 测试收款数据);
        
        const 插入结果 = await supabase
            .from('payment_records')
            .insert([测试收款数据]);
        
        if (插入结果.error) {
            console.log('❌ 添加测试收款记录失败:', 插入结果.error.message);
        } else {
            console.log('✅ 添加测试收款记录成功！');
            
            // 刷新收款记录显示
            if (typeof loadPaymentRecords === 'function') {
                await loadPaymentRecords(currentDatabaseRecordId);
                console.log('✅ 已刷新收款记录显示');
            }
        }
        
    } catch (error) {
        console.log('❌ 测试添加收款记录异常:', error.message);
    }
}

// 导出函数到全局
window.验证修复效果 = 验证修复效果;
window.测试添加收款记录 = 测试添加收款记录;

console.log('\n🎯 验证脚本已加载！');
console.log('可用命令:');
console.log('  验证修复效果()       - 全面验证修复是否成功');
console.log('  测试添加收款记录()   - 测试添加收款记录功能');
console.log('\n推荐执行: 验证修复效果()');

// 自动开始验证
验证修复效果();
