# 安全性头部
<IfModule mod_headers.c>
    # 内容类型保护
    Header always set X-Content-Type-Options nosniff
    
    # 缓存控制
    Header set Cache-Control "public, max-age=3600"
    
    # 对HTML文件设置不缓存
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
    
    # 对CSS和JS文件设置长期缓存
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    # 对图片文件设置长期缓存
    <FilesMatch "\.(jpg|jpeg|png|gif|ico|svg)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
</IfModule>

# GZIP压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 文件类型处理
<IfModule mod_mime.c>
    AddType text/html .html
    AddCharset utf-8 .html
    AddCharset utf-8 .css
    AddCharset utf-8 .js
</IfModule>

# 默认文档
DirectoryIndex index.html index.php 