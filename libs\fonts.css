/* 优化的字体系统 - 使用系统字体栈，无需外部加载 */

/* 主要字体栈 - 中文优化 */
.font-system {
    font-family: 
        /* 系统首选字体 */
        -apple-system, BlinkMacSystemFont,
        /* Windows 字体 */
        "Segoe UI", "Microsoft YaHei UI", "Microsoft YaHei",
        /* macOS 字体 */
        "PingFang SC", "Hiragino Sans GB",
        /* Android 字体 */
        "Droid Sans", "Droid Sans Fallback",
        /* 通用备用字体 */
        "Helvetica Neue", Helvetica, Arial,
        /* 最终备用 */
        sans-serif;
    
    /* 字体渲染优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 标题字体 */
.font-heading {
    font-family: 
        /* 系统标题字体 */
        -apple-system, BlinkMacSystemFont,
        "SF Pro Display", "Helvetica Neue",
        /* Windows 标题字体 */
        "Microsoft YaHei UI", "Microsoft YaHei",
        /* macOS 中文字体 */
        "PingFang SC", "Hiragino Sans GB",
        /* 备用字体 */
        Arial, sans-serif;
    
    font-weight: 600;
    letter-spacing: -0.025em;
}

/* 正文字体 */
.font-body {
    font-family: 
        /* 系统正文字体 */
        -apple-system, BlinkMacSystemFont,
        "SF Pro Text", "Helvetica Neue",
        /* Windows 正文字体 */
        "Microsoft YaHei", "Segoe UI",
        /* macOS 中文字体 */
        "PingFang SC", "Hiragino Sans GB",
        /* 备用字体 */
        Arial, sans-serif;
    
    line-height: 1.6;
}

/* 等宽字体 - 用于代码和数字 */
.font-mono {
    font-family: 
        /* 系统等宽字体 */
        "SF Mono", Monaco, Inconsolata, Consolas,
        /* Windows 等宽字体 */
        "Courier New", monospace;
    
    font-variant-numeric: tabular-nums;
}

/* 应用到全局 */
body {
    font-family: 
        -apple-system, BlinkMacSystemFont,
        "Segoe UI", "Microsoft YaHei UI", "Microsoft YaHei",
        "PingFang SC", "Hiragino Sans GB",
        "Droid Sans", "Droid Sans Fallback",
        "Helvetica Neue", Helvetica, Arial,
        sans-serif;
    
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 标题元素 */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: 
        -apple-system, BlinkMacSystemFont,
        "SF Pro Display", "Helvetica Neue",
        "Microsoft YaHei UI", "Microsoft YaHei",
        "PingFang SC", "Hiragino Sans GB",
        Arial, sans-serif;
    
    font-weight: 600;
    letter-spacing: -0.025em;
}

/* 按钮字体 */
button, .btn {
    font-family: inherit;
    font-weight: 500;
}

/* 输入框字体 */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
}

/* 数字显示优化 */
.number, .currency, .percentage {
    font-family: 
        "SF Mono", Monaco, Inconsolata, Consolas,
        "Courier New", monospace;
    font-variant-numeric: tabular-nums;
    font-weight: 500;
}

/* 字体大小工具类 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* 字体粗细工具类 */
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-black { font-weight: 900; }

/* 响应式字体 */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }
    
    h1, .h1 { font-size: 1.75rem; }
    h2, .h2 { font-size: 1.5rem; }
    h3, .h3 { font-size: 1.25rem; }
    h4, .h4 { font-size: 1.125rem; }
    h5, .h5 { font-size: 1rem; }
    h6, .h6 { font-size: 0.875rem; }
}

/* 打印优化 */
@media print {
    body {
        font-family: "Times New Roman", serif;
        color: #000;
    }
    
    h1, h2, h3, h4, h5, h6 {
        font-family: "Times New Roman", serif;
        font-weight: bold;
    }
} 