/**
 * 缓存优化器 - 提升页面加载速度和离线体验
 */

class CacheOptimizer {
    constructor() {
        this.cacheVersion = 'v1.0.0';
        this.cacheName = `sales-management-${this.cacheVersion}`;
        this.staticAssets = [
            '/',
            '/index.html',
            '/performance-monitor.js',
            '/libs/supabase.min.js',
            '/libs/xlsx.min.js',
            '/libs/html2canvas.min.js',
            '/libs/jspdf.min.js',
            '/libs/icons.svg',
            '/libs/icons.css',
            '/libs/fonts.css',
            '/libs/images.css'
        ];
        this.init();
    }

    init() {
        this.registerServiceWorker();
        this.optimizeLocalStorage();
        this.preloadCriticalResources();
        this.setupPerformanceObserver();
    }

    // 注册Service Worker
    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('✅ Service Worker 注册成功:', registration.scope);
                    this.checkForUpdates(registration);
                })
                .catch(error => {
                    console.warn('⚠️ Service Worker 注册失败:', error);
                });
        }
    }

    // 检查更新
    checkForUpdates(registration) {
        setInterval(() => {
            registration.update();
        }, 60000); // 每分钟检查一次更新
    }

    // 优化本地存储
    optimizeLocalStorage() {
        try {
            // 清理过期的本地存储
            this.cleanupExpiredData();
            
            // 优化存储大小
            this.optimizeStorageSize();
            
            console.log('✅ 本地存储优化完成');
        } catch (error) {
            console.warn('⚠️ 本地存储优化失败:', error);
        }
    }

    // 清理过期数据
    cleanupExpiredData() {
        const now = Date.now();
        const keys = Object.keys(localStorage);
        
        keys.forEach(key => {
            if (key.startsWith('cache_') || key.startsWith('temp_')) {
                try {
                    const data = JSON.parse(localStorage.getItem(key));
                    if (data.expires && data.expires < now) {
                        localStorage.removeItem(key);
                        console.log(`🗑️ 已清理过期数据: ${key}`);
                    }
                } catch (e) {
                    // 无效数据，直接删除
                    localStorage.removeItem(key);
                }
            }
        });
    }

    // 优化存储大小
    optimizeStorageSize() {
        const usage = this.getStorageUsage();
        if (usage.percentage > 80) {
            console.warn('⚠️ 本地存储使用率过高，开始清理...');
            this.cleanOldData();
        }
    }

    // 获取存储使用情况
    getStorageUsage() {
        let used = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                used += localStorage[key].length + key.length;
            }
        }
        
        const quota = 5 * 1024 * 1024; // 假设5MB限制
        return {
            used: used,
            quota: quota,
            percentage: (used / quota) * 100
        };
    }

    // 清理旧数据
    cleanOldData() {
        const keys = Object.keys(localStorage);
        const dataWithTime = [];

        keys.forEach(key => {
            try {
                const data = JSON.parse(localStorage.getItem(key));
                if (data.timestamp) {
                    dataWithTime.push({ key, timestamp: data.timestamp });
                }
            } catch (e) {
                // 忽略无效数据
            }
        });

        // 按时间排序，删除最旧的数据
        dataWithTime.sort((a, b) => a.timestamp - b.timestamp);
        const toDelete = Math.ceil(dataWithTime.length * 0.3); // 删除30%的旧数据

        for (let i = 0; i < toDelete; i++) {
            localStorage.removeItem(dataWithTime[i].key);
        }
    }

    // 预加载关键资源
    preloadCriticalResources() {
        const criticalResources = [
            { href: '/libs/icons.css', as: 'style' },
            { href: '/libs/fonts.css', as: 'style' },
            { href: '/libs/images.css', as: 'style' }
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            link.onload = () => {
                console.log(`✅ 预加载完成: ${resource.href}`);
            };
            link.onerror = () => {
                console.warn(`⚠️ 预加载失败: ${resource.href}`);
            };
            document.head.appendChild(link);
        });
    }

    // 设置性能监控
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // 监控资源加载时间
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.entryType === 'resource') {
                        this.logResourcePerformance(entry);
                    }
                });
            });

            observer.observe({ entryTypes: ['resource'] });
        }
    }

    // 记录资源性能
    logResourcePerformance(entry) {
        const duration = entry.responseEnd - entry.responseStart;
        if (duration > 1000) { // 超过1秒的请求
            console.warn(`🐌 慢资源: ${entry.name} - ${Math.round(duration)}ms`);
        }
    }

    // 缓存数据到本地存储
    cacheData(key, data, ttl = 3600000) { // 默认1小时过期
        const cacheData = {
            data: data,
            timestamp: Date.now(),
            expires: Date.now() + ttl
        };
        
        try {
            localStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
            return true;
        } catch (error) {
            console.warn('⚠️ 缓存数据失败:', error);
            return false;
        }
    }

    // 从本地存储获取缓存数据
    getCachedData(key) {
        try {
            const cached = localStorage.getItem(`cache_${key}`);
            if (!cached) return null;

            const data = JSON.parse(cached);
            if (data.expires && data.expires < Date.now()) {
                localStorage.removeItem(`cache_${key}`);
                return null;
            }

            return data.data;
        } catch (error) {
            console.warn('⚠️ 获取缓存数据失败:', error);
            return null;
        }
    }

    // 预缓存静态资源
    precacheStaticAssets() {
        if ('caches' in window) {
            caches.open(this.cacheName).then(cache => {
                cache.addAll(this.staticAssets).then(() => {
                    console.log('✅ 静态资源预缓存完成');
                }).catch(error => {
                    console.warn('⚠️ 静态资源预缓存失败:', error);
                });
            });
        }
    }

    // 获取缓存统计
    getCacheStats() {
        const storage = this.getStorageUsage();
        const stats = {
            localStorage: {
                used: (storage.used / 1024).toFixed(2) + ' KB',
                percentage: storage.percentage.toFixed(1) + '%'
            },
            timestamp: new Date().toISOString()
        };

        if ('caches' in window) {
            caches.open(this.cacheName).then(cache => {
                cache.keys().then(keys => {
                    stats.serviceWorker = {
                        cachedFiles: keys.length,
                        files: keys.map(request => request.url)
                    };
                    console.log('📊 缓存统计:', stats);
                });
            });
        }

        return stats;
    }

    // 清理所有缓存
    clearAllCaches() {
        // 清理 localStorage
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith('cache_') || key.startsWith('temp_')) {
                localStorage.removeItem(key);
            }
        });

        // 清理 Service Worker 缓存
        if ('caches' in window) {
            caches.delete(this.cacheName).then(() => {
                console.log('✅ Service Worker 缓存已清理');
            });
        }

        console.log('✅ 所有缓存已清理');
    }
}

// 全局缓存优化器实例
window.CacheOptimizer = CacheOptimizer;

// 自动初始化
if (typeof window !== 'undefined') {
    window.cacheOptimizer = new CacheOptimizer();
} 