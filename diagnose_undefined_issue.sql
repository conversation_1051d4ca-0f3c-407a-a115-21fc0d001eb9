-- =====================================================
-- 诊断"未定义"问题的SQL脚本
-- 用于检查数据库状态和找出问题根源
-- =====================================================

-- 1. 检查基础表是否存在数据
SELECT '=== 基础表数据检查 ===' as section;

SELECT 'customer_sources表' as table_name, COUNT(*) as total_count, 
       COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
FROM customer_sources;

SELECT 'business_types表' as table_name, COUNT(*) as total_count, 
       COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
FROM business_types;

SELECT 'income_types表' as table_name, COUNT(*) as total_count, 
       COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
FROM income_types;

SELECT 'salespeople表' as table_name, COUNT(*) as total_count, 
       COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
FROM salespeople;

-- 2. 检查sales_records表的关联字段状态
SELECT '=== sales_records关联字段检查 ===' as section;

SELECT 
    COUNT(*) as total_records,
    COUNT(customer_source_id) as has_customer_source_id,
    COUNT(business_type_id) as has_business_type_id,
    COUNT(income_type_id) as has_income_type_id,
    COUNT(salesperson_id) as has_salesperson_id,
    COUNT(CASE WHEN customer_source_id IS NULL THEN 1 END) as null_customer_source_id,
    COUNT(CASE WHEN business_type_id IS NULL THEN 1 END) as null_business_type_id,
    COUNT(CASE WHEN income_type_id IS NULL THEN 1 END) as null_income_type_id,
    COUNT(CASE WHEN salesperson_id IS NULL THEN 1 END) as null_salesperson_id
FROM sales_records;

-- 3. 检查sales_records表的文本字段内容
SELECT '=== sales_records文本字段检查 ===' as section;

SELECT 
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    customer_source_id,
    business_type_id,
    income_type_id,
    salesperson_id
FROM sales_records 
ORDER BY created_at DESC 
LIMIT 5;

-- 4. 检查视图是否存在
SELECT '=== 视图存在性检查 ===' as section;

SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('sales_records_detail', 'payment_summary', 'payment_details_view');

-- 5. 测试sales_records_detail视图查询
SELECT '=== sales_records_detail视图测试 ===' as section;

SELECT 
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson
FROM sales_records_detail 
ORDER BY created_at DESC 
LIMIT 5;

-- 6. 检查具体的关联问题
SELECT '=== 关联问题详细检查 ===' as section;

-- 检查customer_sources关联
SELECT 
    'customer_sources关联检查' as check_type,
    sr.id,
    sr.customer_source as text_value,
    sr.customer_source_id as id_value,
    cs.name as linked_name
FROM sales_records sr
LEFT JOIN customer_sources cs ON sr.customer_source_id = cs.id
WHERE sr.customer_source_id IS NULL AND sr.customer_source IS NOT NULL
LIMIT 3;

-- 检查business_types关联
SELECT 
    'business_types关联检查' as check_type,
    sr.id,
    sr.business_type as text_value,
    sr.business_type_id as id_value,
    bt.name as linked_name
FROM sales_records sr
LEFT JOIN business_types bt ON sr.business_type_id = bt.id
WHERE sr.business_type_id IS NULL AND sr.business_type IS NOT NULL
LIMIT 3;

-- 7. 查看基础表的具体数据
SELECT '=== 基础表数据样本 ===' as section;

SELECT 'customer_sources数据' as table_name, id, name, is_active FROM customer_sources LIMIT 5;
SELECT 'business_types数据' as table_name, id, name, is_active FROM business_types LIMIT 5;
SELECT 'income_types数据' as table_name, id, name, is_active FROM income_types LIMIT 5;
SELECT 'salespeople数据' as table_name, id, name, is_active FROM salespeople LIMIT 5;

-- 8. 检查是否有重复或无效数据
SELECT '=== 数据质量检查 ===' as section;

-- 检查重复的customer_sources
SELECT name, COUNT(*) as count 
FROM customer_sources 
WHERE is_active = true 
GROUP BY name 
HAVING COUNT(*) > 1;

-- 检查重复的business_types
SELECT name, COUNT(*) as count 
FROM business_types 
WHERE is_active = true 
GROUP BY name 
HAVING COUNT(*) > 1;

-- 9. 生成修复建议
SELECT '=== 修复建议 ===' as section;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM customer_sources WHERE is_active = true) = 0 
        THEN '需要添加customer_sources基础数据'
        WHEN (SELECT COUNT(*) FROM sales_records WHERE customer_source_id IS NULL AND customer_source IS NOT NULL) > 0
        THEN '需要修复customer_sources关联'
        ELSE 'customer_sources状态正常'
    END as customer_source_status;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM business_types WHERE is_active = true) = 0 
        THEN '需要添加business_types基础数据'
        WHEN (SELECT COUNT(*) FROM sales_records WHERE business_type_id IS NULL AND business_type IS NOT NULL) > 0
        THEN '需要修复business_types关联'
        ELSE 'business_types状态正常'
    END as business_type_status;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM income_types WHERE is_active = true) = 0 
        THEN '需要添加income_types基础数据'
        WHEN (SELECT COUNT(*) FROM sales_records WHERE income_type_id IS NULL AND income_type IS NOT NULL) > 0
        THEN '需要修复income_types关联'
        ELSE 'income_types状态正常'
    END as income_type_status;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM salespeople WHERE is_active = true) = 0 
        THEN '需要添加salespeople基础数据'
        WHEN (SELECT COUNT(*) FROM sales_records WHERE salesperson_id IS NULL AND salesperson IS NOT NULL) > 0
        THEN '需要修复salespeople关联'
        ELSE 'salespeople状态正常'
    END as salesperson_status;
