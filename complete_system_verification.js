// =====================================================
// 完整系统验证脚本
// 基于实际数据库结构，验证所有功能完整性
// 在浏览器控制台中运行
// =====================================================

console.log('🔍 开始完整系统验证...');

// 测试1: 验证数据库表结构完整性
async function testDatabaseStructure() {
    console.log('🧪 测试1: 数据库表结构完整性');
    
    if (!isSupabaseReady) {
        console.error('❌ Supabase未就绪');
        return false;
    }
    
    const requiredTables = [
        'business_types',
        'income_types', 
        'customer_sources',
        'salespeople',
        'sales_records',
        'sales_records_detail',
        'user_profiles'
    ];
    
    let allGood = true;
    
    for (const table of requiredTables) {
        try {
            const { data, error } = await supabase
                .from(table)
                .select('*')
                .limit(1);
            
            if (error) {
                console.error(`❌ 表 ${table} 访问失败:`, error.message);
                allGood = false;
            } else {
                console.log(`✅ 表 ${table} 访问正常`);
                
                // 检查关键列是否存在
                if (data && data.length > 0) {
                    const record = data[0];
                    if (table !== 'sales_records_detail' && table !== 'user_profiles') {
                        if (!record.hasOwnProperty('is_active')) {
                            console.warn(`⚠️ 表 ${table} 缺少 is_active 列`);
                        }
                        if (!record.hasOwnProperty('created_at')) {
                            console.warn(`⚠️ 表 ${table} 缺少 created_at 列`);
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`❌ 检查表 ${table} 时出错:`, error);
            allGood = false;
        }
    }
    
    return allGood;
}

// 测试2: 验证权限控制
async function testPermissionControl() {
    console.log('🧪 测试2: 权限控制验证');
    
    try {
        console.log('当前用户权限:', userPermissions);
        console.log('当前用户角色:', userPermissions.role);
        
        // 测试权限函数
        if (typeof check_user_permissions === 'function') {
            console.log('✅ check_user_permissions 函数存在');
        }
        
        // 验证销售人员权限
        if (userPermissions.role === 'sales') {
            console.log('🔒 销售人员权限验证:');
            console.log('  - can_view_all:', userPermissions.can_view_all);
            console.log('  - can_edit_all:', userPermissions.can_edit_all);
            console.log('  - can_delete_all:', userPermissions.can_delete_all);
            console.log('  - can_manage_settings:', userPermissions.can_manage_settings);
            
            if (!userPermissions.can_view_all) {
                console.log('✅ 销售人员正确限制为只能查看自己的记录');
            } else {
                console.warn('⚠️ 销售人员权限过高，可以查看所有记录');
            }
        }
        
        // 验证管理员权限
        if (userPermissions.role === 'admin') {
            console.log('👑 管理员权限验证:');
            console.log('  - 应该可以查看所有记录:', userPermissions.can_view_all);
            console.log('  - 应该可以编辑所有记录:', userPermissions.can_edit_all);
            console.log('  - 应该可以删除所有记录:', userPermissions.can_delete_all);
            console.log('  - 应该可以管理系统设置:', userPermissions.can_manage_settings);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 权限控制验证失败:', error);
        return false;
    }
}

// 测试3: 验证系统设置加载
async function testSystemSettingsLoading() {
    console.log('🧪 测试3: 系统设置加载验证');
    
    try {
        // 测试加载函数
        const success = await loadSystemSettings();
        
        if (success) {
            console.log('✅ 系统设置加载成功');
            
            // 验证数据格式
            const requiredSettings = ['businessTypes', 'incomeTypes', 'customerSources', 'salespeople'];
            let formatOK = true;
            
            requiredSettings.forEach(setting => {
                if (!Array.isArray(systemSettings[setting])) {
                    console.error(`❌ systemSettings.${setting} 不是数组`);
                    formatOK = false;
                } else {
                    console.log(`✅ systemSettings.${setting}: ${systemSettings[setting].length} 项`);
                }
            });
            
            return formatOK;
        } else {
            console.error('❌ 系统设置加载失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 系统设置加载测试失败:', error);
        return false;
    }
}

// 测试4: 验证销售记录权限过滤
async function testSalesRecordsPermissionFiltering() {
    console.log('🧪 测试4: 销售记录权限过滤验证');
    
    try {
        // 重新加载销售记录以测试权限过滤
        await loadSalesRecordsFromDatabase();
        
        console.log(`加载的销售记录数量: ${salesData.length}`);
        console.log(`当前用户角色: ${userPermissions.role}`);
        console.log(`当前用户邮箱: ${currentUser?.email}`);
        
        if (userPermissions.role === 'sales') {
            // 验证销售人员只能看到自己的记录
            const ownRecords = salesData.filter(record => 
                record.createdBy === currentUser.email
            );
            
            console.log(`自己创建的记录数量: ${ownRecords.length}`);
            
            if (salesData.length === ownRecords.length) {
                console.log('✅ 销售人员权限过滤正确：只能看到自己的记录');
            } else {
                console.warn('⚠️ 销售人员可能看到了其他人的记录');
                console.log('所有记录的创建者:', salesData.map(r => r.createdBy));
            }
        } else if (userPermissions.role === 'admin') {
            console.log('👑 管理员可以查看所有记录');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 销售记录权限过滤验证失败:', error);
        return false;
    }
}

// 测试5: 验证数据库同步功能
function testDatabaseSyncFunctions() {
    console.log('🧪 测试5: 数据库同步功能验证');
    
    const requiredFunctions = [
        'syncSalespersonToDatabase',
        'addSalespersonToDatabase',
        'updateSalespersonInDatabase',
        'removeSalespersonFromDatabase',
        'handleNewArchitectureSettingsChange',
        'findOrCreateId'
    ];
    
    let allGood = true;
    
    requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} 函数存在`);
        } else {
            console.error(`❌ ${funcName} 函数缺失`);
            allGood = false;
        }
    });
    
    // 检查 SettingsManager 的方法
    if (businessTypeManager) {
        const managerMethods = ['syncToDatabase', 'getTableName', 'addToDatabase', 'updateInDatabase', 'removeFromDatabase'];
        
        managerMethods.forEach(methodName => {
            if (typeof businessTypeManager[methodName] === 'function') {
                console.log(`✅ SettingsManager.${methodName} 方法存在`);
            } else {
                console.error(`❌ SettingsManager.${methodName} 方法缺失`);
                allGood = false;
            }
        });
    } else {
        console.error('❌ businessTypeManager 不存在');
        allGood = false;
    }
    
    return allGood;
}

// 测试6: 验证表单选项更新
function testFormOptionsUpdate() {
    console.log('🧪 测试6: 表单选项更新验证');
    
    try {
        // 测试表单选项更新函数
        if (typeof updateFormOptions === 'function') {
            updateFormOptions();
            console.log('✅ updateFormOptions 函数执行成功');
        } else {
            console.error('❌ updateFormOptions 函数不存在');
            return false;
        }
        
        // 检查表单选项是否正确填充
        const formSelects = [
            'businessType',
            'incomeType', 
            'customerSource',
            'salesperson'
        ];
        
        let allGood = true;
        
        formSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                const optionCount = select.options.length;
                console.log(`✅ ${selectId} 选择框有 ${optionCount} 个选项`);
                
                if (optionCount <= 1) {
                    console.warn(`⚠️ ${selectId} 选择框选项过少`);
                }
            } else {
                console.warn(`⚠️ ${selectId} 选择框不存在（可能当前不在相应页面）`);
            }
        });
        
        return allGood;
        
    } catch (error) {
        console.error('❌ 表单选项更新验证失败:', error);
        return false;
    }
}

// 测试7: 验证界面刷新功能
function testUIRefreshFunctionality() {
    console.log('🧪 测试7: 界面刷新功能验证');
    
    try {
        // 测试 refreshUI 函数
        if (typeof refreshUI === 'function') {
            refreshUI();
            console.log('✅ refreshUI 函数执行成功');
        } else {
            console.error('❌ refreshUI 函数不存在');
            return false;
        }
        
        // 测试兼容性函数
        const compatFunctions = ['refreshTableAndStats', 'refreshAll'];
        
        compatFunctions.forEach(funcName => {
            if (typeof window[funcName] === 'function') {
                console.log(`✅ ${funcName} 兼容函数存在`);
            } else {
                console.error(`❌ ${funcName} 兼容函数缺失`);
            }
        });
        
        return true;
        
    } catch (error) {
        console.error('❌ 界面刷新功能验证失败:', error);
        return false;
    }
}

// 测试8: 验证数据一致性
function testDataConsistency() {
    console.log('🧪 测试8: 数据一致性验证');
    
    try {
        // 检查销售数据格式
        if (!Array.isArray(salesData)) {
            console.error('❌ salesData 不是数组');
            return false;
        }
        
        console.log(`✅ 销售数据格式正确，共 ${salesData.length} 条记录`);
        
        // 检查系统设置格式
        if (!systemSettings || typeof systemSettings !== 'object') {
            console.error('❌ systemSettings 格式错误');
            return false;
        }
        
        console.log('✅ 系统设置格式正确');
        
        // 检查数据关联性
        let inconsistencyCount = 0;
        
        salesData.forEach((record, index) => {
            // 检查业务类型
            if (record.businessType && !systemSettings.businessTypes.includes(record.businessType)) {
                console.warn(`⚠️ 记录 ${index} 的业务类型 "${record.businessType}" 不在系统设置中`);
                inconsistencyCount++;
            }
            
            // 检查收入类型
            if (record.incomeType && !systemSettings.incomeTypes.includes(record.incomeType)) {
                console.warn(`⚠️ 记录 ${index} 的收入类型 "${record.incomeType}" 不在系统设置中`);
                inconsistencyCount++;
            }
            
            // 检查客户来源
            if (record.customerSource && !systemSettings.customerSources.includes(record.customerSource)) {
                console.warn(`⚠️ 记录 ${index} 的客户来源 "${record.customerSource}" 不在系统设置中`);
                inconsistencyCount++;
            }
        });
        
        if (inconsistencyCount === 0) {
            console.log('✅ 数据一致性检查通过');
        } else {
            console.warn(`⚠️ 发现 ${inconsistencyCount} 个数据不一致问题`);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 数据一致性验证失败:', error);
        return false;
    }
}

// 运行完整系统验证
async function runCompleteSystemVerification() {
    console.log('🚀 开始运行完整系统验证...');
    console.log('=====================================');
    
    const tests = [
        { name: '数据库表结构完整性', func: testDatabaseStructure, async: true },
        { name: '权限控制验证', func: testPermissionControl, async: true },
        { name: '系统设置加载验证', func: testSystemSettingsLoading, async: true },
        { name: '销售记录权限过滤验证', func: testSalesRecordsPermissionFiltering, async: true },
        { name: '数据库同步功能验证', func: testDatabaseSyncFunctions, async: false },
        { name: '表单选项更新验证', func: testFormOptionsUpdate, async: false },
        { name: '界面刷新功能验证', func: testUIRefreshFunctionality, async: false },
        { name: '数据一致性验证', func: testDataConsistency, async: false }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        console.log(`\n📋 测试: ${test.name}`);
        try {
            let result;
            if (test.async) {
                result = await test.func();
            } else {
                result = test.func();
            }
            
            if (result) {
                passedTests++;
                console.log(`✅ 测试通过: ${test.name}`);
            } else {
                console.error(`❌ 测试失败: ${test.name}`);
            }
        } catch (error) {
            console.error(`❌ 测试异常: ${test.name}`, error);
        }
    }
    
    console.log('\n=====================================');
    console.log(`🎯 验证结果: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 完整系统验证全部通过！');
        console.log('💡 系统功能完整，数据库架构正确');
        console.log('💡 权限控制正常，数据一致性良好');
        return true;
    } else {
        console.error('❌ 部分验证失败，需要进一步修复');
        return false;
    }
}

// 快速系统检查
async function quickSystemCheck() {
    console.log('⚡ 快速系统检查...');
    
    try {
        // 检查关键组件
        console.log('检查Supabase连接...', isSupabaseReady ? '✅' : '❌');
        console.log('检查用户登录...', currentUser ? '✅' : '❌');
        console.log('检查用户权限...', userPermissions.role || '未知');
        
        // 检查数据加载
        console.log('检查系统设置...', systemSettings ? '✅' : '❌');
        console.log('检查销售数据...', Array.isArray(salesData) ? '✅' : '❌');
        
        // 检查关键函数
        const keyFunctions = ['loadSystemSettings', 'loadSalesRecordsFromDatabase', 'refreshUI'];
        keyFunctions.forEach(funcName => {
            console.log(`检查 ${funcName}...`, typeof window[funcName] === 'function' ? '✅' : '❌');
        });
        
        console.log('🎉 快速系统检查完成！');
        return true;
        
    } catch (error) {
        console.error('❌ 快速系统检查失败:', error);
        return false;
    }
}

// 自动运行验证
console.log('🔍 完整系统验证脚本已加载');
console.log('💡 运行 runCompleteSystemVerification() 进行完整验证');
console.log('💡 运行 quickSystemCheck() 进行快速检查');

// 延迟自动运行快速检查
if (typeof window !== 'undefined') {
    setTimeout(() => {
        console.log('\n🔄 自动运行快速系统检查...');
        quickSystemCheck();
    }, 3000);
}
