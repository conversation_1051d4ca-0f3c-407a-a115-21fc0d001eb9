-- =====================================================
-- 测试修复结果的SQL脚本
-- 用于验证"未定义"问题是否已解决
-- =====================================================

-- 1. 检查基础表数据
SELECT '=== 基础表数据检查 ===' as section;

SELECT 'customer_sources' as table_name, COUNT(*) as total, COUNT(CASE WHEN is_active = true THEN 1 END) as active
FROM customer_sources
UNION ALL
SELECT 'business_types', COUNT(*), COUNT(CASE WHEN is_active = true THEN 1 END)
FROM business_types
UNION ALL
SELECT 'income_types', COUNT(*), COUNT(CASE WHEN is_active = true THEN 1 END)
FROM income_types
UNION ALL
SELECT 'salespeople', COUNT(*), COUNT(CASE WHEN is_active = true THEN 1 END)
FROM salespeople;

-- 2. 检查sales_records关联状态
SELECT '=== sales_records关联状态 ===' as section;

SELECT 
    COUNT(*) as total_records,
    COUNT(customer_source_id) as has_customer_source_id,
    COUNT(business_type_id) as has_business_type_id,
    COUNT(income_type_id) as has_income_type_id,
    COUNT(salesperson_id) as has_salesperson_id,
    COUNT(CASE WHEN customer_source_id IS NULL THEN 1 END) as missing_customer_source_id,
    COUNT(CASE WHEN business_type_id IS NULL THEN 1 END) as missing_business_type_id,
    COUNT(CASE WHEN income_type_id IS NULL THEN 1 END) as missing_income_type_id,
    COUNT(CASE WHEN salesperson_id IS NULL THEN 1 END) as missing_salesperson_id
FROM sales_records;

-- 3. 测试sales_records_detail视图
SELECT '=== sales_records_detail视图测试 ===' as section;

SELECT 
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    amount,
    contract_amount
FROM sales_records_detail 
ORDER BY created_at DESC;

-- 4. 检查是否还有"未定义"或NULL值
SELECT '=== 未定义值检查 ===' as section;

SELECT 
    COUNT(CASE WHEN customer_source = '未定义' OR customer_source IS NULL THEN 1 END) as undefined_customer_source,
    COUNT(CASE WHEN business_type = '未定义' OR business_type IS NULL THEN 1 END) as undefined_business_type,
    COUNT(CASE WHEN income_type = '未定义' OR income_type IS NULL THEN 1 END) as undefined_income_type,
    COUNT(CASE WHEN salesperson = '未定义' OR salesperson IS NULL THEN 1 END) as undefined_salesperson
FROM sales_records_detail;

-- 5. 显示具体的"未定义"记录（如果有）
SELECT '=== 具体的未定义记录 ===' as section;

SELECT 
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson
FROM sales_records_detail 
WHERE customer_source = '未定义' 
   OR business_type = '未定义' 
   OR income_type = '未定义' 
   OR salesperson = '未定义'
   OR customer_source IS NULL
   OR business_type IS NULL
   OR income_type IS NULL
   OR salesperson IS NULL;

-- 6. 检查payment_summary视图是否正常
SELECT '=== payment_summary视图测试 ===' as section;

SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN customer_source = '未定义' THEN 1 END) as undefined_customer_source,
    COUNT(CASE WHEN business_type = '未定义' THEN 1 END) as undefined_business_type,
    COUNT(CASE WHEN income_type = '未定义' THEN 1 END) as undefined_income_type,
    COUNT(CASE WHEN salesperson = '未定义' THEN 1 END) as undefined_salesperson
FROM payment_summary;

-- 7. 显示修复后的数据样本
SELECT '=== 修复后的数据样本 ===' as section;

SELECT 
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    amount,
    contract_amount,
    payment_status
FROM payment_summary 
ORDER BY created_at DESC 
LIMIT 10;

-- 8. 检查视图是否存在
SELECT '=== 视图存在性检查 ===' as section;

SELECT 
    schemaname,
    viewname,
    definition IS NOT NULL as has_definition
FROM pg_views 
WHERE schemaname = 'public' 
AND viewname IN ('sales_records_detail', 'payment_summary', 'payment_details_view');

-- 9. 生成修复状态报告
SELECT '=== 修复状态报告 ===' as section;

WITH fix_status AS (
    SELECT 
        (SELECT COUNT(*) FROM sales_records WHERE customer_source_id IS NULL) as missing_customer_source_ids,
        (SELECT COUNT(*) FROM sales_records WHERE business_type_id IS NULL) as missing_business_type_ids,
        (SELECT COUNT(*) FROM sales_records WHERE income_type_id IS NULL) as missing_income_type_ids,
        (SELECT COUNT(*) FROM sales_records WHERE salesperson_id IS NULL) as missing_salesperson_ids,
        (SELECT COUNT(*) FROM sales_records_detail WHERE customer_source = '未定义') as undefined_customer_sources,
        (SELECT COUNT(*) FROM sales_records_detail WHERE business_type = '未定义') as undefined_business_types,
        (SELECT COUNT(*) FROM sales_records_detail WHERE income_type = '未定义') as undefined_income_types,
        (SELECT COUNT(*) FROM sales_records_detail WHERE salesperson = '未定义') as undefined_salespersons
)
SELECT 
    CASE 
        WHEN missing_customer_source_ids = 0 AND undefined_customer_sources = 0 
        THEN '✅ 客户来源修复完成'
        ELSE '❌ 客户来源仍有问题: ' || missing_customer_source_ids || ' 个缺失ID, ' || undefined_customer_sources || ' 个未定义'
    END as customer_source_status,
    
    CASE 
        WHEN missing_business_type_ids = 0 AND undefined_business_types = 0 
        THEN '✅ 业务类型修复完成'
        ELSE '❌ 业务类型仍有问题: ' || missing_business_type_ids || ' 个缺失ID, ' || undefined_business_types || ' 个未定义'
    END as business_type_status,
    
    CASE 
        WHEN missing_income_type_ids = 0 AND undefined_income_types = 0 
        THEN '✅ 收入类型修复完成'
        ELSE '❌ 收入类型仍有问题: ' || missing_income_type_ids || ' 个缺失ID, ' || undefined_income_types || ' 个未定义'
    END as income_type_status,
    
    CASE 
        WHEN missing_salesperson_ids = 0 AND undefined_salespersons = 0 
        THEN '✅ 销售人员修复完成'
        ELSE '❌ 销售人员仍有问题: ' || missing_salesperson_ids || ' 个缺失ID, ' || undefined_salespersons || ' 个未定义'
    END as salesperson_status
FROM fix_status;

-- 10. 最终验证
SELECT '=== 最终验证 ===' as section;

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM sales_records_detail 
            WHERE customer_source = '未定义' 
               OR business_type = '未定义' 
               OR income_type = '未定义' 
               OR salesperson = '未定义'
        ) = 0 
        THEN '🎉 所有"未定义"问题已完全解决！'
        ELSE '⚠️ 仍有未定义问题需要处理'
    END as final_status;
