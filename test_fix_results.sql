-- =====================================================
-- 测试修复结果的SQL脚本
-- 用于验证"未定义"问题是否已解决
-- =====================================================

-- 1. 检查基础表数据
SELECT '=== 基础表数据检查 ===' as section;

SELECT 'customer_sources' as table_name, COUNT(*) as total, COUNT(CASE WHEN is_active = true THEN 1 END) as active
FROM customer_sources
UNION ALL
SELECT 'business_types', COUNT(*), COUNT(CASE WHEN is_active = true THEN 1 END)
FROM business_types
UNION ALL
SELECT 'income_types', COUNT(*), COUNT(CASE WHEN is_active = true THEN 1 END)
FROM income_types
UNION ALL
SELECT 'salespeople', COUNT(*), COUNT(CASE WHEN is_active = true THEN 1 END)
FROM salespeople;

-- 2. 检查sales_records关联状态
SELECT '=== sales_records关联状态 ===' as section;

SELECT 
    COUNT(*) as total_records,
    COUNT(customer_source_id) as has_customer_source_id,
    COUNT(business_type_id) as has_business_type_id,
    COUNT(income_type_id) as has_income_type_id,
    COUNT(salesperson_id) as has_salesperson_id,
    COUNT(CASE WHEN customer_source_id IS NULL THEN 1 END) as missing_customer_source_id,
    COUNT(CASE WHEN business_type_id IS NULL THEN 1 END) as missing_business_type_id,
    COUNT(CASE WHEN income_type_id IS NULL THEN 1 END) as missing_income_type_id,
    COUNT(CASE WHEN salesperson_id IS NULL THEN 1 END) as missing_salesperson_id
FROM sales_records;

-- 3. 测试sales_records_detail视图
SELECT '=== sales_records_detail视图测试 ===' as section;

SELECT 
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    amount,
    contract_amount
FROM sales_records_detail 
ORDER BY created_at DESC;

-- 4. 检查是否还有"未定义"或NULL值
SELECT '=== 未定义值检查 ===' as section;

-- 首先检查sales_records_detail视图是否存在
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'sales_records_detail') THEN
        -- 如果视图存在，使用视图查询
        PERFORM 1;
    ELSE
        -- 如果视图不存在，先创建视图
        EXECUTE '
        CREATE OR REPLACE VIEW sales_records_detail AS
        SELECT
            sr.id,
            sr.frontend_id,
            sr.date,
            sr.customer,
            COALESCE(cs.name, ''未知来源'') as customer_source,
            COALESCE(bt.name, ''未知业务'') as business_type,
            COALESCE(it.name, ''未知收入'') as income_type,
            COALESCE(sp.name, ''未知销售'') as salesperson,
            sr.amount,
            sr.contract_amount,
            sr.total_received,
            sr.payment_status,
            sr.payment_count,
            sr.last_payment_date,
            sr.remaining_amount,
            sr.remarks,
            sr.user_id,
            sr.created_by,
            sr.created_at,
            sr.updated_at
        FROM sales_records sr
        LEFT JOIN customer_sources cs ON sr.customer_source_id = cs.id
        LEFT JOIN business_types bt ON sr.business_type_id = bt.id
        LEFT JOIN income_types it ON sr.income_type_id = it.id
        LEFT JOIN salespeople sp ON sr.salesperson_id = sp.id
        ORDER BY sr.created_at DESC;
        ';
    END IF;
END $$;

-- 现在安全地查询未定义值
SELECT
    COUNT(CASE WHEN customer_source IN ('未定义', '未知来源') OR customer_source IS NULL THEN 1 END) as undefined_customer_source,
    COUNT(CASE WHEN business_type IN ('未定义', '未知业务') OR business_type IS NULL THEN 1 END) as undefined_business_type,
    COUNT(CASE WHEN income_type IN ('未定义', '未知收入') OR income_type IS NULL THEN 1 END) as undefined_income_type,
    COUNT(CASE WHEN salesperson IN ('未定义', '未知销售') OR salesperson IS NULL THEN 1 END) as undefined_salesperson
FROM sales_records_detail;

-- 5. 显示具体的"未定义"记录（如果有）
SELECT '=== 具体的未定义记录 ===' as section;

SELECT
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson
FROM sales_records_detail
WHERE customer_source IN ('未定义', '未知来源')
   OR business_type IN ('未定义', '未知业务')
   OR income_type IN ('未定义', '未知收入')
   OR salesperson IN ('未定义', '未知销售')
   OR customer_source IS NULL
   OR business_type IS NULL
   OR income_type IS NULL
   OR salesperson IS NULL;

-- 6. 检查payment_summary视图是否正常
SELECT '=== payment_summary视图测试 ===' as section;

-- 检查payment_summary视图是否存在，如果不存在则跳过
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'payment_summary') THEN
        RAISE NOTICE 'payment_summary视图存在，执行查询...';
    ELSE
        RAISE NOTICE 'payment_summary视图不存在，跳过此检查';
    END IF;
END $$;

-- 如果视图存在，执行查询
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN customer_source IN ('未定义', '未知来源') THEN 1 END) as undefined_customer_source,
    COUNT(CASE WHEN business_type IN ('未定义', '未知业务') THEN 1 END) as undefined_business_type,
    COUNT(CASE WHEN income_type IN ('未定义', '未知收入') THEN 1 END) as undefined_income_type,
    COUNT(CASE WHEN salesperson IN ('未定义', '未知销售') THEN 1 END) as undefined_salesperson
FROM payment_summary
WHERE EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'payment_summary');

-- 7. 显示修复后的数据样本
SELECT '=== 修复后的数据样本 ===' as section;

-- 优先从sales_records_detail视图显示数据
SELECT
    id,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    amount,
    contract_amount,
    payment_status
FROM sales_records_detail
ORDER BY created_at DESC
LIMIT 10;

-- 8. 检查视图是否存在
SELECT '=== 视图存在性检查 ===' as section;

SELECT 
    schemaname,
    viewname,
    definition IS NOT NULL as has_definition
FROM pg_views 
WHERE schemaname = 'public' 
AND viewname IN ('sales_records_detail', 'payment_summary', 'payment_details_view');

-- 9. 生成修复状态报告
SELECT '=== 修复状态报告 ===' as section;

WITH fix_status AS (
    SELECT 
        (SELECT COUNT(*) FROM sales_records WHERE customer_source_id IS NULL) as missing_customer_source_ids,
        (SELECT COUNT(*) FROM sales_records WHERE business_type_id IS NULL) as missing_business_type_ids,
        (SELECT COUNT(*) FROM sales_records WHERE income_type_id IS NULL) as missing_income_type_ids,
        (SELECT COUNT(*) FROM sales_records WHERE salesperson_id IS NULL) as missing_salesperson_ids,
        (SELECT COUNT(*) FROM sales_records_detail WHERE customer_source IN ('未定义', '未知来源')) as undefined_customer_sources,
        (SELECT COUNT(*) FROM sales_records_detail WHERE business_type IN ('未定义', '未知业务')) as undefined_business_types,
        (SELECT COUNT(*) FROM sales_records_detail WHERE income_type IN ('未定义', '未知收入')) as undefined_income_types,
        (SELECT COUNT(*) FROM sales_records_detail WHERE salesperson IN ('未定义', '未知销售')) as undefined_salespersons
)
SELECT 
    CASE 
        WHEN missing_customer_source_ids = 0 AND undefined_customer_sources = 0 
        THEN '✅ 客户来源修复完成'
        ELSE '❌ 客户来源仍有问题: ' || missing_customer_source_ids || ' 个缺失ID, ' || undefined_customer_sources || ' 个未定义'
    END as customer_source_status,
    
    CASE 
        WHEN missing_business_type_ids = 0 AND undefined_business_types = 0 
        THEN '✅ 业务类型修复完成'
        ELSE '❌ 业务类型仍有问题: ' || missing_business_type_ids || ' 个缺失ID, ' || undefined_business_types || ' 个未定义'
    END as business_type_status,
    
    CASE 
        WHEN missing_income_type_ids = 0 AND undefined_income_types = 0 
        THEN '✅ 收入类型修复完成'
        ELSE '❌ 收入类型仍有问题: ' || missing_income_type_ids || ' 个缺失ID, ' || undefined_income_types || ' 个未定义'
    END as income_type_status,
    
    CASE 
        WHEN missing_salesperson_ids = 0 AND undefined_salespersons = 0 
        THEN '✅ 销售人员修复完成'
        ELSE '❌ 销售人员仍有问题: ' || missing_salesperson_ids || ' 个缺失ID, ' || undefined_salespersons || ' 个未定义'
    END as salesperson_status
FROM fix_status;

-- 10. 最终验证
SELECT '=== 最终验证 ===' as section;

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM sales_records_detail
            WHERE customer_source IN ('未定义', '未知来源')
               OR business_type IN ('未定义', '未知业务')
               OR income_type IN ('未定义', '未知收入')
               OR salesperson IN ('未定义', '未知销售')
        ) = 0
        THEN '🎉 所有"未定义"问题已完全解决！'
        ELSE '⚠️ 仍有未定义问题需要处理'
    END as final_status;
