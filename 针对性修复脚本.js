// =====================================================
// 针对性修复脚本 - 专门解决新记录收款同步问题
// 在浏览器控制台中运行此脚本
// =====================================================

console.log('🔧 启动针对性修复脚本...');

// 1. 诊断当前状态
async function 诊断当前状态() {
    console.log('\n📊 当前系统状态:');
    console.log('- 销售记录数量:', salesData?.length || 0);
    console.log('- 当前用户:', currentUser?.email || '未登录');
    console.log('- 用户权限:', userPermissions?.role || '未知');
    console.log('- Supabase状态:', typeof supabase !== 'undefined' ? '已连接' : '未连接');
    
    if (salesData && salesData.length > 0) {
        console.log('\n📋 最新的3条记录:');
        salesData.slice(0, 3).forEach((record, index) => {
            console.log(`${index + 1}. ${record.customer} - 前端ID: ${record.id}, 数据库ID: ${record.databaseId}`);
        });
    }
}

// 2. 修复ID映射问题
async function 修复ID映射() {
    console.log('\n🔧 检查和修复ID映射...');
    
    if (!salesData || salesData.length === 0) {
        console.log('❌ 没有销售数据需要修复');
        return;
    }
    
    let 修复计数 = 0;
    
    for (const record of salesData) {
        if (!record.databaseId) {
            console.log(`🔍 修复记录 ${record.customer} 的ID映射...`);
            
            try {
                // 通过frontend_id查找数据库记录
                const { data, error } = await supabase
                    .from('sales_records')
                    .select('id')
                    .eq('frontend_id', record.id)
                    .single();
                
                if (error) {
                    console.error(`❌ 查找失败:`, error);
                } else if (data) {
                    record.databaseId = data.id;
                    修复计数++;
                    console.log(`✅ 修复成功: ${record.customer} -> ${data.id}`);
                }
            } catch (error) {
                console.error(`❌ 修复异常:`, error);
            }
        }
    }
    
    console.log(`🎯 ID映射修复完成，共修复 ${修复计数} 条记录`);
}

// 3. 测试收款记录查询
async function 测试收款查询() {
    console.log('\n🔍 测试收款记录查询...');
    
    if (!salesData || salesData.length === 0) {
        console.log('❌ 没有销售数据可测试');
        return;
    }
    
    const 测试记录 = salesData[0];
    console.log(`测试记录: ${测试记录.customer}`);
    console.log(`前端ID: ${测试记录.id}`);
    console.log(`数据库ID: ${测试记录.databaseId}`);
    
    if (!测试记录.databaseId) {
        console.log('❌ 数据库ID为空，无法测试');
        return;
    }
    
    try {
        // 测试收款记录查询
        const { data, error } = await supabase
            .from('payment_records')
            .select('*')
            .eq('sales_record_id', 测试记录.databaseId);
        
        if (error) {
            console.error('❌ 收款记录查询失败:', error);
            
            // 检查表是否存在
            const { data: tables } = await supabase
                .from('information_schema.tables')
                .select('table_name')
                .eq('table_name', 'payment_records');
                
            if (!tables || tables.length === 0) {
                console.log('⚠️ payment_records表不存在，需要创建');
                await 创建收款记录表();
            }
        } else {
            console.log(`✅ 收款记录查询成功，找到 ${data.length} 条记录`);
            if (data.length > 0) {
                console.log('收款记录详情:', data);
            }
        }
    } catch (error) {
        console.error('❌ 测试异常:', error);
    }
}

// 4. 创建收款记录表（如果不存在）
async function 创建收款记录表() {
    console.log('\n🏗️ 创建收款记录表...');
    
    try {
        // 注意：这个操作需要数据库管理员权限
        console.log('⚠️ 此操作需要在Supabase控制台中手动执行');
        console.log('请在Supabase SQL编辑器中运行以下SQL:');
        
        const sql = `
-- 创建收款记录表
CREATE TABLE IF NOT EXISTS payment_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sales_record_id UUID NOT NULL REFERENCES sales_records(id) ON DELETE CASCADE,
    payment_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT '银行转账',
    bank_info TEXT,
    transaction_id VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_payment_records_sales_record_id ON payment_records(sales_record_id);
CREATE INDEX IF NOT EXISTS idx_payment_records_payment_date ON payment_records(payment_date);

-- 启用RLS
ALTER TABLE payment_records ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view payment records for their sales records" ON payment_records
    FOR SELECT USING (
        sales_record_id IN (
            SELECT id FROM sales_records 
            WHERE created_by = auth.jwt() ->> 'email'
        )
        OR 
        EXISTS (
            SELECT 1 FROM user_permissions 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Users can insert payment records for their sales records" ON payment_records
    FOR INSERT WITH CHECK (
        sales_record_id IN (
            SELECT id FROM sales_records 
            WHERE created_by = auth.jwt() ->> 'email'
        )
        OR 
        EXISTS (
            SELECT 1 FROM user_permissions 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );
`;
        
        console.log(sql);
        
    } catch (error) {
        console.error('❌ 创建表失败:', error);
    }
}

// 5. 测试添加收款记录
async function 测试添加收款() {
    console.log('\n💰 测试添加收款记录...');
    
    if (!salesData || salesData.length === 0) {
        console.log('❌ 没有销售数据可测试');
        return;
    }
    
    const 测试记录 = salesData[0];
    if (!测试记录.databaseId) {
        console.log('❌ 数据库ID为空，无法测试');
        return;
    }
    
    const 测试收款 = {
        sales_record_id: 测试记录.databaseId,
        payment_date: new Date().toISOString().split('T')[0],
        amount: 1000,
        payment_method: '银行转账',
        notes: '测试收款记录 - ' + new Date().toISOString(),
        created_by: currentUser?.email || '<EMAIL>'
    };
    
    try {
        const { data, error } = await supabase
            .from('payment_records')
            .insert([测试收款])
            .select();
        
        if (error) {
            console.error('❌ 添加收款记录失败:', error);
            
            if (error.code === '42P01') {
                console.log('⚠️ payment_records表不存在');
                await 创建收款记录表();
            }
        } else {
            console.log('✅ 添加收款记录成功:', data);
            
            // 验证查询
            const { data: verifyData } = await supabase
                .from('payment_records')
                .select('*')
                .eq('sales_record_id', 测试记录.databaseId);
                
            console.log(`✅ 验证成功，该销售记录现在有 ${verifyData.length} 条收款记录`);
        }
    } catch (error) {
        console.error('❌ 测试异常:', error);
    }
}

// 6. 修复收款状态显示
function 修复收款状态显示() {
    console.log('\n🎨 修复收款状态显示...');
    
    // 重新渲染表格
    if (typeof renderTable === 'function') {
        renderTable();
        console.log('✅ 表格已重新渲染');
    }
    
    // 更新统计信息
    if (typeof updateStatistics === 'function') {
        updateStatistics();
        console.log('✅ 统计信息已更新');
    }
}

// 7. 强制刷新数据
async function 强制刷新数据() {
    console.log('\n🔄 强制刷新数据...');
    
    try {
        if (typeof loadTableData === 'function') {
            await loadTableData();
            console.log('✅ 数据刷新完成');
        } else {
            console.log('❌ loadTableData函数不存在');
        }
    } catch (error) {
        console.error('❌ 刷新失败:', error);
    }
}

// 主修复流程
async function 执行完整修复() {
    console.log('🚀 开始执行完整修复流程...');
    console.log('=====================================');
    
    await 诊断当前状态();
    await 修复ID映射();
    await 测试收款查询();
    await 测试添加收款();
    修复收款状态显示();
    await 强制刷新数据();
    
    console.log('\n🎉 修复流程完成！');
    console.log('请测试以下功能:');
    console.log('1. 添加新的销售记录');
    console.log('2. 点击新记录的"收款管理"按钮');
    console.log('3. 查看是否能正常显示收款记录');
    console.log('4. 尝试添加收款记录');
}

// 导出函数到全局
window.诊断当前状态 = 诊断当前状态;
window.修复ID映射 = 修复ID映射;
window.测试收款查询 = 测试收款查询;
window.测试添加收款 = 测试添加收款;
window.执行完整修复 = 执行完整修复;

console.log('🎯 针对性修复脚本已加载');
console.log('可用命令:');
console.log('- 执行完整修复()');
console.log('- 诊断当前状态()');
console.log('- 修复ID映射()');
console.log('- 测试收款查询()');
console.log('- 测试添加收款()');

// 自动执行诊断
诊断当前状态();
