-- =====================================================
-- 数据修复脚本：解决"未定义"显示问题
-- 用于修复数据库升级后的关联字段显示问题
-- =====================================================

-- 1. 检查当前数据状况
SELECT 
    '当前sales_records数据检查' as check_item,
    COUNT(*) as total_records,
    COUNT(CASE WHEN customer_source_id IS NULL THEN 1 END) as null_customer_source,
    COUNT(CASE WHEN business_type_id IS NULL THEN 1 END) as null_business_type,
    COUNT(CASE WHEN income_type_id IS NULL THEN 1 END) as null_income_type,
    COUNT(CASE WHEN salesperson_id IS NULL THEN 1 END) as null_salesperson
FROM sales_records;

-- 2. 检查关联表数据
SELECT 'customer_sources表' as table_name, COUNT(*) as record_count FROM customer_sources WHERE is_active = true;
SELECT 'business_types表' as table_name, COUNT(*) as record_count FROM business_types WHERE is_active = true;
SELECT 'income_types表' as table_name, COUNT(*) as record_count FROM income_types WHERE is_active = true;
SELECT 'salespeople表' as table_name, COUNT(*) as record_count FROM salespeople WHERE is_active = true;

-- 3. 创建数据修复函数
CREATE OR REPLACE FUNCTION fix_sales_records_relations()
RETURNS TEXT AS $$
DECLARE
    fixed_count INTEGER := 0;
    record_cursor CURSOR FOR 
        SELECT id, customer_source, business_type, income_type, salesperson 
        FROM sales_records 
        WHERE customer_source_id IS NULL 
           OR business_type_id IS NULL 
           OR income_type_id IS NULL 
           OR salesperson_id IS NULL;
    
    rec RECORD;
    cs_id UUID;
    bt_id UUID;
    it_id UUID;
    sp_id UUID;
BEGIN
    -- 遍历需要修复的记录
    FOR rec IN record_cursor LOOP
        -- 查找或创建customer_source
        IF rec.customer_source IS NOT NULL AND rec.customer_source != '' THEN
            SELECT id INTO cs_id 
            FROM customer_sources 
            WHERE name = rec.customer_source AND is_active = true 
            LIMIT 1;
            
            IF cs_id IS NULL THEN
                INSERT INTO customer_sources (name, description, is_active)
                VALUES (rec.customer_source, '数据修复时自动创建', true)
                RETURNING id INTO cs_id;
            END IF;
        END IF;
        
        -- 查找或创建business_type
        IF rec.business_type IS NOT NULL AND rec.business_type != '' THEN
            SELECT id INTO bt_id 
            FROM business_types 
            WHERE name = rec.business_type AND is_active = true 
            LIMIT 1;
            
            IF bt_id IS NULL THEN
                INSERT INTO business_types (name, description, is_active)
                VALUES (rec.business_type, '数据修复时自动创建', true)
                RETURNING id INTO bt_id;
            END IF;
        END IF;
        
        -- 查找或创建income_type
        IF rec.income_type IS NOT NULL AND rec.income_type != '' THEN
            SELECT id INTO it_id 
            FROM income_types 
            WHERE name = rec.income_type AND is_active = true 
            LIMIT 1;
            
            IF it_id IS NULL THEN
                INSERT INTO income_types (name, description, is_active)
                VALUES (rec.income_type, '数据修复时自动创建', true)
                RETURNING id INTO it_id;
            END IF;
        END IF;
        
        -- 查找或创建salesperson
        IF rec.salesperson IS NOT NULL AND rec.salesperson != '' THEN
            SELECT id INTO sp_id 
            FROM salespeople 
            WHERE name = rec.salesperson AND is_active = true 
            LIMIT 1;
            
            IF sp_id IS NULL THEN
                INSERT INTO salespeople (name, is_active)
                VALUES (rec.salesperson, true)
                RETURNING id INTO sp_id;
            END IF;
        END IF;
        
        -- 更新sales_records记录
        UPDATE sales_records 
        SET 
            customer_source_id = COALESCE(cs_id, customer_source_id),
            business_type_id = COALESCE(bt_id, business_type_id),
            income_type_id = COALESCE(it_id, income_type_id),
            salesperson_id = COALESCE(sp_id, salesperson_id),
            updated_at = NOW()
        WHERE id = rec.id;
        
        fixed_count := fixed_count + 1;
        
        -- 重置变量
        cs_id := NULL;
        bt_id := NULL;
        it_id := NULL;
        sp_id := NULL;
    END LOOP;
    
    RETURN '修复完成，共处理 ' || fixed_count || ' 条记录';
END;
$$ LANGUAGE plpgsql;

-- 4. 执行数据修复
SELECT fix_sales_records_relations() as repair_result;

-- 5. 创建或更新sales_records_detail视图以正确显示关联数据
CREATE OR REPLACE VIEW sales_records_detail AS
SELECT 
    sr.id,
    sr.frontend_id,
    sr.date,
    sr.customer,
    COALESCE(cs.name, sr.customer_source, '未定义') as customer_source,
    COALESCE(bt.name, sr.business_type, '未定义') as business_type,
    COALESCE(it.name, sr.income_type, '未定义') as income_type,
    COALESCE(sp.name, sr.salesperson, '未定义') as salesperson,
    sr.amount,
    sr.contract_amount,
    sr.total_received,
    sr.payment_status,
    sr.payment_count,
    sr.last_payment_date,
    sr.remarks,
    sr.user_id,
    sr.created_by,
    sr.created_at,
    sr.updated_at
FROM sales_records sr
LEFT JOIN customer_sources cs ON sr.customer_source_id = cs.id AND cs.is_active = true
LEFT JOIN business_types bt ON sr.business_type_id = bt.id AND bt.is_active = true
LEFT JOIN income_types it ON sr.income_type_id = it.id AND it.is_active = true
LEFT JOIN salespeople sp ON sr.salesperson_id = sp.id AND sp.is_active = true
ORDER BY sr.created_at DESC;

-- 6. 验证修复结果
SELECT 
    '修复后数据检查' as check_item,
    COUNT(*) as total_records,
    COUNT(CASE WHEN customer_source_id IS NULL THEN 1 END) as null_customer_source,
    COUNT(CASE WHEN business_type_id IS NULL THEN 1 END) as null_business_type,
    COUNT(CASE WHEN income_type_id IS NULL THEN 1 END) as null_income_type,
    COUNT(CASE WHEN salesperson_id IS NULL THEN 1 END) as null_salesperson
FROM sales_records;

-- 7. 检查sales_records_detail视图数据
SELECT 
    '视图数据检查' as check_item,
    COUNT(*) as total_records,
    COUNT(CASE WHEN customer_source = '未定义' THEN 1 END) as undefined_customer_source,
    COUNT(CASE WHEN business_type = '未定义' THEN 1 END) as undefined_business_type,
    COUNT(CASE WHEN income_type = '未定义' THEN 1 END) as undefined_income_type,
    COUNT(CASE WHEN salesperson = '未定义' THEN 1 END) as undefined_salesperson
FROM sales_records_detail;

-- 8. 显示修复后的数据样本
SELECT 
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    amount,
    contract_amount
FROM sales_records_detail 
LIMIT 10;

-- 9. 清理修复函数（可选）
-- DROP FUNCTION IF EXISTS fix_sales_records_relations();

SELECT '✅ 数据修复脚本执行完成！请刷新前端页面查看效果。' as message;
