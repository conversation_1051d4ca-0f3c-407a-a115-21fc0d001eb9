-- =====================================================
-- Supabase数据库升级验证脚本
-- 用于验证分期收款功能升级是否成功
-- =====================================================

-- 1. 检查新表是否创建成功
SELECT 'payment_records表检查' as check_item,
CASE 
    WHEN COUNT(*) > 0 THEN '✅ 表已创建'
    ELSE '❌ 表不存在'
END as status
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'payment_records';

-- 2. 检查sales_records表新字段
SELECT 'sales_records新字段检查' as check_item,
STRING_AGG(
    CASE 
        WHEN column_name IN ('total_received', 'payment_status', 'last_payment_date', 'payment_count') 
        THEN '✅ ' || column_name
        ELSE NULL
    END,
    ', '
) as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'sales_records' 
AND column_name IN ('total_received', 'payment_status', 'last_payment_date', 'payment_count');

-- 3. 检查视图是否创建成功
SELECT 'payment_summary视图检查' as check_item,
CASE 
    WHEN COUNT(*) > 0 THEN '✅ 视图已创建'
    ELSE '❌ 视图不存在'
END as status
FROM information_schema.views 
WHERE table_schema = 'public' AND table_name = 'payment_summary';

SELECT 'payment_details_view视图检查' as check_item,
CASE 
    WHEN COUNT(*) > 0 THEN '✅ 视图已创建'
    ELSE '❌ 视图不存在'
END as status
FROM information_schema.views 
WHERE table_schema = 'public' AND table_name = 'payment_details_view';

-- 4. 检查函数是否创建成功
SELECT 'payment_stats函数检查' as check_item,
CASE 
    WHEN COUNT(*) > 0 THEN '✅ 函数已创建'
    ELSE '❌ 函数不存在'
END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'update_sales_record_payment_stats';

-- 5. 检查触发器是否创建成功
SELECT 'payment_records触发器检查' as check_item,
STRING_AGG(trigger_name, ', ') as triggers_created
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND event_object_table = 'payment_records';

-- 6. 检查RLS策略
SELECT 'RLS策略检查' as check_item,
CASE 
    WHEN COUNT(*) >= 4 THEN '✅ RLS策略已创建 (' || COUNT(*) || '个)'
    ELSE '❌ RLS策略不完整 (' || COUNT(*) || '个)'
END as status
FROM pg_policies 
WHERE tablename = 'payment_records';

-- 7. 检查数据迁移情况
SELECT 'payment_records数据迁移检查' as check_item,
CONCAT(
    '总记录数: ', COUNT(*), 
    ', 有效记录: ', SUM(CASE WHEN is_deleted = FALSE THEN 1 ELSE 0 END),
    ', 迁移记录: ', SUM(CASE WHEN remarks LIKE '%历史数据迁移%' THEN 1 ELSE 0 END)
) as status
FROM payment_records;

-- 8. 检查sales_records统计字段更新情况
SELECT 'sales_records统计字段更新检查' as check_item,
CONCAT(
    '有合同金额记录: ', COUNT(*),
    ', 已更新total_received: ', SUM(CASE WHEN total_received > 0 THEN 1 ELSE 0 END),
    ', 已更新payment_status: ', SUM(CASE WHEN payment_status != 'unpaid' THEN 1 ELSE 0 END)
) as status
FROM sales_records 
WHERE contract_amount > 0;

-- 9. 验证付款状态分布
SELECT 
    '付款状态分布' as check_item,
    payment_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sales_records WHERE contract_amount > 0), 1) || '%' as percentage
FROM sales_records 
WHERE contract_amount > 0
GROUP BY payment_status
ORDER BY payment_status;

-- 10. 验证收款统计准确性（抽样检查）
SELECT 
    '收款统计准确性验证' as check_item,
    sr.id,
    sr.customer,
    sr.contract_amount,
    sr.total_received as system_calculated,
    COALESCE(manual_sum.manual_total, 0) as manual_calculated,
    CASE 
        WHEN sr.total_received = COALESCE(manual_sum.manual_total, 0) THEN '✅ 一致'
        ELSE '❌ 不一致'
    END as accuracy_check
FROM sales_records sr
LEFT JOIN (
    SELECT 
        sales_record_id,
        SUM(payment_amount) as manual_total
    FROM payment_records 
    WHERE is_deleted = FALSE
    GROUP BY sales_record_id
) manual_sum ON sr.id = manual_sum.sales_record_id
WHERE sr.contract_amount > 0
LIMIT 10;

-- 11. 检查外键约束
SELECT 'payment_records外键约束检查' as check_item,
CASE 
    WHEN COUNT(*) > 0 THEN '✅ 外键约束已创建'
    ELSE '❌ 外键约束缺失'
END as status
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_schema = 'public' 
AND tc.table_name = 'payment_records' 
AND tc.constraint_type = 'FOREIGN KEY';

-- 12. 检查索引创建情况
SELECT 'payment_records索引检查' as check_item,
STRING_AGG(DISTINCT indexname, ', ') as indexes_created
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename = 'payment_records'
AND indexname != 'payment_records_pkey';

-- 13. 功能测试：查看payment_summary视图数据
SELECT 
    '功能测试：payment_summary视图' as test_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_count,
    SUM(CASE WHEN payment_status = 'partial' THEN 1 ELSE 0 END) as partial_count,
    SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_count,
    SUM(remaining_amount) as total_remaining
FROM payment_summary;

-- 14. 功能测试：查看payment_details_view视图数据
SELECT 
    '功能测试：payment_details_view视图' as test_name,
    COUNT(*) as total_payment_details,
    COUNT(DISTINCT sales_record_id) as unique_sales_records,
    SUM(payment_amount) as total_payments,
    MIN(payment_date) as earliest_payment,
    MAX(payment_date) as latest_payment
FROM payment_details_view;

-- 15. 数据完整性检查
SELECT 
    '数据完整性检查' as check_item,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 无数据完整性问题'
        ELSE '❌ 发现 ' || COUNT(*) || ' 个问题'
    END as status
FROM (
    -- 检查是否有payment_records指向不存在的sales_records
    SELECT pr.id 
    FROM payment_records pr 
    LEFT JOIN sales_records sr ON pr.sales_record_id = sr.id 
    WHERE sr.id IS NULL
    
    UNION ALL
    
    -- 检查是否有负数金额
    SELECT pr.id 
    FROM payment_records pr 
    WHERE pr.payment_amount < 0
    
    UNION ALL
    
    -- 检查是否有未来日期的收款记录
    SELECT pr.id 
    FROM payment_records pr 
    WHERE pr.payment_date > CURRENT_DATE
) integrity_issues;

-- 16. 升级总结报告
SELECT 
    '=== Supabase数据库升级总结报告 ===' as report_section,
    '' as details
UNION ALL
SELECT 
    '升级项目',
    '状态'
UNION ALL
SELECT 
    '✅ payment_records表',
    '已创建'
UNION ALL
SELECT 
    '✅ sales_records新字段',
    '已添加'
UNION ALL
SELECT 
    '✅ payment_summary视图',
    '已创建'
UNION ALL
SELECT 
    '✅ payment_details_view视图',
    '已创建'
UNION ALL
SELECT 
    '✅ 自动更新函数和触发器',
    '已创建'
UNION ALL
SELECT 
    '✅ RLS安全策略',
    '已配置'
UNION ALL
SELECT 
    '✅ 历史数据迁移',
    '已完成'
UNION ALL
SELECT 
    '✅ 索引优化',
    '已创建'
UNION ALL
SELECT 
    '=== 下一步操作建议 ===',
    ''
UNION ALL
SELECT 
    '1. 更新前端界面',
    '支持分期收款录入'
UNION ALL
SELECT 
    '2. 更新JavaScript代码',
    '添加收款记录管理'
UNION ALL
SELECT 
    '3. 更新统计分析',
    '使用新的付款状态'
UNION ALL
SELECT 
    '4. 测试RLS策略',
    '验证权限控制'
UNION ALL
SELECT 
    '5. 用户培训',
    '新功能使用说明';
