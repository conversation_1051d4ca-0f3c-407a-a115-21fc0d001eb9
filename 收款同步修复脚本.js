// =====================================================
// 收款同步修复脚本 - 专门解决新记录收款同步问题
// 在浏览器控制台中运行此脚本
// =====================================================

console.log('🔧 启动收款同步修复脚本...');

class 收款同步修复器 {
    constructor() {
        this.调试信息 = [];
        this.修复步骤 = [];
    }

    记录调试(信息) {
        const 时间戳 = new Date().toLocaleTimeString();
        const 调试条目 = `[${时间戳}] ${信息}`;
        this.调试信息.push(调试条目);
        console.log(调试条目);
    }

    记录修复步骤(步骤) {
        this.修复步骤.push(步骤);
        console.log(`🔧 修复步骤: ${步骤}`);
    }

    // 1. 诊断当前状态
    async 诊断当前状态() {
        this.记录调试('🔍 开始诊断当前状态...');
        
        // 检查全局变量
        if (typeof salesData === 'undefined') {
            this.记录调试('❌ salesData未定义');
            return false;
        }
        
        if (typeof currentDatabaseRecordId === 'undefined') {
            this.记录调试('❌ currentDatabaseRecordId未定义');
            return false;
        }
        
        this.记录调试(`✅ salesData包含${salesData.length}条记录`);
        this.记录调试(`✅ 当前数据库记录ID: ${currentDatabaseRecordId}`);
        
        // 查找当前记录
        const 当前记录 = salesData.find(r => r.databaseId === currentDatabaseRecordId || r.id === currentDatabaseRecordId);
        if (当前记录) {
            this.记录调试(`✅ 找到当前记录: 客户=${当前记录.customer}, ID=${当前记录.id}, 数据库ID=${当前记录.databaseId}`);
            return 当前记录;
        } else {
            this.记录调试(`❌ 未找到当前记录，ID: ${currentDatabaseRecordId}`);
            return false;
        }
    }

    // 2. 检查数据库连接
    async 检查数据库连接() {
        this.记录调试('🔍 检查数据库连接...');
        
        try {
            // 测试sales_records表
            const 销售记录测试 = await supabase.from('sales_records').select('count', { count: 'exact' });
            if (销售记录测试.error) {
                this.记录调试(`❌ sales_records表查询失败: ${销售记录测试.error.message}`);
                return false;
            }
            this.记录调试(`✅ sales_records表正常，总记录数: ${销售记录测试.count}`);

            // 测试payment_records表
            const 收款记录测试 = await supabase.from('payment_records').select('count', { count: 'exact' });
            if (收款记录测试.error) {
                this.记录调试(`❌ payment_records表查询失败: ${收款记录测试.error.message}`);
                return false;
            }
            this.记录调试(`✅ payment_records表正常，总记录数: ${收款记录测试.count}`);
            
            return true;
        } catch (error) {
            this.记录调试(`❌ 数据库连接异常: ${error.message}`);
            return false;
        }
    }

    // 3. 查询当前记录的收款数据
    async 查询当前收款数据() {
        this.记录调试('🔍 查询当前记录的收款数据...');
        
        if (!currentDatabaseRecordId) {
            this.记录调试('❌ 当前数据库记录ID为空');
            return null;
        }

        try {
            // 尝试多种查询策略
            const 查询策略 = [
                {
                    名称: 'payment_records表直接查询',
                    查询: () => supabase.from('payment_records').select('*').eq('sales_record_id', currentDatabaseRecordId)
                },
                {
                    名称: 'payment_details_view视图查询',
                    查询: () => supabase.from('payment_details_view').select('*').eq('sales_record_id', currentDatabaseRecordId)
                }
            ];

            for (const 策略 of 查询策略) {
                this.记录调试(`尝试${策略.名称}...`);
                try {
                    const 结果 = await 策略.查询();
                    if (结果.error) {
                        this.记录调试(`${策略.名称}失败: ${结果.error.message}`);
                    } else {
                        this.记录调试(`${策略.名称}成功: 找到${结果.data.length}条记录`);
                        if (结果.data.length > 0) {
                            this.记录调试(`收款记录详情: ${JSON.stringify(结果.data[0], null, 2)}`);
                        }
                        return 结果.data;
                    }
                } catch (error) {
                    this.记录调试(`${策略.名称}异常: ${error.message}`);
                }
            }
            
            return [];
        } catch (error) {
            this.记录调试(`❌ 查询收款数据异常: ${error.message}`);
            return null;
        }
    }

    // 4. 检查ID映射问题
    检查ID映射问题() {
        this.记录调试('🔍 检查ID映射问题...');
        
        if (!salesData || salesData.length === 0) {
            this.记录调试('❌ 没有销售数据可检查');
            return false;
        }

        let 映射正常数 = 0;
        let 映射异常数 = 0;
        let 异常记录 = [];

        salesData.forEach((记录, 索引) => {
            if (记录.databaseId && 记录.databaseId !== 记录.id) {
                映射正常数++;
            } else {
                映射异常数++;
                异常记录.push({
                    索引,
                    客户: 记录.customer,
                    前端ID: 记录.id,
                    数据库ID: 记录.databaseId
                });
            }
        });

        this.记录调试(`ID映射统计: 正常=${映射正常数}, 异常=${映射异常数}`);
        
        if (异常记录.length > 0) {
            this.记录调试('❌ 发现ID映射异常的记录:');
            异常记录.forEach(记录 => {
                this.记录调试(`  - 客户: ${记录.客户}, 前端ID: ${记录.前端ID}, 数据库ID: ${记录.数据库ID}`);
            });
            return 异常记录;
        }
        
        this.记录调试('✅ 所有记录的ID映射正常');
        return true;
    }

    // 5. 修复ID映射
    async 修复ID映射() {
        this.记录调试('🔧 开始修复ID映射...');
        
        const ID映射问题 = this.检查ID映射问题();
        if (ID映射问题 === true) {
            this.记录调试('✅ ID映射无需修复');
            return true;
        }

        if (!Array.isArray(ID映射问题)) {
            this.记录调试('❌ 无法获取ID映射问题详情');
            return false;
        }

        // 修复每个异常记录
        for (const 异常记录 of ID映射问题) {
            try {
                // 查询数据库中的实际记录
                const 查询结果 = await supabase
                    .from('sales_records')
                    .select('id')
                    .eq('customer', 异常记录.客户)
                    .single();

                if (查询结果.error) {
                    this.记录调试(`❌ 查询客户"${异常记录.客户}"的数据库记录失败: ${查询结果.error.message}`);
                    continue;
                }

                // 更新前端数据
                const 前端记录 = salesData[异常记录.索引];
                前端记录.databaseId = 查询结果.data.id;
                
                this.记录调试(`✅ 修复客户"${异常记录.客户}"的ID映射: ${查询结果.data.id}`);
                this.记录修复步骤(`修复客户"${异常记录.客户}"的ID映射`);
                
            } catch (error) {
                this.记录调试(`❌ 修复客户"${异常记录.客户}"的ID映射时异常: ${error.message}`);
            }
        }

        return true;
    }

    // 6. 强制刷新收款记录
    async 强制刷新收款记录() {
        this.记录调试('🔧 强制刷新收款记录...');
        
        try {
            // 重新调用loadPaymentRecords函数
            if (typeof loadPaymentRecords === 'function') {
                await loadPaymentRecords(currentDatabaseRecordId);
                this.记录调试('✅ 成功调用loadPaymentRecords函数');
                this.记录修复步骤('强制刷新收款记录');
                return true;
            } else {
                this.记录调试('❌ loadPaymentRecords函数不存在');
                return false;
            }
        } catch (error) {
            this.记录调试(`❌ 强制刷新收款记录异常: ${error.message}`);
            return false;
        }
    }

    // 7. 测试添加收款记录
    async 测试添加收款记录() {
        this.记录调试('🧪 测试添加收款记录...');
        
        if (!currentDatabaseRecordId) {
            this.记录调试('❌ 当前数据库记录ID为空，无法测试');
            return false;
        }

        try {
            const 测试数据 = {
                sales_record_id: currentDatabaseRecordId,
                payment_date: new Date().toISOString().split('T')[0],
                amount: 100,
                payment_method: '银行转账',
                bank_info: '测试银行',
                transaction_id: 'TEST' + Date.now(),
                notes: '测试收款记录',
                created_by: currentUser?.email || '<EMAIL>'
            };

            const 插入结果 = await supabase
                .from('payment_records')
                .insert([测试数据]);

            if (插入结果.error) {
                this.记录调试(`❌ 测试添加收款记录失败: ${插入结果.error.message}`);
                return false;
            }

            this.记录调试('✅ 测试添加收款记录成功');
            this.记录修复步骤('测试添加收款记录');
            
            // 立即刷新显示
            await this.强制刷新收款记录();
            
            return true;
        } catch (error) {
            this.记录调试(`❌ 测试添加收款记录异常: ${error.message}`);
            return false;
        }
    }

    // 主修复流程
    async 开始修复() {
        console.log('🚀 开始收款同步修复流程...');
        console.log('=====================================');
        
        // 1. 诊断当前状态
        const 当前记录 = await this.诊断当前状态();
        if (!当前记录) {
            console.log('❌ 诊断失败，无法继续修复');
            return false;
        }

        // 2. 检查数据库连接
        const 数据库正常 = await this.检查数据库连接();
        if (!数据库正常) {
            console.log('❌ 数据库连接异常，无法继续修复');
            return false;
        }

        // 3. 查询当前收款数据
        const 收款数据 = await this.查询当前收款数据();
        if (收款数据 === null) {
            console.log('❌ 无法查询收款数据');
            return false;
        }

        // 4. 修复ID映射
        await this.修复ID映射();

        // 5. 强制刷新收款记录
        await this.强制刷新收款记录();

        // 6. 测试添加收款记录
        await this.测试添加收款记录();

        console.log('\n📋 修复完成！');
        console.log('修复步骤:', this.修复步骤);
        
        return true;
    }

    // 生成调试报告
    生成调试报告() {
        console.log('\n📋 ===== 调试报告 =====');
        this.调试信息.forEach(信息 => console.log(信息));
        
        if (this.修复步骤.length > 0) {
            console.log('\n🔧 执行的修复步骤:');
            this.修复步骤.forEach((步骤, 索引) => {
                console.log(`${索引 + 1}. ${步骤}`);
            });
        }
    }
}

// 创建修复器实例
const 修复器 = new 收款同步修复器();

// 导出到全局
window.收款同步修复器 = 修复器;
window.开始收款修复 = () => 修复器.开始修复();
window.生成调试报告 = () => 修复器.生成调试报告();

console.log('🔧 收款同步修复脚本已加载');
console.log('运行命令: 开始收款修复()');

// 自动开始修复
修复器.开始修复();
