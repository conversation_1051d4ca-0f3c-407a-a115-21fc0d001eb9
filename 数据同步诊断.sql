-- =====================================================
-- 数据同步诊断脚本
-- 用于检查销售记录和收款记录的数据同步问题
-- =====================================================

-- 1. 检查销售记录表结构和数据
SELECT '=== 销售记录表结构检查 ===' as section;

-- 检查sales_records表的列
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'sales_records'
ORDER BY ordinal_position;

-- 2. 检查最近的销售记录
SELECT '=== 最近的销售记录 ===' as section;

SELECT 
    id,
    customer,
    contract_amount,
    amount,
    created_at,
    created_by
FROM sales_records 
ORDER BY created_at DESC 
LIMIT 5;

-- 3. 检查收款记录表是否存在
SELECT '=== 收款记录表检查 ===' as section;

-- 检查payment_records表是否存在
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'payment_records';

-- 如果表存在，检查结构
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'payment_records'
ORDER BY ordinal_position;

-- 4. 检查收款记录数据
SELECT '=== 收款记录数据检查 ===' as section;

-- 查询所有收款记录
SELECT
    id,
    sales_record_id,
    payment_date,
    payment_amount,
    payment_method,
    created_at
FROM payment_records
ORDER BY created_at DESC
LIMIT 10;

-- 统计收款记录总数
SELECT
    COUNT(*) as total_payment_records,
    COUNT(DISTINCT sales_record_id) as unique_sales_records_with_payments
FROM payment_records;

-- 5. 检查特定销售记录的收款情况
SELECT '=== 特定销售记录收款检查 ===' as section;

-- 获取最新的一条销售记录并检查其收款情况
WITH latest_sale AS (
    SELECT id, customer, contract_amount
    FROM sales_records
    ORDER BY created_at DESC
    LIMIT 1
)
SELECT
    ls.id as sales_record_id,
    ls.customer,
    ls.contract_amount,
    COALESCE(pr.payment_count, 0) as payment_count,
    COALESCE(pr.total_payments, 0) as total_received
FROM latest_sale ls
LEFT JOIN (
    SELECT
        sales_record_id,
        COUNT(*) as payment_count,
        SUM(payment_amount) as total_payments
    FROM payment_records
    GROUP BY sales_record_id
) pr ON ls.id = pr.sales_record_id;

-- 6. 检查数据类型匹配
SELECT '=== 数据类型匹配检查 ===' as section;

-- 检查sales_records.id的数据类型
SELECT
    'sales_records.id' as field,
    data_type,
    udt_name
FROM information_schema.columns
WHERE table_name = 'sales_records' AND column_name = 'id';

-- 检查payment_records.sales_record_id的数据类型
SELECT
    'payment_records.sales_record_id' as field,
    data_type,
    udt_name
FROM information_schema.columns
WHERE table_name = 'payment_records' AND column_name = 'sales_record_id';

-- 6. 检查视图是否存在
SELECT '=== 视图检查 ===' as section;

SELECT 
    table_name as view_name,
    'VIEW' as object_type
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name IN ('payment_summary', 'payment_details_view', 'sales_records_detail')
ORDER BY table_name;

-- 7. 权限检查
SELECT '=== 权限检查 ===' as section;

SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('sales_records', 'payment_records')
ORDER BY tablename;
