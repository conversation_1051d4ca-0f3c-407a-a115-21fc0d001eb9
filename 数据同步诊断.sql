-- =====================================================
-- 数据同步诊断脚本
-- 用于检查销售记录和收款记录的数据同步问题
-- =====================================================

-- 1. 检查销售记录表结构和数据
SELECT '=== 销售记录表结构检查 ===' as section;

-- 检查sales_records表的列
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'sales_records'
ORDER BY ordinal_position;

-- 2. 检查最近的销售记录
SELECT '=== 最近的销售记录 ===' as section;

SELECT 
    id,
    customer,
    contract_amount,
    amount,
    created_at,
    created_by
FROM sales_records 
ORDER BY created_at DESC 
LIMIT 5;

-- 3. 检查收款记录表是否存在
SELECT '=== 收款记录表检查 ===' as section;

-- 检查payment_records表是否存在
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'payment_records';

-- 如果表存在，检查结构
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'payment_records'
ORDER BY ordinal_position;

-- 4. 检查收款记录数据
SELECT '=== 收款记录数据检查 ===' as section;

-- 尝试查询收款记录（如果表存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_records') THEN
        RAISE NOTICE '收款记录表存在，查询数据...';
    ELSE
        RAISE NOTICE '收款记录表不存在！';
    END IF;
END $$;

-- 5. 检查特定销售记录的收款情况
SELECT '=== 特定销售记录收款检查 ===' as section;

-- 获取最新的一条销售记录ID
WITH latest_sale AS (
    SELECT id, customer, contract_amount 
    FROM sales_records 
    ORDER BY created_at DESC 
    LIMIT 1
)
SELECT 
    ls.id as sales_record_id,
    ls.customer,
    ls.contract_amount,
    'payment_records表可能不存在或无数据' as payment_status
FROM latest_sale ls;

-- 6. 检查视图是否存在
SELECT '=== 视图检查 ===' as section;

SELECT 
    table_name as view_name,
    'VIEW' as object_type
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name IN ('payment_summary', 'payment_details_view', 'sales_records_detail')
ORDER BY table_name;

-- 7. 权限检查
SELECT '=== 权限检查 ===' as section;

SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('sales_records', 'payment_records')
ORDER BY tablename;
