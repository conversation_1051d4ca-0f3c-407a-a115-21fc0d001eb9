-- =====================================================
-- 数据库升级脚本：支持分期收款功能
-- 版本：v2.0
-- 创建日期：2025-06-30
-- 说明：为销售管理系统添加分期收款支持
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 1. 创建收款记录表（核心表）
CREATE TABLE IF NOT EXISTS payment_records (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '收款记录ID',
    sales_record_id INT NOT NULL COMMENT '关联的销售记录ID',
    payment_date DATE NOT NULL COMMENT '收款日期',
    payment_amount DECIMAL(12,2) NOT NULL COMMENT '收款金额',
    payment_method VARCHAR(50) DEFAULT '转账' COMMENT '收款方式（转账、现金、支票等）',
    payment_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'confirmed' COMMENT '收款状态',
    bank_info VARCHAR(200) COMMENT '银行信息',
    transaction_no VARCHAR(100) COMMENT '交易流水号',
    remarks TEXT COMMENT '收款备注',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人邮箱',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    
    -- 外键约束
    FOREIGN KEY (sales_record_id) REFERENCES sales_records(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_sales_record_id (sales_record_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_created_by (created_by),
    INDEX idx_payment_status (payment_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收款记录表';

-- 2. 为sales_records表添加新字段
ALTER TABLE sales_records 
ADD COLUMN IF NOT EXISTS total_received DECIMAL(12,2) DEFAULT 0 COMMENT '累计收款金额',
ADD COLUMN IF NOT EXISTS payment_status ENUM('unpaid', 'partial', 'paid', 'overpaid') DEFAULT 'unpaid' COMMENT '付款状态',
ADD COLUMN IF NOT EXISTS last_payment_date DATE COMMENT '最后收款日期',
ADD COLUMN IF NOT EXISTS payment_count INT DEFAULT 0 COMMENT '收款次数';

-- 3. 创建收款统计视图
CREATE OR REPLACE VIEW payment_summary AS
SELECT 
    sr.id as sales_record_id,
    sr.frontend_id,
    sr.customer,
    sr.salesperson,
    sr.contract_amount,
    sr.date as contract_date,
    COALESCE(SUM(pr.payment_amount), 0) as total_received,
    COUNT(pr.id) as payment_count,
    MAX(pr.payment_date) as last_payment_date,
    (sr.contract_amount - COALESCE(SUM(pr.payment_amount), 0)) as remaining_amount,
    CASE 
        WHEN COALESCE(SUM(pr.payment_amount), 0) = 0 THEN 'unpaid'
        WHEN COALESCE(SUM(pr.payment_amount), 0) < sr.contract_amount THEN 'partial'
        WHEN COALESCE(SUM(pr.payment_amount), 0) = sr.contract_amount THEN 'paid'
        ELSE 'overpaid'
    END as payment_status,
    sr.created_by,
    sr.created_at
FROM sales_records sr
LEFT JOIN payment_records pr ON sr.id = pr.sales_record_id AND pr.is_deleted = FALSE
WHERE sr.contract_amount > 0
GROUP BY sr.id, sr.frontend_id, sr.customer, sr.salesperson, sr.contract_amount, sr.date, sr.created_by, sr.created_at;

-- 4. 创建详细收款记录视图（包含所有收款明细）
CREATE OR REPLACE VIEW payment_details_view AS
SELECT 
    pr.id as payment_id,
    pr.sales_record_id,
    sr.frontend_id,
    sr.customer,
    sr.salesperson,
    sr.business_type,
    sr.contract_amount,
    sr.date as contract_date,
    pr.payment_date,
    pr.payment_amount,
    pr.payment_method,
    pr.payment_status,
    pr.bank_info,
    pr.transaction_no,
    pr.remarks as payment_remarks,
    sr.remarks as contract_remarks,
    pr.created_by as payment_created_by,
    pr.created_at as payment_created_at,
    -- 计算累计收款
    (SELECT COALESCE(SUM(p2.payment_amount), 0) 
     FROM payment_records p2 
     WHERE p2.sales_record_id = pr.sales_record_id 
     AND p2.payment_date <= pr.payment_date 
     AND p2.is_deleted = FALSE) as cumulative_received,
    -- 计算剩余金额
    (sr.contract_amount - (SELECT COALESCE(SUM(p3.payment_amount), 0) 
                          FROM payment_records p3 
                          WHERE p3.sales_record_id = pr.sales_record_id 
                          AND p3.payment_date <= pr.payment_date 
                          AND p3.is_deleted = FALSE)) as remaining_after_payment
FROM payment_records pr
JOIN sales_records sr ON pr.sales_record_id = sr.id
WHERE pr.is_deleted = FALSE
ORDER BY pr.sales_record_id, pr.payment_date;

-- 5. 创建触发器：自动更新sales_records表的统计字段
DELIMITER //

CREATE TRIGGER update_sales_record_payment_stats
AFTER INSERT ON payment_records
FOR EACH ROW
BEGIN
    UPDATE sales_records 
    SET 
        total_received = (
            SELECT COALESCE(SUM(payment_amount), 0) 
            FROM payment_records 
            WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
        ),
        payment_count = (
            SELECT COUNT(*) 
            FROM payment_records 
            WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
        ),
        last_payment_date = (
            SELECT MAX(payment_date) 
            FROM payment_records 
            WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
        )
    WHERE id = NEW.sales_record_id;
    
    -- 更新付款状态
    UPDATE sales_records 
    SET payment_status = CASE 
        WHEN total_received = 0 THEN 'unpaid'
        WHEN total_received < contract_amount THEN 'partial'
        WHEN total_received = contract_amount THEN 'paid'
        ELSE 'overpaid'
    END
    WHERE id = NEW.sales_record_id;
END//

CREATE TRIGGER update_sales_record_payment_stats_on_update
AFTER UPDATE ON payment_records
FOR EACH ROW
BEGIN
    UPDATE sales_records 
    SET 
        total_received = (
            SELECT COALESCE(SUM(payment_amount), 0) 
            FROM payment_records 
            WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
        ),
        payment_count = (
            SELECT COUNT(*) 
            FROM payment_records 
            WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
        ),
        last_payment_date = (
            SELECT MAX(payment_date) 
            FROM payment_records 
            WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
        )
    WHERE id = NEW.sales_record_id;
    
    -- 更新付款状态
    UPDATE sales_records 
    SET payment_status = CASE 
        WHEN total_received = 0 THEN 'unpaid'
        WHEN total_received < contract_amount THEN 'partial'
        WHEN total_received = contract_amount THEN 'paid'
        ELSE 'overpaid'
    END
    WHERE id = NEW.sales_record_id;
END//

CREATE TRIGGER update_sales_record_payment_stats_on_delete
AFTER UPDATE ON payment_records
FOR EACH ROW
BEGIN
    IF NEW.is_deleted = TRUE AND OLD.is_deleted = FALSE THEN
        UPDATE sales_records 
        SET 
            total_received = (
                SELECT COALESCE(SUM(payment_amount), 0) 
                FROM payment_records 
                WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
            ),
            payment_count = (
                SELECT COUNT(*) 
                FROM payment_records 
                WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
            ),
            last_payment_date = (
                SELECT MAX(payment_date) 
                FROM payment_records 
                WHERE sales_record_id = NEW.sales_record_id AND is_deleted = FALSE
            )
        WHERE id = NEW.sales_record_id;
        
        -- 更新付款状态
        UPDATE sales_records 
        SET payment_status = CASE 
            WHEN total_received = 0 THEN 'unpaid'
            WHEN total_received < contract_amount THEN 'partial'
            WHEN total_received = contract_amount THEN 'paid'
            ELSE 'overpaid'
        END
        WHERE id = NEW.sales_record_id;
    END IF;
END//

DELIMITER ;

-- 6. 数据迁移：将现有的amount字段数据迁移到payment_records表
INSERT INTO payment_records (
    sales_record_id, 
    payment_date, 
    payment_amount, 
    payment_method, 
    payment_status,
    remarks, 
    created_by, 
    created_at
)
SELECT 
    id,
    date,
    amount,
    '历史数据迁移',
    'confirmed',
    CONCAT('从原amount字段迁移: ', COALESCE(remarks, '')),
    created_by,
    created_at
FROM sales_records 
WHERE amount > 0 AND contract_amount > 0;

-- 7. 创建索引优化查询性能
CREATE INDEX idx_sales_records_payment_status ON sales_records(payment_status);
CREATE INDEX idx_sales_records_contract_amount ON sales_records(contract_amount);
CREATE INDEX idx_sales_records_total_received ON sales_records(total_received);

-- 8. 插入测试数据（可选，用于验证功能）
-- 注意：这部分在生产环境中可以注释掉
/*
INSERT INTO payment_records (sales_record_id, payment_date, payment_amount, payment_method, remarks, created_by) VALUES
(1, '2024-01-15', 5000.00, '银行转账', '首期付款', '<EMAIL>'),
(1, '2024-02-15', 3000.00, '银行转账', '二期付款', '<EMAIL>'),
(2, '2024-01-20', 8000.00, '现金', '全额付款', '<EMAIL>');
*/

-- 提交事务
COMMIT;

-- 显示升级完成信息
SELECT '数据库升级完成！支持分期收款功能已启用。' as message;

-- 验证升级结果
SELECT 
    COUNT(*) as total_sales_records,
    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_records,
    SUM(CASE WHEN payment_status = 'partial' THEN 1 ELSE 0 END) as partial_records,
    SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_records
FROM sales_records;

SELECT COUNT(*) as total_payment_records FROM payment_records WHERE is_deleted = FALSE;
