# MCP浏览器工具启动脚本
Write-Host "🚀 MCP浏览器工具启动脚本" -ForegroundColor Green
Write-Host ""

# 确保Node.js在PATH中
$env:PATH += ";C:\Program Files\nodejs"

# 检查Node.js和npm是否可用
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js或npm未找到，请确保已正确安装" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📋 可用的MCP服务器:" -ForegroundColor Yellow
Write-Host "1. Playwright MCP - 官方Playwright浏览器自动化工具"
Write-Host "2. ExecuteAutomation Playwright MCP - 增强的Playwright MCP服务器"
Write-Host "3. AgentDesk Browser Tools MCP - 浏览器工具集成"
Write-Host "4. Winds AI Frontend Development MCP - 完整的前端开发工具包"
Write-Host "5. 启动所有服务器（并行运行）"
Write-Host ""

$choice = Read-Host "请选择要启动的服务器 (1-5)"

switch ($choice) {
    "1" {
        Write-Host "🎭 启动 Playwright MCP..." -ForegroundColor Cyan
        Write-Host "端口: 默认MCP端口" -ForegroundColor Gray
        npx @playwright/mcp
    }
    "2" {
        Write-Host "🎯 启动 ExecuteAutomation Playwright MCP..." -ForegroundColor Cyan
        Write-Host "功能: Playwright自动化 + AI集成" -ForegroundColor Gray
        npx @executeautomation/playwright-mcp-server
    }
    "3" {
        Write-Host "🔧 启动 AgentDesk Browser Tools MCP..." -ForegroundColor Cyan
        Write-Host "功能: 浏览器调试工具 + Chrome扩展集成" -ForegroundColor Gray
        npx @agentdeskai/browser-tools-mcp
    }
    "4" {
        Write-Host "🌪️ 启动 Winds AI Frontend Development MCP..." -ForegroundColor Cyan
        Write-Host "功能: 完整的前端开发工具包" -ForegroundColor Gray
        npx @winds-ai/frontend-development-mcp-tools
    }
    "5" {
        Write-Host "🚀 启动所有MCP服务器..." -ForegroundColor Magenta
        Write-Host "⚠️ 这将在后台启动多个服务器进程" -ForegroundColor Yellow
        
        # 启动多个服务器
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "npx @playwright/mcp" -WindowStyle Minimized
        Start-Sleep 2
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "npx @executeautomation/playwright-mcp-server" -WindowStyle Minimized
        Start-Sleep 2
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "npx @agentdeskai/browser-tools-mcp" -WindowStyle Minimized
        Start-Sleep 2
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "npx @winds-ai/frontend-development-mcp-tools" -WindowStyle Minimized
        
        Write-Host "✅ 所有MCP服务器已在后台启动！" -ForegroundColor Green
        Write-Host "💡 检查任务栏中的PowerShell窗口" -ForegroundColor Gray
        Write-Host "🔍 使用任务管理器查看运行的进程" -ForegroundColor Gray
    }
    default {
        Write-Host "❌ 无效选择，请重新运行脚本" -ForegroundColor Red
        exit 1
    }
}

if ($choice -ne "5") {
    Write-Host ""
    Write-Host "✅ MCP服务器已启动！" -ForegroundColor Green
    Write-Host "💡 保持此窗口打开以维持服务器运行" -ForegroundColor Yellow
    Write-Host "📝 现在您可以在AI工具中连接到此MCP服务器" -ForegroundColor Gray
    Write-Host ""
    Write-Host "按任意键退出..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
