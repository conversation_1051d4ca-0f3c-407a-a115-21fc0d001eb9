// =====================================================
// 控制台日志收集器 - 捕获并显示所有控制台输出
// 在浏览器控制台中运行此脚本
// =====================================================

// 创建日志显示面板
function 创建日志面板() {
    // 检查是否已存在
    if (document.getElementById('logPanel')) {
        document.getElementById('logPanel').remove();
    }

    const panel = document.createElement('div');
    panel.id = 'logPanel';
    panel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        width: 500px;
        height: 600px;
        background: rgba(0, 0, 0, 0.9);
        color: #00ff00;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        padding: 10px;
        border-radius: 8px;
        z-index: 10000;
        overflow-y: auto;
        border: 2px solid #333;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    `;

    const header = document.createElement('div');
    header.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #333;
    `;

    const title = document.createElement('span');
    title.textContent = '🔍 控制台日志监控';
    title.style.color = '#ffff00';

    const controls = document.createElement('div');
    
    const clearBtn = document.createElement('button');
    clearBtn.textContent = '清空';
    clearBtn.style.cssText = `
        background: #ff4444;
        color: white;
        border: none;
        padding: 2px 8px;
        border-radius: 3px;
        cursor: pointer;
        margin-right: 5px;
    `;
    clearBtn.onclick = () => {
        logContent.innerHTML = '';
        logCount = 0;
    };

    const closeBtn = document.createElement('button');
    closeBtn.textContent = '✕';
    closeBtn.style.cssText = `
        background: #666;
        color: white;
        border: none;
        padding: 2px 8px;
        border-radius: 3px;
        cursor: pointer;
    `;
    closeBtn.onclick = () => panel.remove();

    const copyBtn = document.createElement('button');
    copyBtn.textContent = '复制';
    copyBtn.style.cssText = `
        background: #4444ff;
        color: white;
        border: none;
        padding: 2px 8px;
        border-radius: 3px;
        cursor: pointer;
        margin-right: 5px;
    `;
    copyBtn.onclick = () => {
        const text = logContent.innerText;
        navigator.clipboard.writeText(text).then(() => {
            copyBtn.textContent = '已复制!';
            setTimeout(() => copyBtn.textContent = '复制', 1000);
        });
    };

    controls.appendChild(copyBtn);
    controls.appendChild(clearBtn);
    controls.appendChild(closeBtn);
    
    header.appendChild(title);
    header.appendChild(controls);

    const logContent = document.createElement('div');
    logContent.id = 'logContent';
    logContent.style.cssText = `
        height: calc(100% - 40px);
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-all;
    `;

    panel.appendChild(header);
    panel.appendChild(logContent);
    document.body.appendChild(panel);

    return { panel, logContent };
}

// 日志计数器
let logCount = 0;

// 创建面板
const { panel, logContent } = 创建日志面板();

// 添加日志函数
function 添加日志(type, args, color = '#00ff00') {
    logCount++;
    const timestamp = new Date().toLocaleTimeString();
    const logLine = document.createElement('div');
    logLine.style.color = color;
    logLine.style.marginBottom = '2px';
    
    const argsStr = args.map(arg => {
        if (typeof arg === 'object') {
            try {
                return JSON.stringify(arg, null, 2);
            } catch (e) {
                return String(arg);
            }
        }
        return String(arg);
    }).join(' ');
    
    logLine.textContent = `[${timestamp}] ${type}: ${argsStr}`;
    logContent.appendChild(logLine);
    
    // 自动滚动到底部
    logContent.scrollTop = logContent.scrollHeight;
    
    // 限制日志数量
    if (logCount > 200) {
        const firstChild = logContent.firstChild;
        if (firstChild) {
            logContent.removeChild(firstChild);
            logCount--;
        }
    }
}

// 保存原始控制台方法
const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug
};

// 重写控制台方法
console.log = function(...args) {
    originalConsole.log.apply(console, args);
    添加日志('LOG', args, '#00ff00');
};

console.error = function(...args) {
    originalConsole.error.apply(console, args);
    添加日志('ERROR', args, '#ff4444');
};

console.warn = function(...args) {
    originalConsole.warn.apply(console, args);
    添加日志('WARN', args, '#ffaa00');
};

console.info = function(...args) {
    originalConsole.info.apply(console, args);
    添加日志('INFO', args, '#4444ff');
};

console.debug = function(...args) {
    originalConsole.debug.apply(console, args);
    添加日志('DEBUG', args, '#888888');
};

// 捕获未处理的错误
window.addEventListener('error', function(event) {
    添加日志('UNCAUGHT ERROR', [event.message, 'at', event.filename + ':' + event.lineno], '#ff0000');
});

// 捕获未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(event) {
    添加日志('UNHANDLED PROMISE', [event.reason], '#ff6600');
});

// 恢复控制台的函数
function 恢复控制台() {
    console.log = originalConsole.log;
    console.error = originalConsole.error;
    console.warn = originalConsole.warn;
    console.info = originalConsole.info;
    console.debug = originalConsole.debug;
    
    if (document.getElementById('logPanel')) {
        document.getElementById('logPanel').remove();
    }
    
    console.log('✅ 控制台日志监控已停止');
}

// 添加全局函数
window.恢复控制台 = 恢复控制台;

// 监控特定的销售记录相关日志
function 监控销售记录日志() {
    添加日志('MONITOR', ['🔍 开始监控销售记录相关日志...'], '#ffff00');
    
    // 监控数据加载
    const originalLoadTableData = window.loadTableData;
    if (originalLoadTableData) {
        window.loadTableData = async function(...args) {
            添加日志('SALES', ['📊 开始加载销售数据...'], '#00ffff');
            try {
                const result = await originalLoadTableData.apply(this, args);
                添加日志('SALES', [`✅ 销售数据加载完成，记录数: ${salesData?.length || 0}`], '#00ffff');
                return result;
            } catch (error) {
                添加日志('SALES', ['❌ 销售数据加载失败:', error], '#ff4444');
                throw error;
            }
        };
    }
    
    // 监控收款记录查询
    const originalLoadPaymentRecords = window.loadPaymentRecords;
    if (originalLoadPaymentRecords) {
        window.loadPaymentRecords = async function(salesRecordId, ...args) {
            添加日志('PAYMENT', [`💰 查询收款记录，销售ID: ${salesRecordId}`], '#ffaa00');
            try {
                const result = await originalLoadPaymentRecords.apply(this, [salesRecordId, ...args]);
                添加日志('PAYMENT', [`✅ 收款记录查询完成`], '#ffaa00');
                return result;
            } catch (error) {
                添加日志('PAYMENT', ['❌ 收款记录查询失败:', error], '#ff4444');
                throw error;
            }
        };
    }
}

// 启动监控
监控销售记录日志();

console.log('🚀 控制台日志收集器已启动！');
console.log('📋 功能说明:');
console.log('- 右上角面板显示所有控制台输出');
console.log('- 点击"复制"按钮可复制所有日志');
console.log('- 点击"清空"按钮清空日志');
console.log('- 运行 恢复控制台() 可停止监控');
console.log('');
console.log('🔍 现在请刷新页面或执行操作，所有日志都会显示在右上角面板中！');
