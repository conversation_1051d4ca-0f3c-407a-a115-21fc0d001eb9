<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="专业的销售记录管理与统计分析平台">
    <meta name="keywords" content="销售管理,记录系统,数据统计">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#4CAF50">
    
    <!-- 已使用本地库，无需外部CDN预连接 -->
    
    <!-- 性能优化：预加载关键资源 -->
    <link rel="preload" as="style" href="data:text/css,body{visibility:hidden}" onload="this.rel='stylesheet'">
    
    <title>销售记录管理系统</title>
    
    <!-- 性能监控：避免阻塞渲染的脚本 -->
    <script>
        // 性能优化：早期错误捕获和性能监控
        window.addEventListener('error', function(e) {
            console.warn('脚本加载错误（已忽略）:', e.filename, e.lineno, e.message);
        });
        
        // 性能优化：检测浏览器支持
        if (!window.requestAnimationFrame) {
            window.requestAnimationFrame = window.setTimeout;
        }
        if (!window.requestIdleCallback) {
            window.requestIdleCallback = function(fn, options) {
                return setTimeout(fn, (options && options.timeout) || 0);
            };
        }
        
        // 性能提示：显示加载状态
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.visibility = 'visible';
            console.log('🚀 页面渲染完成，开始初始化...');
        });
    </script>
    
    <!-- 性能优化：关键库预加载，非关键库延迟加载 -->
    <link rel="preload" href="libs/supabase.min.js" as="script">
    
    <!-- 立即加载关键库 -->
    <script src="libs/supabase.min.js"></script>
    
    <!-- 延迟加载大文件，仅在需要时加载 -->
    <script>
        // 性能优化：智能延迟加载大型库
        let xlsxLoaded = false;
        let html2canvasLoaded = false;
        let jspdfLoaded = false;
        
        // 延迟加载XLSX库（仅在导出时需要）
        function loadXLSX() {
            if (xlsxLoaded) return Promise.resolve();
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'libs/xlsx.min.js';
                script.onload = () => {
                    xlsxLoaded = true;
                    console.log('✅ XLSX库已加载');
                    resolve();
                };
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        // 延迟加载HTML2Canvas库（仅在生成图片时需要）
        function loadHTML2Canvas() {
            if (html2canvasLoaded) return Promise.resolve();
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'libs/html2canvas.min.js';
                script.onload = () => {
                    html2canvasLoaded = true;
                    console.log('✅ HTML2Canvas库已加载');
                    resolve();
                };
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        // 延迟加载jsPDF库（仅在生成PDF时需要）
        function loadJSPDF() {
            if (jspdfLoaded) return Promise.resolve();
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'libs/jspdf.min.js';
                script.onload = () => {
                    jspdfLoaded = true;
                    console.log('✅ jsPDF库已加载');
                    resolve();
                };
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        // 在页面空闲时预加载常用库
        if (window.requestIdleCallback) {
            requestIdleCallback(() => {
                // 5秒后在空闲时间预加载HTML2Canvas（最常用）
                setTimeout(() => loadHTML2Canvas(), 5000);
            });
        }
    </script>
    <style>
        /* 性能优化：关键CSS内联，压缩格式 */
        *{margin:0;padding:0;box-sizing:border-box}
        body{font:16px/1.4 'Microsoft YaHei',sans-serif;background:linear-gradient(135deg,#f8f9fa,#ffffff);min-height:100vh;padding:20px;-webkit-font-smoothing:antialiased}
        .container{max-width:1400px;margin:0 auto;background:#fff;border-radius:15px;box-shadow:0 20px 40px rgba(0,0,0,.1);overflow:hidden;will-change:transform}
        .header{background:linear-gradient(135deg,#ff4757,#ff3742);color:#fff;padding:30px;text-align:center;position:relative}
        .header h1{font-size:2.5em;margin-bottom:10px;text-shadow:2px 2px 4px rgba(0,0,0,.3)}
        .header p{font-size:1.2em;opacity:.9}

        /* 认证界面样式 */
        .auth-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .auth-content {
            background: white;
            border-radius: 16px;
            padding: 40px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: authAppear 0.5s ease-out;
        }

        @keyframes authAppear {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .auth-header h2 {
            color: #333;
            margin-bottom: 8px;
            font-size: 24px;
        }

        .auth-header p {
            color: #666;
            font-size: 14px;
        }

        .auth-form .form-group {
            margin-bottom: 20px;
        }

        .auth-form label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .auth-form input,
        .auth-form select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .auth-form input:focus,
        .auth-form select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .auth-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .auth-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .auth-switch {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        .auth-switch a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .auth-switch a:hover {
            text-decoration: underline;
        }

        .auth-message {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            display: none;
        }

        .auth-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .auth-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .user-status {
            position: absolute;
            top: 15px;
            right: 20px;
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .sync-btn {
            background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-right: 8px;
        }

        .sync-btn:hover {
            background: linear-gradient(135deg, #30d158 0%, #32d760 100%);
            transform: translateY(-1px);
        }

        .sync-btn:active {
            transform: rotate(180deg);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .auto-sync-indicator {
            display: inline-block;
            margin-left: 8px;
            font-size: 12px;
            cursor: help;
            user-select: none;
            animation: syncPulse 2s infinite ease-in-out;
        }
        
        .auto-sync-indicator.syncing {
            animation: syncPulse 0.8s infinite ease-in-out;
        }
        
        @keyframes syncPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(0.95); }
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        /* 喜报弹窗样式 - 苹果风格重新设计 */
        .celebration-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            animation: modalBackdropFadeIn 0.3s ease-out;
        }

        @keyframes modalBackdropFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .celebration-content {
            position: relative;
            background: 
                linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            margin: 2vh auto;
            padding: 0;
            border-radius: 28px;
            width: 90vw;
            max-width: 360px;
            height: auto;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 
                0 24px 48px rgba(0, 0, 0, 0.15),
                0 12px 24px rgba(0, 0, 0, 0.08),
                0 6px 12px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.08);
            animation: celebrationAppear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;
        }

        @keyframes celebrationAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .celebration-header {
            background: linear-gradient(135deg, #ff0022 0%, #fa5a5a 100%);
            color: white;
            padding: 24px 20px 28px;
            text-align: center;
            position: relative;
            overflow: hidden;
            height: auto;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            border-radius: 28px 28px 0 0;
        }

        .celebration-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .celebration-close {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
            font-weight: 600;
            width: 32px;
            height: 32px;
            border-radius: 16px;
            cursor: pointer;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .celebration-close:hover {
            background: rgba(0, 0, 0, 0.2);
            transform: scale(1.05);
        }

        .celebration-copy {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            font-weight: 600;
            width: 32px;
            height: 32px;
            border-radius: 16px;
            cursor: pointer;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .celebration-copy:hover {
            background: rgba(0, 0, 0, 0.2);
            transform: scale(1.05);
        }

        .celebration-copy:active {
            transform: scale(0.95);
        }

        .celebration-close:active {
            transform: scale(0.95);
        }

        .celebration-main-title {
            font-size: 32px;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            margin: 0 0 8px 0;
            z-index: 3;
            position: relative;
            letter-spacing: -0.5px;
            line-height: 1.1;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
        }

        .celebration-trophy {
            font-size: 22px;
            margin: 0 0 8px 0;
            z-index: 3;
            position: relative;
            animation: trophyFloat 3s ease-in-out infinite;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
        }

        @keyframes trophyFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-6px); }
        }

        .celebration-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 10px 0;
            z-index: 2;
            position: relative;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            line-height: 1.3;
            opacity: 0.9;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
        }

        .celebration-motto {
            font-size: 13px;
            font-weight: 500;
            color: white;
            margin: 0;
            z-index: 2;
            position: relative;
            letter-spacing: 0.3px;
            padding: 8px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
        }

        .celebration-body {
            padding: 16px;
            background: rgba(255, 255, 255, 0.95);
            color: #1d1d1f;
            flex: 0;
            overflow: visible;
            display: flex;
            flex-direction: column;
            gap: 12px;
            border-radius: 0 0 28px 28px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
        }

        .celebration-body::-webkit-scrollbar {
            display: none;
        }

        .celebration-time {
            text-align: center;
            font-size: 13px;
            color: #8e8e93;
            margin: 0;
            padding: 10px 16px;
            background: rgba(142, 142, 147, 0.08);
            border-radius: 12px;
            border: 1px solid rgba(142, 142, 147, 0.12);
            flex-shrink: 0;
            font-weight: 500;
        }

        .celebration-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        /* 主要信息区域 - 苹果风格 */
        .celebration-main-info {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin: 0;
        }

        .celebration-highlight-amount {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f94141 0%, #f54c4c 100%);
            border-radius: 16px;
            color: white;
            box-shadow: 0 8px 24px rgba(52, 199, 89, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .amount-label {
            font-size: 13px;
            opacity: 0.9;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .amount-value {
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            letter-spacing: -0.5px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
        }

        .celebration-key-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .key-info-item {
            text-align: center;
            padding: 12px 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .key-label {
            display: block;
            font-size: 11px;
            color: #8e8e93;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .key-value {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #1d1d1f;
            word-break: break-all;
            line-height: 1.2;
        }

        /* 垂直布局 - 苹果风格 */
        .celebration-three-columns {
            display: flex;
            flex-direction: column;
            gap: 6px;
            flex: 0;
        }

        .celebration-column {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 10px;
            border: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
        }

        .column-title {
            font-size: 12px;
            font-weight: 600;
            color: #007AFF;
            margin: 0 0 6px 0;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
        }

        /* 迷你信息网格 - 苹果风格 */
        .mini-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 6px;
        }

        .mini-info-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 4px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.04);
            text-align: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
        }

        .mini-label {
            font-size: 10px;
            color: #8e8e93;
            font-weight: 500;
            margin-bottom: 3px;
        }

        .mini-value {
            font-size: 11px;
            font-weight: 600;
            color: #1d1d1f;
            word-break: break-all;
            line-height: 1.1;
        }

        /* 统计高亮区域 - 苹果风格 */
        .stats-highlight {
            display: flex;
            align-items: stretch;
            gap: 12px;
        }

        .stat-big {
            flex: 1;
            padding: 16px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 4px;
            word-break: break-all;
            line-height: 1.1;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
        }

        .stat-desc {
            font-size: 11px;
            color: #8e8e93;
            font-weight: 500;
        }

        .stat-small-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 80px;
        }

        .stat-small {
            padding: 8px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
        }

        /* 简化统计布局 - 两列横向 */
        .stats-simple {
            display: flex;
            gap: 6px;
        }

        .stat-item {
            flex: 1;
            padding: 8px 6px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
        }

        .stat-item .stat-number {
            font-size: 15px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 2px;
            word-break: break-all;
            line-height: 1.1;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
        }

        .stat-item .stat-desc {
            font-size: 10px;
            color: #8e8e93;
            font-weight: 500;
        }

        /* 横向业绩卡片布局 */
        .performance-cards {
            display: flex;
            gap: 8px;
            flex: 0;
        }

        .performance-card {
            flex: 1;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 12px;
            border: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        }

        .card-title {
            font-size: 12px;
            font-weight: 600;
            color: #007AFF;
            margin: 0 0 8px 0;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
        }

        .card-stats {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .card-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
        }

        .card-stat-number {
            font-size: 14px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 2px;
            word-break: break-all;
            line-height: 1.1;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
        }

        .card-stat-desc {
            font-size: 10px;
            color: #8e8e93;
            font-weight: 500;
        }

        /* 9:16 弹窗响应式样式 */
        @media (max-width: 400px) {
            .celebration-content {
                width: 95vw;
                max-width: 340px;
                margin: 1vh auto;
            }
            
            .celebration-header {
                padding: 20px 20px 24px;
                min-height: 110px;
            }
            
            .celebration-main-title {
                font-size: 28px;
                letter-spacing: -0.3px;
            }
            
            .celebration-body {
                padding: 14px;
                gap: 10px;
            }
            
            .amount-value {
                font-size: 22px;
            }
            
            .mini-info-item {
                padding: 5px 3px;
            }
            
            .mini-label {
                font-size: 9px;
            }
            
            .mini-value {
                font-size: 10px;
            }
            
            .stat-item {
                padding: 7px 5px;
            }
            
            .stat-item .stat-number {
                font-size: 13px;
            }
            
            .stat-item .stat-desc {
                font-size: 9px;
            }
            
            .performance-cards {
                gap: 6px;
            }
            
            .performance-card {
                padding: 8px;
            }
            
            .card-title {
                font-size: 11px;
                margin-bottom: 6px;
            }
            
            .card-stats {
                gap: 4px;
            }
            
            .card-stat-item {
                padding: 4px;
            }
            
            .card-stat-number {
                font-size: 12px;
            }
            
            .card-stat-desc {
                font-size: 9px;
            }
        }

        @media (max-height: 700px) {
            .celebration-content {
                max-height: 85vh;
                margin: 1vh auto;
            }
            
            .celebration-header {
                min-height: 100px;
                padding: 18px 20px 22px;
            }
            
            .celebration-main-title {
                font-size: 28px;
            }
            
            .celebration-body {
                padding: 14px;
                gap: 10px;
            }
            
            .stat-item {
                padding: 6px 4px;
            }
            
            .stat-item .stat-number {
                font-size: 12px;
            }
            
            .stat-item .stat-desc {
                font-size: 9px;
            }
            
            .performance-card {
                padding: 10px;
            }
            
            .card-stat-item {
                padding: 5px;
            }
            
            .card-stat-number {
                font-size: 11px;
            }
            
            .card-stat-desc {
                font-size: 8px;
            }
        }

        @media (max-height: 600px) {
            .celebration-content {
                max-height: 80vh;
                margin: 1vh auto;
            }
            
            .celebration-header {
                min-height: 90px;
                padding: 16px 20px 20px;
            }
            
            .celebration-main-title {
                font-size: 26px;
            }
            
            .celebration-body {
                padding: 12px;
                gap: 8px;
            }
            
            .stat-item {
                padding: 5px 3px;
            }
            
            .stat-item .stat-number {
                font-size: 11px;
            }
            
            .stat-item .stat-desc {
                font-size: 8px;
            }
            
            .performance-card {
                padding: 8px;
            }
            
            .card-stats {
                gap: 3px;
            }
            
            .card-stat-item {
                padding: 3px;
            }
            
            .card-stat-number {
                font-size: 10px;
            }
            
            .card-stat-desc {
                font-size: 7px;
            }
        }

        .stat-small-num {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #34c759;
            word-break: break-all;
            line-height: 1.1;
            margin-bottom: 2px;
        }

        .stat-small-desc {
            display: block;
            font-size: 10px;
            color: #8e8e93;
            font-weight: 500;
        }

        /* 备注信息 - 苹果风格 */
        .celebration-remarks {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 16px;
            background: rgba(255, 204, 0, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 204, 0, 0.2);
            margin: 0;
        }

        .remarks-label {
            font-size: 14px;
            flex-shrink: 0;
        }

        .remarks-text {
            font-size: 12px;
            color: #1d1d1f;
            line-height: 1.3;
            word-break: break-all;
            font-weight: 500;
        }

        /* 移动端优化 - 苹果风格 */
        @media (max-width: 480px) {
            .celebration-header {
                min-height: 100px;
                padding: 16px 20px 20px;
                border-radius: 20px 20px 0 0;
            }
            
            .celebration-main-title {
                font-size: 28px;
                letter-spacing: -0.3px;
            }
            
            .celebration-trophy {
                font-size: 24px;
                margin: 0 0 8px 0;
            }
            
            .celebration-title {
                font-size: 13px;
            }
            
            .celebration-motto {
                font-size: 11px;
                padding: 5px 12px;
            }
            
            .celebration-body {
                padding: 20px;
                gap: 12px;
                border-radius: 0 0 20px 20px;
            }
            
            .celebration-close {
                width: 28px;
                height: 28px;
                font-size: 14px;
                top: 14px;
                right: 14px;
                border-radius: 14px;
            }

            .celebration-key-info {
                grid-template-columns: repeat(2, 1fr);
                gap: 6px;
            }

            .key-info-item {
                padding: 10px 6px;
            }

            .amount-value {
                font-size: 24px;
            }

            .stat-number {
                font-size: 18px;
            }
            
            .performance-cards {
                gap: 6px;
            }
            
            .performance-card {
                padding: 8px;
            }
            
            .card-title {
                font-size: 10px;
                margin-bottom: 4px;
            }
            
            .card-stats {
                gap: 3px;
            }
            
            .card-stat-item {
                padding: 3px;
            }
            
            .card-stat-number {
                font-size: 11px;
            }
            
            .card-stat-desc {
                font-size: 8px;
            }
        }

        /* 性能优化 */
        .celebration-modal * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 滚动条美化 - 苹果风格 */
        .celebration-body::-webkit-scrollbar {
            width: 4px;
        }

        .celebration-body::-webkit-scrollbar-track {
            background: transparent;
        }

        .celebration-body::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 2px;
        }

        .celebration-body::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        .celebration-footer {
            padding: 8px 15px;
            text-align: center;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            flex-shrink: 0;
        }

        .celebration-close {
            background: linear-gradient(135deg, #FF6B6B, #FF4757);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .celebration-close:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .celebration-fireworks {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 20px;
            animation: fireworks 2s infinite;
        }

        @keyframes fireworks {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }



        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 100%;
            overflow: hidden;
        }

        .left-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            width: 350px;
            max-width: 350px;
        }

        .right-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            min-width: 0;
            overflow: hidden;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            border-color: #4CAF50;
            outline: none;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            margin: 10px 5px 10px 0;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            white-space: nowrap;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        /* 其他地方使用的次要按钮样式 */
        .btn.btn-secondary:not(.action-buttons .btn-secondary) {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .statistics {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #ff4757;
        }

        .statistics h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-weight: bold;
            color: #666;
        }

        .stat-value {
            color: #ff4757;
            font-weight: bold;
        }

        .table-container {
            background: white;
            border-radius: 0 0 10px 10px;
            overflow-x: auto;
            overflow-y: auto;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-height: 600px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 900px;
            table-layout: fixed;
        }

        th {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            font-size: 14px;
        }

        /* 设置各列的固定宽度 */
        th:nth-child(1), td:nth-child(1) { width: 90px; }  /* 收款日期 */
        th:nth-child(2), td:nth-child(2) { width: 70px; }  /* 客户名 */
        th:nth-child(3), td:nth-child(3) { width: 80px; }  /* 客户来源 */
        th:nth-child(4), td:nth-child(4) { width: 80px; }  /* 业务类型 */
        th:nth-child(5), td:nth-child(5) { width: 90px; }  /* 收入金额 */
        th:nth-child(6), td:nth-child(6) { width: 70px; }  /* 收入类型 */
        th:nth-child(7), td:nth-child(7) { width: 70px; }  /* 销售人员 */
        th:nth-child(8), td:nth-child(8) { width: 90px; }  /* 合同金额 */
        th:nth-child(9), td:nth-child(9) { width: 100px; } /* 备注 */
        th:nth-child(10), td:nth-child(10) { width: 60px; } /* 操作 */

        td {
            padding: 10px 8px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
            vertical-align: middle;
        }

        /* 备注列特殊处理 - 鼠标悬停显示完整内容 */
        td:nth-child(9) {
            position: relative;
            cursor: pointer;
        }

        td:nth-child(9):hover {
            overflow: visible;
            white-space: normal;
            background: #fff3cd;
            z-index: 5;
            border: 1px solid #ffc107;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            max-width: 200px;
            min-width: 120px;
        }

        /* 删除按钮样式优化 */
        .delete-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }

        .delete-btn:hover {
            background: #d32f2f;
            transform: scale(1.05);
        }

        /* 收款按钮样式 */
        .payment-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .payment-btn:hover {
            background: #45a049;
            transform: scale(1.05);
        }

        /* 收款状态样式 */
        .payment-status {
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }

        .status-unpaid {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }

        .status-partial {
            background: #fff3e0;
            color: #ef6c00;
            border: 1px solid #ffcc02;
        }

        .status-paid {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }

        .status-overpaid {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #bbdefb;
        }

        .status-no-contract {
            background: #f5f5f5;
            color: #757575;
            border: 1px solid #e0e0e0;
        }

        /* 表格工具栏样式 */
        .table-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #e9ecef;
            gap: 15px;
        }

        .table-info {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .table-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            background: #ff4757;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-small:hover {
            background: #ff3742;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* 搜索功能样式 */
        .table-search {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #customerSearchInput {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 180px;
            outline: none;
            transition: all 0.3s ease;
        }

        #customerSearchInput:focus {
            border-color: #ff4757;
            box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.2);
        }

        .search-btn {
            background: #4CAF50 !important;
        }

        .search-btn:hover {
            background: #45a049 !important;
        }

        .clear-search-btn {
            background: #666 !important;
        }

        .clear-search-btn:hover {
            background: #555 !important;
        }

        /* 搜索高亮样式 */
        .search-highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: bold;
        }

        .search-no-results {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .table-toolbar {
                flex-direction: column;
                gap: 10px;
                align-items: center;
            }

            .table-search {
                justify-content: center;
            }

            #customerSearchInput {
                width: 200px;
            }

            .table-info {
                text-align: center;
            }
        }

        /* 客户来源统计分析样式 */
        .customer-source-analysis {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
            padding: 10px;
        }

        .source-stat-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .source-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff4757, #ff6b7a);
        }

        .source-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.1);
        }

        .source-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .source-title {
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .source-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .source-percentage {
            font-size: 10px;
            color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
            display: inline-block;
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .customer-source-analysis {
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
            }
        }

        @media (max-width: 768px) {
            .customer-source-analysis {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
            
            .source-stat-card {
                padding: 12px;
            }
            
            .source-number {
                font-size: 20px;
            }
        }

        /* 未收款统计样式 */
        .unpaid-stats {
            max-height: 300px;
            overflow-y: auto;
        }

        .unpaid-summary {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            text-align: center;
        }

        .unpaid-total {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .unpaid-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .unpaid-detail {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            border-left: 4px solid #FF9800;
        }

        .unpaid-customer {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .unpaid-amounts {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .contract-amount {
            color: #666;
        }

        .received-amount {
            color: #4CAF50;
        }

        .unpaid-amount {
            color: #FF9800;
            font-weight: bold;
        }



        tr:hover td {
            background-color: #f5f5f5;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .daily-summary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .daily-summary h3 {
            margin-bottom: 15px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }

        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: #f8f9fa;
            color: #666;
            font-size: 16px;
            transition: all 0.3s;
        }

        .tab.active {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
        }

        .tab.protected-tab {
            position: relative;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-left: 3px solid #ff4757;
        }

        .tab.protected-tab:hover {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 71, 87, 0.3);
        }

        .tab.protected-tab.active {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            border-left: 3px solid #ff3742;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .export-section {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-top: 20px;
        }

        /* 统计网格样式 */
        .statistics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 300px 1fr;
                gap: 15px;
                padding: 15px;
            }
            
            .left-panel {
                width: 300px;
                max-width: 300px;
                padding: 15px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 10px;
            }
            
            .left-panel {
                width: 100%;
                max-width: 100%;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            table {
                font-size: 12px;
                min-width: 750px;
            }
            
            th {
                padding: 8px 4px;
                font-size: 12px;
            }
            
            td {
                padding: 8px 4px;
                font-size: 11px;
            }
            
            /* 移动端各列宽度调整 */
            th:nth-child(1), td:nth-child(1) { width: 75px; }
            th:nth-child(2), td:nth-child(2) { width: 60px; }
            th:nth-child(3), td:nth-child(3) { width: 70px; }
            th:nth-child(4), td:nth-child(4) { width: 70px; }
            th:nth-child(5), td:nth-child(5) { width: 80px; }
            th:nth-child(6), td:nth-child(6) { width: 60px; }
            th:nth-child(7), td:nth-child(7) { width: 60px; }
            th:nth-child(8), td:nth-child(8) { width: 80px; }
            th:nth-child(9), td:nth-child(9) { width: 85px; }
            th:nth-child(10), td:nth-child(10) { width: 50px; }
            
            .statistics-grid {
                grid-template-columns: 1fr !important;
                gap: 15px;
            }
        }

        .highlight {
            background-color: #fff3cd !important;
            border-left: 4px solid #ffc107;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .settings-item {
            background: white;
            padding: 15px;
            margin: 8px 0;
            border-radius: 8px;
            border-left: 4px solid #ff4757;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .settings-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }

        .settings-item-content {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .settings-item-actions {
            display: flex;
            gap: 8px;
        }

        .settings-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .settings-section h3 {
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #ff4757;
            color: #333;
            font-size: 1.2em;
        }

        .add-form {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .add-form input, .add-form select {
            flex: 1;
            min-width: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .add-form button {
            white-space: nowrap;
            padding: 10px 20px;
            font-size: 14px;
        }

        .items-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            font-style: italic;
        }

        .item-index {
            background: #e9ecef;
            color: #666;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 10px;
            min-width: 24px;
            text-align: center;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .ladder-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ladder-item input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        /* 重复的 .btn-small 定义已删除，使用统一定义 */

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .action-buttons .btn {
            flex: 1;
            min-width: 130px;
            margin: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 15px;
        }

        /* 主要按钮 - 红色渐变，最重要 */
        .btn-primary {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
        }

        /* 次要按钮 - 中等色调 */
        .btn-secondary {
            background: linear-gradient(135deg, #81C784 0%, #66BB6A 100%);
            color: white;
            font-weight: 500;
            box-shadow: 0 3px 8px rgba(129, 199, 132, 0.25);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(129, 199, 132, 0.35);
        }

        /* 第三级按钮 - 最浅色调 */
        .btn-tertiary {
            background: linear-gradient(135deg, #A5D6A7 0%, #8BC34A 100%);
            color: #2E7D32;
            font-weight: normal;
            box-shadow: 0 2px 6px rgba(165, 214, 167, 0.2);
        }

        .btn-tertiary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(165, 214, 167, 0.3);
            background: linear-gradient(135deg, #8BC34A 0%, #7CB342 100%);
            color: white;
        }

        /* 导出按钮样式 */
        .export-buttons .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 14px;
            padding: 12px 16px;
        }

        /* 统计分析模块样式 */
        .analytics-dashboard {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(255, 71, 87, 0.3);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .dashboard-header h3 {
            margin: 0;
            font-size: 1.4em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .time-selector select {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
        }

        .time-selector select option {
            background: #333;
            color: white;
        }

        /* KPI 卡片网格 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        @media (max-width: 900px) {
            .kpi-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .kpi-grid {
                grid-template-columns: 1fr;
            }
        }

        .kpi-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .kpi-card:hover {
            transform: translateY(-3px);
            background: rgba(255,255,255,0.2);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .kpi-icon {
            font-size: 2.5em;
            opacity: 0.9;
        }

        .kpi-content {
            flex: 1;
        }

        .kpi-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .kpi-value {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .kpi-change {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
            background: rgba(76, 175, 80, 0.3);
            display: inline-block;
        }

        .kpi-change.negative {
            background: rgba(244, 67, 54, 0.3);
        }

        /* 分析矩阵 */
        .analytics-matrix {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 图表网格布局 */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .chart-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }

        .chart-card.wide {
            grid-column: span 2;
        }

        .chart-header {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .chart-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
        }

        .chart-header h4 {
            margin: 0;
            font-size: 15px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .chart-badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .chart-body {
            padding: 20px;
            position: relative;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .analysis-row {
            width: 100%;
        }

        .analysis-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .analysis-card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .analysis-card.insight {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .card-header {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .analysis-card.insight .card-header {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }

        .card-icon {
            font-size: 1.2em;
            margin-right: 10px;
        }

        .card-header h4 {
            margin: 0;
            font-size: 1.1em;
            display: flex;
            align-items: center;
        }

        .card-toggle {
            cursor: pointer;
            font-size: 1.5em;
            font-weight: bold;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .card-toggle:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .card-content {
            padding: 20px;
            transition: all 0.3s ease;
        }

        .card-content.collapsed {
            display: none;
        }

        /* 趋势分析网格 */
        .trend-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .trend-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .trend-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .trend-chart {
            min-height: 120px;
        }

        /* 业务结构网格 */
        .business-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .business-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .business-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            font-size: 14px;
            text-align: center;
            padding-bottom: 8px;
            border-bottom: 2px solid #ff4757;
        }

        .business-stats {
            max-height: 200px;
            overflow-y: auto;
        }

        /* 团队分析 */
        .team-summary {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #ff4757;
        }

        .team-ranking {
            display: grid;
            gap: 10px;
        }

        .team-member {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .team-member:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .member-rank {
            background: #ff4757;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .member-info {
            flex: 1;
            margin-left: 12px;
        }

        .member-name {
            font-weight: bold;
            color: #333;
        }

        .member-performance {
            font-size: 12px;
            color: #666;
        }

        .member-amount {
            font-weight: bold;
            color: #ff4757;
            font-size: 16px;
        }

        /* 客户分析网格 */
        .customer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .customer-metrics, .customer-segments, .top-customers {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        /* 智能洞察 */
        .insights-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .insight-item {
            background: linear-gradient(90deg, #fff3cd 0%, #ffeaa7 100%);
            border-left: 4px solid #f39c12;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .insight-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .insight-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #f39c12, #e67e22);
        }

        .insight-icon {
            font-size: 1.2em;
            margin-right: 8px;
        }

        .insight-text {
            font-size: 14px;
            line-height: 1.4;
            color: #333;
        }

        /* 紧凑的统计项 */
        .stat-item-compact {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 3px 0;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #4CAF50;
            transition: all 0.3s ease;
        }

        .stat-item-compact:hover {
            background: #f0f8f0;
            transform: scale(1.02);
        }

        .stat-label-compact {
            font-size: 13px;
            color: #555;
            font-weight: 500;
        }

        .stat-value-compact {
            font-size: 13px;
            color: #4CAF50;
            font-weight: bold;
        }

        .stat-percentage {
            font-size: 11px;
            color: #666;
            margin-left: 5px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .action-buttons .btn {
                flex: none;
                min-width: 100%;
                margin-bottom: 8px;
            }
            
            .export-buttons {
                grid-template-columns: 1fr !important;
            }

            .kpi-grid {
                grid-template-columns: 1fr 1fr;
            }

            .trend-grid {
                grid-template-columns: 1fr;
            }

            .business-grid {
                grid-template-columns: 1fr;
            }

            .customer-grid {
                grid-template-columns: 1fr;
            }

            .kpi-card {
                padding: 15px;
            }

            .kpi-icon {
                font-size: 2em;
            }

            .kpi-value {
                font-size: 1.4em;
            }
        }

        @media (max-width: 480px) {
            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .analytics-dashboard {
                padding: 15px;
            }

            .card-content {
                padding: 15px;
            }
        }
        
        /* 新增样式 */
        .insight-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 8px;
            border-left: 3px solid #28a745;
            transition: all 0.2s ease;
        }
        
        .insight-item:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateX(3px);
        }
        
        .insight-icon {
            font-size: 16px;
            margin-right: 10px;
            margin-top: 1px;
            flex-shrink: 0;
        }
        
        .insight-text {
            flex: 1;
            font-size: 13px;
            line-height: 1.4;
            color: #495057;
        }
        
        .team-member {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }
        
        .team-member:hover {
            background: #e9ecef;
            transform: translateX(2px);
        }
        
        .member-rank {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            margin-right: 12px;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-weight: 600;
            color: #343a40;
            font-size: 14px;
        }
        
        .member-performance {
            font-size: 11px;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .member-amount {
            font-weight: 600;
            color: #28a745;
            font-size: 14px;
        }
        
        /* 图表容器样式 */
        .chart-container {
            position: relative;
        }
        
        .chart-container.wide {
            grid-column: span 2;
        }
        
        /* 饼图样式 */
        .pie-chart {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            position: relative;
            margin: 0 auto 15px;
            background: conic-gradient(from 0deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: rotate(-90deg);
        }
        
        .chart-legend.compact {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 6px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 11px;
            padding: 4px 6px;
            background: #f8f9fa;
            border-radius: 6px;
            transition: all 0.2s ease;
        }
        
        .legend-item:hover {
            background: #e9ecef;
            transform: scale(1.02);
        }
        
        .legend-color {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
            flex-shrink: 0;
        }
        
        .legend-label {
            flex: 1;
            font-weight: 500;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .legend-value {
            font-weight: 600;
            color: #4CAF50;
            font-size: 10px;
        }
        
        /* 环形图样式 */
        .donut-chart-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .donut-chart {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            position: relative;
            background: conic-gradient(from 0deg, 
                #FF6B6B 0deg 72deg,
                #4ECDC4 72deg 144deg,
                #45B7D1 144deg 216deg,
                #96CEB4 216deg 288deg,
                #FFEAA7 288deg 360deg
            );
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: rotate(-90deg);
        }
        
        .donut-chart::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70px;
            height: 70px;
            background: white;
            border-radius: 50%;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .donut-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(90deg);
            text-align: center;
            z-index: 2;
        }
        
        .center-value {
            font-size: 16px;
            font-weight: 700;
            color: #4CAF50;
            margin-bottom: 2px;
        }
        
        .center-label {
            font-size: 10px;
            color: #666;
            font-weight: 500;
        }
        
        /* 业绩排名样式 */
        .performance-ranking {
            padding: 10px 0;
        }
        
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #ddd;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .ranking-item:hover {
            background: #f0f7ff;
            transform: translateX(2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .ranking-item:nth-child(1) {
            border-left-color: #ff4757;
            background: linear-gradient(135deg, #fff5f5, #f8f9fa);
        }
        
        .ranking-item:nth-child(2) {
            border-left-color: #45B7D1;
            background: linear-gradient(135deg, #f0f8ff, #f8f9fa);
        }
        
        .ranking-item:nth-child(3) {
            border-left-color: #4ECDC4;
            background: linear-gradient(135deg, #f0fffe, #f8f9fa);
        }
        
        .ranking-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .ranking-item:nth-child(2) .ranking-number {
            background: linear-gradient(135deg, #45B7D1, #2196F3);
        }
        
        .ranking-item:nth-child(3) .ranking-number {
            background: linear-gradient(135deg, #4ECDC4, #26A69A);
        }
        
        .ranking-item:nth-child(n+4) .ranking-number {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }
        
        .ranking-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
        }
        
        .ranking-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
            min-width: 60px;
        }
        
        .ranking-stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .ranking-amount {
            font-weight: bold;
            color: #ff4757;
            font-size: 16px;
        }
        
        .ranking-count {
            color: #666;
            font-size: 14px;
            background: rgba(255,71,87,0.1);
            padding: 4px 10px;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .ranking-empty {
            text-align: center;
            color: #999;
            padding: 40px 20px;
            font-size: 14px;
        }
        
        /* 阶梯图样式 */
        .step-chart {
            height: 130px;
            display: flex;
            align-items: end;
            gap: 3px;
            padding: 10px;
            justify-content: center;
        }
        
        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 25px;
        }
        
        .step-bar {
            width: 100%;
            background: linear-gradient(to top, #FF9800, #FFC107);
            border-radius: 2px 2px 0 0;
            position: relative;
            transition: all 0.3s ease;
            min-height: 10px;
            box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
        }
        
        .step-bar:nth-child(odd) {
            background: linear-gradient(to top, #2196F3, #64B5F6);
            box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
        }
        
        .step-bar:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 10px rgba(255, 152, 0, 0.4);
        }
        
        .step-label {
            margin-top: 4px;
            font-size: 9px;
            color: #666;
            text-align: center;
            font-weight: 500;
            max-width: 30px;
            line-height: 1.1;
        }
        
        /* 树状图样式 */
        .treemap-container {
            padding: 10px;
        }
        
        .treemap-chart {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 4px;
            height: 180px;
        }
        
        .treemap-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            font-size: 11px;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .treemap-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .treemap-item:nth-child(2) { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .treemap-item:nth-child(3) { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .treemap-item:nth-child(4) { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .treemap-item:nth-child(5) { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
        
        .treemap-label {
            font-size: 10px;
            margin-bottom: 2px;
        }
        
        .treemap-value {
            font-size: 12px;
            font-weight: bold;
        }
        
        

        
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 15px;
            }
            
            .chart-card.wide {
                grid-column: span 1;
            }
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .chart-body {
                padding: 15px;
                min-height: 140px;
            }
            
            .chart-header {
                padding: 12px 16px;
            }
            
            .chart-header h4 {
                font-size: 14px;
            }
            
            .pie-chart, .donut-chart {
                width: 120px;
            }
            
            /* 移动端业绩排名样式 */
            .ranking-item {
                padding: 10px 12px;
                margin-bottom: 6px;
            }
            
            .ranking-number {
                width: 26px;
                height: 26px;
                font-size: 12px;
                margin-right: 12px;
            }
            
            .ranking-info {
                gap: 10px;
                flex-direction: column;
                align-items: flex-start;
            }
            
            .ranking-name {
                font-size: 14px;
                min-width: auto;
            }
            
            .ranking-stats {
                gap: 15px;
                width: 100%;
                justify-content: space-between;
            }
            
            .ranking-amount {
                font-size: 14px;
            }
            
            .ranking-count {
                font-size: 12px;
            }
            
            .pie-chart, .donut-chart {
                height: 120px;
            }
            
            .bar-chart {
                height: 120px;
                gap: 8px;
            }
            
            .step-chart {
                height: 100px;
            }
            
            .legend-item {
                font-size: 10px;
                padding: 3px 5px;
            }
        }

        /* 加载指示器样式 */
        .loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-content h3 {
            margin: 20px 0 10px 0;
            font-size: 24px;
            font-weight: 600;
        }

        .loading-content p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>🏢 销售管理系统</h3>
            <p>正在初始化，请稍候...</p>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div id="authModal" class="auth-modal" style="display: none;">
        <div class="auth-content">
            <div class="auth-header">
                <h2>🏢 销售管理系统</h2>
                <p>请登录您的账户</p>
            </div>
            
            <!-- 登录表单 -->
            <div id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="loginEmail">邮箱地址</label>
                    <input type="email" id="loginEmail" placeholder="请输入邮箱" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">密码</label>
                    <input type="password" id="loginPassword" placeholder="请输入密码" required>
                </div>
                <button type="button" onclick="handleLogin()" class="auth-btn">登录</button>
                <p class="auth-switch">
                    还没有账户？<a href="#" onclick="showRegisterForm()">立即注册</a>
                </p>
            </div>
            
            <!-- 注册表单 -->
            <div id="registerForm" class="auth-form" style="display: none;">
                <div class="form-group">
                    <label for="registerEmail">邮箱地址</label>
                    <input type="email" id="registerEmail" placeholder="请输入邮箱" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">密码</label>
                    <input type="password" id="registerPassword" placeholder="请输入密码（至少6位）" required>
                </div>
                <div class="form-group">
                    <label for="registerName">姓名</label>
                    <input type="text" id="registerName" placeholder="请输入您的姓名" required>
                </div>
                <div class="form-group">
                    <label for="registerRole">角色</label>
                    <select id="registerRole" required>
                        <option value="sales">销售人员</option>
                    </select>
                </div>
                <button type="button" onclick="handleRegister()" class="auth-btn">注册</button>
                <p class="auth-switch">
                    已有账户？<a href="#" onclick="showLoginForm()">立即登录</a>
                </p>
            </div>
            
            <div id="authMessage" class="auth-message"></div>
        </div>
    </div>

    <div class="container" id="mainContainer" style="display: none;">
        <div class="header">
            <h1>🏢 销售记录管理系统</h1>
            <p>专业的销售数据管理与统计分析平台</p>
            <div class="user-status" id="userStatus">
                <span id="currentUser">加载中...</span>
                                    <button class="sync-btn" onclick="syncWithDatabase()" title="手动同步云端数据">🔄</button>
                    <span class="auto-sync-indicator" id="autoSyncIndicator" title="自动同步状态">🟢</span>
                <button class="logout-btn" onclick="handleLogout()">退出登录</button>
            </div>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <h2>📝 录入销售数据</h2>
                <form id="salesForm">
                    <div class="form-group">
                        <label for="date">收款日期:</label>
                        <input type="date" id="date" required>
                    </div>

                    <div class="form-group">
                        <label for="customer">客户名:</label>
                        <input type="text" id="customer" placeholder="请输入客户姓名" required>
                    </div>

                    <div class="form-group">
                        <label for="customerSource">客户来源:</label>
                        <select id="customerSource" required>
                            <option value="">请选择客户来源</option>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="businessType">业务类型:</label>
                        <select id="businessType" required>
                            <option value="">请选择业务类型</option>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="amount">收入金额:</label>
                        <input type="number" id="amount" placeholder="请输入金额" step="0.01" required>
                    </div>

                    <div class="form-group">
                        <label for="incomeType">收入类型:</label>
                        <select id="incomeType" required>
                            <option value="">请选择收入类型</option>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="salesperson">销售人员:</label>
                        <select id="salesperson" required>
                            <option value="">请选择销售人员</option>
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="contractAmount">合同金额:</label>
                        <input type="number" id="contractAmount" placeholder="请输入合同金额" step="0.01">
                    </div>

                    <div class="form-group">
                        <label for="remarks">备注:</label>
                        <input type="text" id="remarks" placeholder="请输入备注信息">
                    </div>

                    <div class="action-buttons">
                        <button type="submit" class="btn btn-primary">
                            <span>➕</span>
                            <span>添加记录</span>
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="generateDailySummary()">
                            <span>📊</span>
                            <span>生成日报</span>
                        </button>
                        <button type="button" class="btn btn-tertiary" onclick="clearAllData()">
                            <span>🗑️</span>
                            <span>清空数据</span>
                        </button>
                    </div>
                </form>

                <div class="daily-summary" id="dailySummary" style="display:none;">
                    <h3>📈 今日入账统计</h3>
                    <div id="summaryContent"></div>
                    <button class="btn" onclick="copyToClipboard()">
                        <span>📋</span>
                        <span>复制到剪贴板</span>
                    </button>
                </div>
            </div>

            <div class="right-panel">
                <div class="tabs">
                    <button class="tab active" onclick="showTab('records')">📋 销售记录</button>
                    <button class="tab protected-tab" onclick="showTab('statistics')">📊 统计分析 🔒</button>
                    <button class="tab protected-tab" onclick="showTab('salary')">💰 工资计算 🔒</button>
                    <button class="tab protected-tab" onclick="showTab('settings')">⚙️ 系统设置 🔒</button>
                </div>

                <div id="records" class="tab-content active">
                    <div class="statistics">
                        <h3>📈 实时统计</h3>
                        <div class="stat-item">
                            <span class="stat-label">今日总入账:</span>
                            <span class="stat-value" id="todayTotal">¥0.00</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">本月总入账:</span>
                            <span class="stat-value" id="monthTotal">¥0.00</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">记录总数:</span>
                            <span class="stat-value" id="recordCount">0</span>
                        </div>
                    </div>

                    <!-- 表格工具栏 -->
                    <div class="table-toolbar">
                        <div class="table-info">
                            <span id="tableRecordCount">共 0 条记录</span>
                        </div>
                        <div class="table-search">
                            <input type="text" id="customerSearchInput" placeholder="搜索客户名..." title="输入客户名进行搜索">
                            <button class="btn-small search-btn" onclick="searchCustomer()" title="搜索客户">
                                🔍 搜索
                            </button>
                            <button class="btn-small clear-search-btn" onclick="clearSearch()" title="清除搜索" style="display: none;">
                                ❌ 清除
                            </button>
                        </div>

                    </div>

                    <div class="table-container">
                        <table id="salesTable">
                            <thead>
                                <tr>
                                    <th>收款日期</th>
                                    <th>客户名</th>
                                    <th>客户来源</th>
                                    <th>业务类型</th>
                                    <th>收入金额</th>
                                    <th>收入类型</th>
                                    <th>销售人员</th>
                                    <th>合同金额</th>
                                    <th>收款状态</th>
                                    <th>备注</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="salesTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="statistics" class="tab-content">
                    <!-- 核心指标仪表板 -->
                    <div class="analytics-dashboard">
                        <div class="dashboard-header">
                            <h3>📊 核心业绩指标</h3>
                            <div class="time-selector">
                                <select id="analyticsPeriod" onchange="updateAnalyticsPeriod()">
                                    <option value="month">本月</option>
                                    <option value="quarter">本季度</option>
                                    <option value="year">本年度</option>
                                    <option value="all">全部</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 关键指标卡片 -->
                        <div class="kpi-grid">
                            <div class="kpi-card">
                                <div class="kpi-icon">💰</div>
                                <div class="kpi-content">
                                    <div class="kpi-label">总收入</div>
                                    <div class="kpi-value" id="kpiTotalRevenue">¥0</div>
                                    <div class="kpi-change" id="kpiRevenueChange">+0%</div>
                                </div>
                            </div>
                            
                            <div class="kpi-card">
                                <div class="kpi-icon">📈</div>
                                <div class="kpi-content">
                                    <div class="kpi-label">成交笔数</div>
                                    <div class="kpi-value" id="kpiTotalDeals">0</div>
                                    <div class="kpi-change" id="kpiDealsChange">+0%</div>
                                </div>
                            </div>
                            
                            <div class="kpi-card">
                                <div class="kpi-icon">👥</div>
                                <div class="kpi-content">
                                    <div class="kpi-label">活跃销售</div>
                                    <div class="kpi-value" id="kpiActiveSales">0人</div>
                                    <div class="kpi-change" id="kpiSalesChange">+0%</div>
                                </div>
                            </div>
                            
                            <div class="kpi-card">
                                <div class="kpi-icon">🎯</div>
                                <div class="kpi-content">
                                    <div class="kpi-label">线索成交</div>
                                    <div class="kpi-value" id="kpiLeadConversion">0笔</div>
                                    <div class="kpi-change" id="kpiLeadChange">+0%</div>
                                </div>
                            </div>
                            
                            <div class="kpi-card">
                                <div class="kpi-icon">💳</div>
                                <div class="kpi-content">
                                    <div class="kpi-label">欠款总金额</div>
                                    <div class="kpi-value" id="kpiUnpaidAmount">¥0</div>
                                    <div class="kpi-change" id="kpiUnpaidAmountChange">+0%</div>
                                </div>
                            </div>
                            
                            <div class="kpi-card">
                                <div class="kpi-icon">📋</div>
                                <div class="kpi-content">
                                    <div class="kpi-label">欠款数量</div>
                                    <div class="kpi-value" id="kpiUnpaidCount">0笔</div>
                                    <div class="kpi-change" id="kpiUnpaidCountChange">+0%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析模块矩阵 -->
                    <div class="analytics-matrix">
                        <!-- 趋势分析 -->
                        <div class="analysis-row">
                            <div class="analysis-card compact">
                                <div class="card-header">
                                    <span class="card-icon">📈</span>
                                    <h4>趋势分析</h4>
                                    <div class="card-toggle" onclick="toggleCardContent('trends')">−</div>
                                </div>
                                <div class="card-content" id="trendsContent">
                                    <div class="trend-grid">
                                        <div class="trend-item">
                                            <div class="trend-title">月度趋势</div>
                                            <div class="trend-chart" id="monthlyTrends"></div>
                                        </div>
                                        <div class="trend-item">
                                            <div class="trend-title">周度表现</div>
                                            <div class="trend-chart" id="weeklyTrends"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- 图表分析区域 -->
                        <div class="charts-grid">
                            <!-- 第一行：饼图和环形图 -->
                            <div class="chart-card pie-chart-card">
                                <div class="chart-header">
                                    <h4>🍰 业务类型分布</h4>
                                    <span class="chart-badge">饼图</span>
                                </div>
                                <div class="chart-body">
                                    <div class="pie-chart" id="businessTypePieChart"></div>
                                    <div class="chart-legend compact" id="pieChartLegend"></div>
                                </div>
                            </div>

                            <div class="chart-card donut-chart-card wide">
                                <div class="chart-header">
                                    <h4>👥 客户来源统计分析</h4>
                                    <span class="chart-badge">来源分布</span>
                                </div>
                                <div class="chart-body">
                                    <div class="customer-source-analysis" id="customerSourceAnalysis">
                                        <!-- 客户来源统计分析将在这里显示 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：业绩排名 -->
                            <div class="chart-card bar-chart-card wide">
                                <div class="chart-header">
                                    <h4>🏆 业绩排名</h4>
                                    <span class="chart-badge">排行榜</span>
                                </div>
                                <div class="chart-body">
                                    <div class="performance-ranking" id="teamPerformanceRanking"></div>
                                </div>
                            </div>

                            <!-- 第三行：阶梯图和树状图 -->
                            <div class="chart-card step-chart-card">
                                <div class="chart-header">
                                    <h4>💰 未收款统计</h4>
                                    <span class="chart-badge">欠款分析</span>
                                </div>
                                <div class="chart-body">
                                    <div class="unpaid-stats" id="unpaidStats">
                                        <!-- 未收款统计将在这里显示 -->
                                    </div>
                                </div>
                            </div>


                        </div>

                        <!-- 智能洞察 -->
                        <div class="analysis-row">
                            <div class="analysis-card insight">
                                <div class="card-header">
                                    <span class="card-icon">🔍</span>
                                    <h4>智能洞察</h4>
                                    <div class="card-toggle" onclick="toggleCardContent('insights')">−</div>
                                </div>
                                <div class="card-content" id="insightsContent">
                                    <div class="insights-list" id="smartInsights"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="salary" class="tab-content">
                    <div class="statistics">
                        <h3>💰 工资计算详情</h3>
                        <div id="salaryDetails"></div>
                    </div>
                </div>

                <div id="settings" class="tab-content">
                    <!-- 业务类型管理 -->
                    <div class="settings-section">
                        <h3>📝 业务类型管理</h3>
                        <div class="add-form">
                            <input type="text" id="newBusinessType" placeholder="请输入新的业务类型">
                            <button class="btn" onclick="addBusinessType()">
                                <span>➕</span>
                                <span>添加</span>
                            </button>
                        </div>
                        <div class="items-list" id="businessTypesList"></div>
                    </div>

                    <!-- 收入类型管理 -->
                    <div class="settings-section">
                        <h3>💼 收入类型管理</h3>
                        <div class="add-form">
                            <input type="text" id="newIncomeType" placeholder="请输入新的收入类型">
                            <button class="btn" onclick="addIncomeType()">
                                <span>➕</span>
                                <span>添加</span>
                            </button>
                        </div>
                        <div class="items-list" id="incomeTypesList"></div>
                    </div>

                    <!-- 客户来源管理 -->
                    <div class="settings-section">
                        <h3>🔗 客户来源管理</h3>
                        <div class="add-form">
                            <input type="text" id="newCustomerSource" placeholder="请输入新的客户来源">
                            <button class="btn" onclick="addCustomerSource()">
                                <span>➕</span>
                                <span>添加</span>
                            </button>
                        </div>
                        <div class="items-list" id="customerSourcesList"></div>
                    </div>

                    <!-- 销售人员管理 -->
                    <div class="settings-section">
                        <h3>👥 销售人员管理</h3>
                        <div class="add-form">
                            <input type="text" id="newSalesperson" placeholder="姓名">
                            <input type="number" id="newBaseSalary" placeholder="基础工资">
                            <select id="newCommissionType" onchange="toggleCommissionInput()">
                                <option value="fixed">固定比例</option>
                                <option value="ladder">阶梯式</option>
                            </select>
                            <input type="number" id="newFixedRate" placeholder="提成%" step="0.1" min="0">
                            <button class="btn" onclick="addSalesperson()">
                                <span>➕</span>
                                <span>添加人员</span>
                            </button>
                        </div>
                        <div class="items-list" id="salespersonList"></div>
                    </div>

                    <!-- 系统操作 -->
                    <div class="settings-section">
                        <h3>🔧 系统操作</h3>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                            <button class="btn" onclick="loadSettingsData()">
                                <span>🔄</span>
                                <span>刷新数据</span>
                            </button>
                            <button class="btn btn-secondary" onclick="forceRefreshAll()">
                                <span>⚡</span>
                                <span>强制刷新</span>
                            </button>
                            <button class="btn btn-warning" onclick="fixDataConsistency()">
                                <span>🔧</span>
                                <span>修复数据</span>
                            </button>

                            <button class="btn btn-warning" onclick="forceFixUndefinedData()" style="background: #ff6b35;">
                                <span>⚡</span>
                                <span>强力修复未定义</span>
                            </button>

                            <button class="btn btn-danger" onclick="resetAllSettings()">
                                <span>🔄</span>
                                <span>重置所有设置</span>
                            </button>
                            <button class="btn btn-secondary" onclick="exportSettings()">
                                <span>📤</span>
                                <span>导出设置</span>
                            </button>
                            <button class="btn btn-tertiary" onclick="document.getElementById('importSettingsFile').click()">
                                <span>📥</span>
                                <span>导入设置</span>
                            </button>
                        </div>
                        <input type="file" id="importSettingsFile" accept=".json" style="display:none" onchange="importSettings()">
                    </div>
                </div>

                <div class="export-section">
                    <h3>📤 数据导出</h3>
                    <div class="export-buttons" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 10px; margin-bottom: 15px;">
                        <button class="btn btn-secondary" onclick="exportToExcel()">
                            <span>📊</span>
                            <span>导出Excel</span>
                        </button>
                        <button class="btn btn-warning" onclick="exportToPDF()">
                            <span>📄</span>
                            <span>导出PDF</span>
                        </button>
                        <button class="btn btn-tertiary" onclick="exportToCSV()">
                            <span>📋</span>
                            <span>导出CSV</span>
                        </button>
                        <button class="btn" onclick="backupData()">
                            <span>💾</span>
                            <span>备份数据</span>
                        </button>
                        <button class="btn btn-secondary" onclick="document.getElementById('importFile').click()">
                            <span>📥</span>
                            <span>导入数据</span>
                        </button>
                    </div>
                    <input type="file" id="importFile" accept=".json" style="display:none" onchange="importData()">
                    <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; font-size: 12px; color: #666;">
                        💡 <strong>导出说明：</strong><br>
                        • <strong>Excel格式：</strong>包含3个工作表（销售记录+工资计算+统计汇总），支持完整财务数据。如导出失败会自动提供简化版<br>
                        • <strong>PDF格式：</strong>包含完整报表，适合打印和存档<br>
                        • <strong>CSV格式：</strong>纯数据格式，适合数据分析和导入其他系统<br>
                        <span style="color: #ff6b6b;">⚠️ 首次使用请确保网络连接正常，以加载必要组件</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 阶梯式提成编辑模态框 -->
    <div id="ladderModal" class="modal">
        <div class="modal-content">
            <h3>🎯 编辑阶梯式提成</h3>
            <p>销售人员：<span id="currentEditPerson"></span></p>
            <div style="background: #e7f3ff; padding: 15px; margin-bottom: 15px; border-radius: 5px; font-size: 12px;">
                <strong>💡 阶梯式提成说明：</strong><br>
                • 采用累进式计算，低销售额部分按低比例，高销售额部分按高比例<br>
                • <strong>标准阶梯：</strong><br>
                　　≤ 3万元：6%<br>
                　　3万-10万元：15%（前3万仍按6%）<br>
                　　10万-20万元：20%（前面部分按原比例）<br>
                　　> 20万元：25%（前面部分按原比例）<br>
                • <strong>举例：</strong>销售8万元 = 3万×6% + 5万×15% = 1800+7500 = 9300元
            </div>
            <div id="ladderEditor"></div>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="addLadderLevel()">➕ 添加阶梯</button>
                <button class="btn btn-secondary" onclick="setStandardLadder()">🎯 设置标准阶梯</button>
                <button class="btn btn-tertiary" onclick="clearAllLadders()">🗑️ 清空阶梯</button>
            </div>
            <div style="margin-top: 20px;">
                <button class="btn" onclick="saveLadderSettings()">💾 保存</button>
                <button class="btn btn-secondary" onclick="closeLadderModal()">❌ 取消</button>
            </div>
        </div>
    </div>

    <!-- 收款管理弹窗 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <h3>💰 收款记录管理</h3>
            <div id="paymentModalContent">
                <!-- 合同信息 -->
                <div class="contract-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div>
                            <strong>客户：</strong><span id="contractCustomer"></span>
                        </div>
                        <div>
                            <strong>合同金额：</strong><span id="contractAmount"></span>
                        </div>
                        <div>
                            <strong>收款状态：</strong><span id="contractStatus"></span>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 10px;">
                        <div>
                            <strong>累计收款：</strong><span id="totalReceived"></span>
                        </div>
                        <div>
                            <strong>剩余金额：</strong><span id="remainingAmount"></span>
                        </div>
                        <div>
                            <strong>收款次数：</strong><span id="paymentCount"></span>
                        </div>
                    </div>
                </div>

                <!-- 添加新收款 -->
                <div class="add-payment-section" style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h4>➕ 添加新收款</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                        <div>
                            <label>收款日期：</label>
                            <input type="date" id="newPaymentDate" style="width: 100%;">
                        </div>
                        <div>
                            <label>收款金额：</label>
                            <input type="number" id="newPaymentAmount" placeholder="0.00" step="0.01" style="width: 100%;">
                        </div>
                        <div>
                            <label>收款方式：</label>
                            <select id="newPaymentMethod" style="width: 100%;">
                                <option value="银行转账">银行转账</option>
                                <option value="现金">现金</option>
                                <option value="支票">支票</option>
                                <option value="支付宝">支付宝</option>
                                <option value="微信">微信</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                        <div>
                            <label>银行信息：</label>
                            <input type="text" id="newPaymentBank" placeholder="银行名称/账号等" style="width: 100%;">
                        </div>
                        <div>
                            <label>交易流水号：</label>
                            <input type="text" id="newPaymentTransaction" placeholder="交易流水号" style="width: 100%;">
                        </div>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <label>备注：</label>
                        <input type="text" id="newPaymentRemarks" placeholder="收款备注" style="width: 100%;">
                    </div>
                    <button class="btn btn-primary" onclick="addPaymentRecord()">💰 添加收款记录</button>
                </div>

                <!-- 收款记录列表 -->
                <div class="payment-records-section">
                    <h4>📋 收款记录明细</h4>
                    <div class="table-container" style="max-height: 300px; overflow-y: auto;">
                        <table id="paymentRecordsTable" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th>收款日期</th>
                                    <th>收款金额</th>
                                    <th>收款方式</th>
                                    <th>银行信息</th>
                                    <th>交易流水号</th>
                                    <th>备注</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="paymentRecordsBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button class="btn btn-secondary" onclick="closePaymentModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // Supabase配置 - 请替换为您的实际配置
        const SUPABASE_URL = 'https://telnhaacebduhkhqbvla.supabase.co'; // 替换为您的Supabase URL
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRlbG5oYWFjZWJkdWhraHFidmxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1MDA5MzMsImV4cCI6MjA2NjA3NjkzM30.XEWYm_6FMh-JhFriN2ux4zyPgi1-DXxyd25s065jqV4'; // 替换为您的Supabase匿名密钥
        
        // 全局变量
        let supabase = null;
        let currentUser = null;
        let isSupabaseReady = false;
        
        // 初始化Supabase客户端
        function initializeSupabase() {
            try {
                if (typeof window.supabase !== 'undefined' && window.supabase.createClient) {
                    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                    isSupabaseReady = true;
                    console.log('✅ Supabase客户端已初始化');
                    checkAuthStatus();
                } else {
                    // Supabase库还没加载完成，稍后重试
                    setTimeout(initializeSupabase, 100);
                }
            } catch (error) {
                console.warn('⚠️ Supabase初始化失败，将使用本地模式:', error);
                hideLoadingIndicator();
                showMainApp(); // 降级到本地模式
                showToast('连接服务器失败，已切换到离线模式', 'warning');
            }
        }

        // 性能优化：延迟初始化数据，减少首次加载时间
        let salesData = null;
        let systemSettings = null;
        const SYSTEM_SETTINGS_VERSION = '1.0.0'; // 系统设置版本号
        
        // 懒加载销售数据
        function getSalesData() {
            if (salesData === null) {
                salesData = JSON.parse(localStorage.getItem('salesData')) || [];
                
                // 确保所有记录的数值字段都是有效的数字
                salesData = salesData.map(record => ({
                    ...record,
                    amount: Number(record.amount) || 0,
                    contractAmount: Number(record.contractAmount) || 0
                }));
            }
            return salesData;
        }
        
        // 懒加载系统设置
        function getSystemSettings() {
            if (systemSettings === null) {
                systemSettings = JSON.parse(localStorage.getItem('systemSettings')) || {};
                
                // 确保所有必需字段都存在，如果缺失则添加默认值
                let needsSave = false;
                if (!systemSettings.businessTypes) {
                    systemSettings.businessTypes = ['咨询服务', '咨询首款', '咨询尾款', '陪跑服务', '陪跑首付', '陪跑尾款', '培训服务', '培训占位', '其他'];
                    needsSave = true;
                }
                if (!systemSettings.incomeTypes) {
                    systemSettings.incomeTypes = ['押金', '培训收入', '日签收入', '英签收入', '美签收入', '加签收入', '澳签收入', '咨询收入', '日租收入', '其他收入'];
                    needsSave = true;
                }
                if (!systemSettings.customerSources) {
                    systemSettings.customerSources = ['自有客户', '运营线索', '运营转介绍', '客户转介绍', '管理内推'];
                    needsSave = true;
                }
                if (!systemSettings.salespeople) {
                    systemSettings.salespeople = [
                        { name: '冯承芑', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '李妍', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '庞锦媚', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '邹平', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '王东辉', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '张玉涵', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '李占英', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] },
                        { name: '李桂玲', baseSalary: 5500, commissionType: 'fixed', fixedRate: 0.06, ladderRates: [] }
                    ];
                    needsSave = true;
                }
                
                // 只有在需要时才保存
                if (needsSave) {
                    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
                }
            }
            return systemSettings;
        }
        
        // 保存系统设置
        function saveSystemSettings() {
            if (systemSettings !== null) {
                localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
            }
        }
        
        // 保存销售数据
        function saveSalesData() {
            if (salesData !== null) {
                localStorage.setItem('salesData', JSON.stringify(salesData));
            }
        }
        
        let currentEditPersonIndex = -1;

        // 性能优化：检查外部库加载状态并提供降级方案
        function checkLibrariesLoaded() {
            const libStatus = {
                xlsx: typeof XLSX !== 'undefined',
                html2canvas: typeof html2canvas !== 'undefined',
                jsPDF: typeof window.jspdf !== 'undefined'
            };
            
            // 记录库加载状态但不影响用户体验
            if (!libStatus.xlsx) {
                console.info('XLSX库未加载，Excel导出将使用降级方案');
            }
            if (!libStatus.html2canvas) {
                console.info('html2canvas库未加载，PDF导出将使用降级方案');
            }
            if (!libStatus.jsPDF) {
                console.info('jsPDF库未加载，PDF导出将使用降级方案');
            }
            
            return libStatus;
        }
        
        // 性能优化：延迟加载未加载的库
        function ensureLibraryLoaded(libName) {
            return new Promise((resolve) => {
                if (libName === 'xlsx' && typeof XLSX !== 'undefined') {
                    resolve(true);
                } else if (libName === 'html2canvas' && typeof html2canvas !== 'undefined') {
                    resolve(true);
                } else if (libName === 'jspdf' && typeof window.jspdf !== 'undefined') {
                    resolve(true);
                } else {
                    // 库未加载，尝试重新加载或使用降级方案
                    console.warn(`${libName}库未加载，使用降级方案`);
                    resolve(false);
                }
            });
        }

        // ====== 认证相关函数 ======
        
        // 检查认证状态
        async function checkAuthStatus() {
            if (!isSupabaseReady) {
                setTimeout(checkAuthStatus, 500);
                return;
            }
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    currentUser = session.user;
                    console.log('✅ 用户已登录:', currentUser.email);
                    await loadUserProfile();
                    showMainApp();
                } else {
                    console.log('ℹ️ 用户未登录，显示登录界面');
                    showAuthModal();
                }
            } catch (error) {
                console.error('检查认证状态失败:', error);
                hideLoadingIndicator();
                showAuthModal();
                showAuthError('认证检查失败，请刷新页面重试');
            }
        }
        
        // 隐藏加载指示器
        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }
        
        // 显示认证模态框
        function showAuthModal() {
            hideLoadingIndicator();
            document.getElementById('authModal').style.display = 'flex';
            document.getElementById('mainContainer').style.display = 'none';
        }
        
        // 显示主应用
        function showMainApp() {
            hideLoadingIndicator();
            document.getElementById('authModal').style.display = 'none';
            document.getElementById('mainContainer').style.display = 'block';
            initializeMainApp();
        }
        
        // 初始化主应用
        async function initializeMainApp() {
            try {
                // 设置默认日期
                document.getElementById('date').value = new Date().toISOString().split('T')[0];
                
                // 设置基础UI状态
                toggleCommissionInput();
                
                // 显示加载状态
                showToast('正在加载数据...', 'info');
                
                // 优先从云端加载系统设置，如果失败才使用本地默认数据
                console.log('🔄 优先从云端加载系统设置...');
                await loadSystemSettings();
                
                // 基于最终的系统设置更新表单选项
                updateFormOptions();
                
                // 加载销售数据
                await loadSalesRecordsFromDatabase();
                
                // 加载界面数据
                refreshUI();
                
                // 启动自动同步（仅同步销售数据，系统设置通过实时监听更新）
                startAutoSync();
                
                // 监听系统设置变化（实时同步）
                setupSystemSettingsListener();
                
                // 显示完成状态
                showToast('✅ 系统加载完成', 'success');
                console.log('✅ 主应用初始化完成');
                
            } catch (error) {
                console.error('❌ 主应用初始化失败:', error);
                showToast('初始化失败，请刷新页面重试', 'error');
            }
        }
        
        // 显示/隐藏表单
        function showLoginForm() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
            clearAuthMessage();
        }
        
        function showRegisterForm() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
            clearAuthMessage();
        }
        
        // 处理登录
        async function handleLogin() {
            const email = document.getElementById('loginEmail').value.trim();
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showAuthError('请填写完整的登录信息');
                return;
            }
            
            if (!isSupabaseReady) {
                showAuthError('系统正在初始化，请稍后再试');
                return;
            }
            
            setAuthLoading(true);
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                currentUser = data.user;
                console.log('✅ 登录成功:', currentUser.email);
                showAuthSuccess('登录成功，正在跳转...');
                
                setTimeout(async () => {
                    await loadUserProfile();
                    showMainApp();
                }, 1000);
                
            } catch (error) {
                console.error('登录失败:', error);
                let errorMessage = '登录失败，请检查邮箱和密码';
                
                if (error.message.includes('Invalid login credentials')) {
                    errorMessage = '邮箱或密码错误';
                } else if (error.message.includes('Email not confirmed')) {
                    errorMessage = '请先验证您的邮箱';
                }
                
                showAuthError(errorMessage);
            } finally {
                setAuthLoading(false);
            }
        }
        
        // 处理注册
        async function handleRegister() {
            const email = document.getElementById('registerEmail').value.trim();
            const password = document.getElementById('registerPassword').value;
            const name = document.getElementById('registerName').value.trim();
            const role = document.getElementById('registerRole').value;
            
            if (!email || !password || !name || !role) {
                showAuthError('请填写完整的注册信息');
                return;
            }
            
            if (password.length < 6) {
                showAuthError('密码至少需要6位字符');
                return;
            }
            
            if (!isSupabaseReady) {
                showAuthError('系统正在初始化，请稍后再试');
                return;
            }
            
            setAuthLoading(true);
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                    options: {
                        data: {
                            full_name: name,
                            role: role
                        }
                    }
                });
                
                if (error) {
                    throw error;
                }
                
                console.log('✅ 注册成功:', data.user?.email);
                showAuthSuccess('注册成功！请检查邮箱验证链接，验证后即可登录。');
                
                // 自动切换到登录表单
                setTimeout(() => {
                    showLoginForm();
                    document.getElementById('loginEmail').value = email;
                }, 2000);
                
            } catch (error) {
                console.error('注册失败:', error);
                let errorMessage = '注册失败，请稍后重试';
                
                if (error.message.includes('already registered')) {
                    errorMessage = '该邮箱已被注册，请使用其他邮箱';
                } else if (error.message.includes('Password should be')) {
                    errorMessage = '密码格式不符合要求';
                }
                
                showAuthError(errorMessage);
            } finally {
                setAuthLoading(false);
            }
        }
        
        // 处理登出
        async function handleLogout() {
            if (!isSupabaseReady) return;
            
            try {
                await supabase.auth.signOut();
                currentUser = null;
                console.log('✅ 已登出');
                showAuthModal();
                
                // 停止自动同步
                stopAutoSync();
                
                // 停止系统设置监听
                stopSystemSettingsListener();
                
                // 清理本地数据和权限信息
                salesData = null;
                systemSettings = null;
                userPermissions = { role: 'sales', can_view_all: false, can_edit_all: false, can_delete_all: false, can_manage_settings: false };
                
            } catch (error) {
                console.error('登出失败:', error);
                alert('登出失败，请刷新页面');
            }
        }

        // 强制刷新用户权限
        async function forceRefreshPermissions() {
            if (!isSupabaseReady || !currentUser) {
                showToast('请先登录', 'error');
                return;
            }
            
            try {
                console.log('🔄 强制刷新用户权限...');
                
                // 重新获取权限信息
                const { data, error } = await supabase.rpc('check_user_permissions');
                
                if (error) {
                    console.error('❌ 权限刷新失败:', error);
                    showToast('权限刷新失败', 'error');
                    return;
                }
                
                if (data && data.length > 0) {
                    const oldRole = userPermissions.role;
                    userPermissions = {
                        role: data[0].role || 'sales',
                        can_view_all: data[0].can_view_all || false,
                        can_edit_all: data[0].can_edit_all || false,
                        can_delete_all: data[0].can_delete_all || false,
                        can_manage_settings: data[0].can_manage_settings || false
                    };
                    
                    console.log('✅ 权限已强制刷新:', userPermissions);
                    console.log(`🔄 角色变化: ${oldRole} → ${userPermissions.role}`);
                    
                    // 重新加载用户资料显示
                    await loadUserProfile();
                    
                    // 重新加载数据
                    await loadSalesRecordsFromDatabase();
                    refreshUI({ salary: false });
                    
                    showToast(`权限已刷新！角色: ${userPermissions.role}`, 'success');
                } else {
                    console.log('❌ 未获取到权限数据');
                    showToast('未获取到权限数据', 'warning');
                }
                
            } catch (error) {
                console.error('❌ 强制刷新权限失败:', error);
                showToast('权限刷新失败', 'error');
            }
        }


        
        // 加载用户资料
        async function loadUserProfile() {
            if (!currentUser) return;
            
            try {
                // 修复406错误：使用maybeSingle()代替single()，并改善错误处理
                const { data, error } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .maybeSingle(); // 使用maybeSingle()避免406错误
                
                if (error) {
                    console.warn('加载用户资料时出现警告:', error);
                    // 不抛出错误，继续使用默认信息
                }
                
                // 更新用户状态显示
                const userStatusElement = document.getElementById('currentUser');
                if (userStatusElement) {
                    const userName = data?.full_name || currentUser.user_metadata?.full_name || currentUser.email;
                    const userRole = data?.role || currentUser.user_metadata?.role || 'sales';
                    
                    // 加载权限信息并显示
                    await loadUserPermissions();
                    const permissionIcon = userPermissions.can_view_all ? '👨‍💼' : (userPermissions.role === 'manager' ? '👔' : '👤');
                    userStatusElement.textContent = `${permissionIcon} ${userName} (${getRoleDisplayName(userRole)})`;
                    console.log('✅ 用户状态已更新:', userName, getRoleDisplayName(userRole), userPermissions);
                }
                
            } catch (error) {
                console.error('加载用户资料失败:', error);
                // 降级处理：使用基本用户信息
                const userStatusElement = document.getElementById('currentUser');
                if (userStatusElement) {
                    const userName = currentUser.user_metadata?.full_name || currentUser.email;
                    const userRole = currentUser.user_metadata?.role || 'sales';
                    userStatusElement.textContent = `${userName} (${getRoleDisplayName(userRole)})`;
                }
            }
        }
        
        // 获取角色显示名称
        function getRoleDisplayName(role) {
            switch (role) {
                case 'admin': return '管理员';
                case 'manager': return '销售经理';
                case 'sales': return '销售人员';
                default: return '用户';
            }
        }
        
        // 显示认证错误
        function showAuthError(message) {
            const messageElement = document.getElementById('authMessage');
            messageElement.textContent = message;
            messageElement.className = 'auth-message error';
            messageElement.style.display = 'block';
            
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
        
        // 显示认证成功
        function showAuthSuccess(message) {
            const messageElement = document.getElementById('authMessage');
            messageElement.textContent = message;
            messageElement.className = 'auth-message success';
            messageElement.style.display = 'block';
        }
        
        // 清除认证消息
        function clearAuthMessage() {
            const messageElement = document.getElementById('authMessage');
            messageElement.style.display = 'none';
        }
        
        // 设置认证加载状态
        function setAuthLoading(loading) {
            const loginBtn = document.querySelector('#loginForm .auth-btn');
            const registerBtn = document.querySelector('#registerForm .auth-btn');
            
            if (loading) {
                if (loginBtn) {
                    loginBtn.disabled = true;
                    loginBtn.textContent = '登录中...';
                }
                if (registerBtn) {
                    registerBtn.disabled = true;
                    registerBtn.textContent = '注册中...';
                }
            } else {
                if (loginBtn) {
                    loginBtn.disabled = false;
                    loginBtn.textContent = '登录';
                }
                if (registerBtn) {
                    registerBtn.disabled = false;
                    registerBtn.textContent = '注册';
                }
            }
        }

        // 性能优化：页面加载时分阶段初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面开始初始化...');
            
            // 立即初始化Supabase（不立即启动自动同步，等用户登录后再启动）
            initializeSupabase();
        });

        // ====== 数据库操作函数 ======
        
        // ====== 系统设置数据库同步 ======
        
        // 辅助函数：查找或创建基础数据ID
        async function findOrCreateId(tableName, name) {
            if (!name || name.trim() === '') return null;

            try {
                // 先查找现有记录
                const { data: existing } = await supabase
                    .from(tableName)
                    .select('id')
                    .eq('name', name.trim())
                    .eq('is_active', true)
                    .maybeSingle();

                if (existing) {
                    return existing.id;
                }

                // 如果不存在，创建新记录
                const { data: newRecord } = await supabase
                    .from(tableName)
                    .insert([{ name: name.trim(), description: '自动创建' }])
                    .select('id')
                    .single();

                return newRecord?.id;

            } catch (error) {
                console.error(`查找或创建${tableName}失败:`, error);
                return null;
            }
        }

        // 从数据库加载系统设置 - 完整的新架构实现
        async function loadSystemSettings() {
            try {
                console.log('🔄 从新架构数据库加载系统设置...');

                // 并行加载所有设置类型，使用 sort_order 排序
                const [businessTypesResult, incomeTypesResult, customerSourcesResult, salespeopleResult] = await Promise.all([
                    supabase.from('business_types').select('*').eq('is_active', true).order('sort_order, name'),
                    supabase.from('income_types').select('*').eq('is_active', true).order('sort_order, name'),
                    supabase.from('customer_sources').select('*').eq('is_active', true).order('sort_order, name'),
                    supabase.from('salespeople').select('*').eq('is_active', true).order('name')
                ]);

                // 🔧 修复：检查错误但不完全中断，确保基本功能可用
                let hasErrors = false;
                if (businessTypesResult.error) {
                    console.error('❌ 加载业务类型失败:', businessTypesResult.error);
                    hasErrors = true;
                }
                if (incomeTypesResult.error) {
                    console.error('❌ 加载收入类型失败:', incomeTypesResult.error);
                    hasErrors = true;
                }
                if (customerSourcesResult.error) {
                    console.error('❌ 加载客户来源失败:', customerSourcesResult.error);
                    hasErrors = true;
                }
                if (salespeopleResult.error) {
                    console.error('❌ 加载销售人员失败:', salespeopleResult.error);
                    hasErrors = true;
                }

                // 🔧 修复：转换为前端格式，确保有足够的默认数据
                systemSettings = {
                    businessTypes: businessTypesResult.data?.map(item => item.name) || [
                        '咨询服务', '咨询首款', '咨询尾款', '陪跑服务', '陪跑首付', '陪跑尾款',
                        '培训服务', '培训占位', '直销', '代理', '渠道销售', '其他'
                    ],
                    incomeTypes: incomeTypesResult.data?.map(item => item.name) || [
                        '押金', '培训收入', '日签收入', '英签收入', '美签收入', '加签收入',
                        '澳签收入', '咨询收入', '日租收入', '基本工资', '销售提成', '奖金', '其他收入'
                    ],
                    customerSources: customerSourcesResult.data?.map(item => item.name) || [
                        '自有客户', '运营线索', '运营转介绍', '客户转介绍', '管理内推',
                        '电话营销', '网络推广', '客户推荐'
                    ],
                    salespeople: salespeopleResult.data?.map(item => {
                        // 🔧 修复：正确处理数据库中的阶梯设置
                        let ladderRates = [];

                        if (item.ladder_rates) {
                            if (typeof item.ladder_rates === 'string') {
                                try {
                                    ladderRates = JSON.parse(item.ladder_rates);
                                } catch (e) {
                                    console.warn(`⚠️ ${item.name} 阶梯数据解析失败:`, item.ladder_rates);
                                    ladderRates = [];
                                }
                            } else if (Array.isArray(item.ladder_rates)) {
                                ladderRates = item.ladder_rates;
                            } else if (typeof item.ladder_rates === 'object' && item.ladder_rates !== null) {
                                // 如果是对象，尝试转换为数组
                                ladderRates = Object.values(item.ladder_rates);
                            }
                        }

                        const result = {
                            name: item.name,
                            baseSalary: item.base_salary || 0,
                            base_salary: item.base_salary || 0,
                            commissionType: item.commission_type || 'fixed',
                            commission_type: item.commission_type || 'fixed',
                            fixedRate: item.fixed_rate || 0.06,
                            fixed_rate: item.fixed_rate || 0.06,
                            ladderRates: ladderRates,
                            ladder_rates: ladderRates
                        };

                        // 🔍 调试信息：显示阶梯数据加载情况
                        if (result.commissionType === 'ladder') {
                            console.log(`📊 从数据库加载 ${item.name} 的阶梯设置:`, {
                                原始数据: item.ladder_rates,
                                数据类型: typeof item.ladder_rates,
                                解析结果: ladderRates,
                                阶梯数量: ladderRates.length
                            });
                        }

                        return result;
                    }) || []
                };

                // 🔧 修复：确保数据完整性，添加调试信息
                console.log('✅ 系统设置构建完成:', {
                    businessTypes: systemSettings.businessTypes.length,
                    incomeTypes: systemSettings.incomeTypes.length,
                    customerSources: systemSettings.customerSources.length,
                    salespeople: systemSettings.salespeople.length,
                    hasErrors: hasErrors
                });

                // 保存到本地作为缓存
                localStorage.setItem('systemSettings', JSON.stringify(systemSettings));

                console.log('✅ 新架构系统设置加载完成:', {
                    businessTypes: systemSettings.businessTypes.length,
                    incomeTypes: systemSettings.incomeTypes.length,
                    customerSources: systemSettings.customerSources.length,
                    salespeople: systemSettings.salespeople.length
                });

                return true;

            } catch (error) {
                console.error('❌ 从新架构数据库加载系统设置失败:', error);

                // 降级到本地存储
                const localSettings = localStorage.getItem('systemSettings');
                if (localSettings) {
                    systemSettings = { ...systemSettings, ...JSON.parse(localSettings) };
                    console.log('📱 已降级使用本地系统设置');
                } else {
                    // 使用默认设置
                    console.log('🔧 使用默认系统设置');
                    systemSettings = {
                        businessTypes: ['咨询服务', '培训服务', '其他'],
                        incomeTypes: ['咨询收入', '培训收入', '其他收入'],
                        customerSources: ['自有客户', '运营线索', '客户转介绍'],
                        salespeople: []
                    };
                }

                return false;
            }
        }
        
        // 保存系统设置到数据库 - 使用新的数据库架构
        async function saveSystemSettingsToDatabase() {
            if (!isSupabaseReady || !currentUser) {
                console.warn('Supabase未就绪或用户未登录，仅保存到本地');
                saveSystemSettings();
                return false;
            }

            // 检查用户权限，只有管理员可以保存到数据库
            if (userPermissions.role !== 'admin') {
                console.log('🔒 非管理员用户，系统设置仅保存到本地');
                saveSystemSettings();
                return false;
            }

            try {
                console.log('💾 保存系统设置到云端...');

                // 新架构：直接操作各个独立的表，而不是统一的 system_settings 表
                // 这样可以避免数据库架构不匹配的问题

                // 注意：在新架构中，系统设置通过独立表管理，
                // 添加/删除操作会直接调用相应的 findOrCreateId 函数
                // 这里我们只需要保存到本地存储即可

                console.log('ℹ️ 使用新数据库架构，系统设置通过独立表管理');
                console.log('ℹ️ 添加/删除操作会自动同步到对应的数据库表');

                // 保存到本地
                saveSystemSettings();

                console.log('✅ 系统设置已保存到本地存储');
                return true;

            } catch (error) {
                console.error('❌ 保存系统设置失败:', error);
                // 降级保存到本地
                saveSystemSettings();
                return false;
            }
        }

        // 手动为记录添加关联数据
        async function enrichRecordsWithRelatedData(records) {
            console.log('🔧 手动为记录添加关联数据...');

            try {
                // 获取所有关联表数据
                const [customerSources, businessTypes, incomeTypes, salespeople] = await Promise.all([
                    supabase.from('customer_sources').select('id, name').eq('is_active', true),
                    supabase.from('business_types').select('id, name').eq('is_active', true),
                    supabase.from('income_types').select('id, name').eq('is_active', true),
                    supabase.from('salespeople').select('id, name').eq('is_active', true)
                ]);

                // 创建查找映射
                const customerSourceMap = new Map(customerSources.data?.map(item => [item.id, item]) || []);
                const businessTypeMap = new Map(businessTypes.data?.map(item => [item.id, item]) || []);
                const incomeTypeMap = new Map(incomeTypes.data?.map(item => [item.id, item]) || []);
                const salespersonMap = new Map(salespeople.data?.map(item => [item.id, item]) || []);

                // 为每条记录添加关联数据
                return records.map(record => ({
                    ...record,
                    customer_sources: customerSourceMap.get(record.customer_source_id) || null,
                    business_types: businessTypeMap.get(record.business_type_id) || null,
                    income_types: incomeTypeMap.get(record.income_type_id) || null,
                    salespeople: salespersonMap.get(record.salesperson_id) || null
                }));

            } catch (error) {
                console.error('手动添加关联数据失败:', error);
                return records; // 返回原始记录
            }
        }

        // 保存销售记录到数据库 - 新版本适配新数据库结构
        async function saveSalesRecordToDatabase(record) {
            try {
                console.log('💾 保存销售记录到数据库...', record);

                // 查找各个关联表的ID - 添加详细日志
                console.log('🔍 开始查找关联表ID...');

                const businessTypeId = await findOrCreateId('business_types', record.businessType);
                console.log(`🔍 业务类型 "${record.businessType}" -> ID: ${businessTypeId}`);

                const customerSourceId = record.customerSource ?
                    await findOrCreateId('customer_sources', record.customerSource) : null;
                console.log(`🔍 客户来源 "${record.customerSource}" -> ID: ${customerSourceId}`);

                const incomeTypeId = record.incomeType ?
                    await findOrCreateId('income_types', record.incomeType) : null;
                console.log(`🔍 收入类型 "${record.incomeType}" -> ID: ${incomeTypeId}`);

                const salespersonId = await findOrCreateId('salespeople', record.salesperson);
                console.log(`🔍 销售人员 "${record.salesperson}" -> ID: ${salespersonId}`);

                if (!businessTypeId) {
                    throw new Error(`业务类型 "${record.businessType}" 的ID获取失败`);
                }

                if (!salespersonId) {
                    throw new Error(`销售人员 "${record.salesperson}" 的ID获取失败`);
                }

                const recordToSave = {
                    frontend_id: record.id, // 保持前端兼容性
                    user_id: currentUser.id,
                    created_by: currentUser.email,
                    date: record.date,
                    customer: record.customer,
                    customer_source_id: customerSourceId,
                    business_type_id: businessTypeId,
                    income_type_id: incomeTypeId,
                    salesperson_id: salespersonId,
                    amount: parseFloat(record.amount) || 0,
                    contract_amount: parseFloat(record.contractAmount) || 0,
                    remarks: record.remarks || ''
                };

                const { data, error } = await supabase
                    .from('sales_records')
                    .insert([recordToSave]);

                if (error) throw error;

                console.log('✅ 销售记录保存成功');
                return true;

            } catch (error) {
                console.error('❌ 保存销售记录失败:', error);
                throw error;
            }
        }
        
        // 全局变量：用户权限信息
        let userPermissions = {
            role: 'sales',
            can_view_all: false,
            can_edit_all: false,
            can_delete_all: false,
            can_manage_settings: false
        };

        // 加载用户权限信息
        async function loadUserPermissions() {
            if (!isSupabaseReady || !currentUser) {
                return userPermissions;
            }
            
            try {
                const { data, error } = await supabase.rpc('check_user_permissions');
                
                if (error) throw error;
                
                if (data && data.length > 0) {
                    userPermissions = {
                        role: data[0].role || 'sales',
                        can_view_all: data[0].can_view_all || false,
                        can_edit_all: data[0].can_edit_all || false,
                        can_delete_all: data[0].can_delete_all || false,
                        can_manage_settings: data[0].can_manage_settings || false
                    };
                    
                    console.log('✅ 用户权限加载完成:', userPermissions);
                }
                
                return userPermissions;
                
            } catch (error) {
                console.error('❌ 加载用户权限失败:', error);
                return userPermissions;
            }
        }

        // 🚨 紧急修复：从数据库加载销售记录 - 严格权限控制
        async function loadSalesRecordsFromDatabase() {
            try {
                console.log('📥 从数据库加载销售记录...');
                console.log('🔒 当前用户权限:', userPermissions);
                console.log('👤 当前用户邮箱:', currentUser?.email);

                // 🔧 修复：直接使用关联查询，避免依赖可能不存在的视图
                let query = supabase
                    .from('sales_records')
                    .select(`
                        *,
                        customer_sources(name),
                        business_types(name),
                        income_types(name),
                        salespeople(name)
                    `)
                    .order('created_at', { ascending: false });

                let fallbackQuery = supabase
                    .from('sales_records') // 备用查询：只查询基础字段
                    .select('*')
                    .order('created_at', { ascending: false });

                // 🚨 紧急修复：严格的权限控制
                if (userPermissions.role !== 'admin') {
                    console.log('🔒 非管理员权限：严格限制只能查看自己创建的记录');
                    // 双重过滤确保安全
                    query = query.eq('created_by', currentUser.email);
                    fallbackQuery = fallbackQuery.eq('created_by', currentUser.email);
                } else {
                    console.log('👑 管理员权限：可以查看所有记录');
                }

                // 先尝试从payment_summary视图查询
                let { data, error } = await query;

                // 如果关联查询失败，使用备用查询
                if (error) {
                    console.warn('关联查询失败，使用备用查询:', error);
                    const fallbackResult = await fallbackQuery;

                    if (fallbackResult.error) {
                        console.error('备用查询也失败:', fallbackResult.error);
                        data = fallbackResult.data;
                        error = fallbackResult.error;
                    } else {
                        // 备用查询成功，需要手动获取关联数据
                        console.log('备用查询成功，手动获取关联数据...');
                        data = await enrichRecordsWithRelatedData(fallbackResult.data);
                        error = null;
                    }
                }

                if (error) {
                    console.error('❌ 数据库查询错误:', error);
                    throw error;
                }

                console.log(`📊 从数据库获取到 ${data?.length || 0} 条记录`);

                // 🔧 修复数据显示问题：正确处理关联查询结果
                salesData = (data || []).map(record => {
                    console.log('🔍 处理记录:', record); // 调试日志

                    const fixedRecord = {
                        id: record.frontend_id || record.sales_record_id || record.id,
                        date: record.contract_date || record.date,
                        customer: record.customer || '未知客户',
                        // 🔧 修复：从关联对象中获取名称
                        customerSource: record.customer_sources?.name || '未定义',
                        businessType: record.business_types?.name || '未定义',
                        incomeType: record.income_types?.name || '未定义',
                        salesperson: record.salespeople?.name || '未定义',
                        amount: record.amount || 0,
                        contractAmount: record.contract_amount || 0,
                        // 新增收款状态相关字段
                        total_received: record.total_received || 0,
                        payment_status: record.payment_status || 'unpaid',
                        payment_count: record.payment_count || 0,
                        last_payment_date: record.last_payment_date,
                        remaining_amount: record.remaining_amount || 0,
                        remarks: record.remarks || '',
                        createdAt: record.created_at,
                        updatedAt: record.updated_at,
                        createdBy: record.created_by
                    };

                    // 🔍 调试信息：检查数据质量
                    if (fixedRecord.businessType === '未定义' || fixedRecord.incomeType === '未定义') {
                        console.warn('⚠️ 发现数据关联问题:', {
                            id: fixedRecord.id,
                            businessType: fixedRecord.businessType,
                            incomeType: fixedRecord.incomeType,
                            customerSource: fixedRecord.customerSource
                        });
                    }

                    return fixedRecord;
                });

                // 🔒 安全检查：确保非管理员用户只能看到自己的记录
                if (userPermissions.role !== 'admin') {
                    const beforeFilter = salesData.length;
                    salesData = salesData.filter(record => record.createdBy === currentUser.email);
                    const afterFilter = salesData.length;

                    if (beforeFilter !== afterFilter) {
                        console.warn(`⚠️ 安全过滤：移除了 ${beforeFilter - afterFilter} 条非本人记录`);
                    }
                }

                // 保存到本地存储
                localStorage.setItem('salesData', JSON.stringify(salesData));

                console.log(`✅ 最终加载了 ${salesData.length} 条销售记录 (权限: ${userPermissions.role})`);

            } catch (error) {
                console.error('❌ 加载销售记录失败:', error);

                // 🔒 安全降级：清空可能的错误数据
                if (userPermissions.role !== 'admin') {
                    console.log('🔒 非管理员用户，清空本地缓存以确保安全');
                    localStorage.removeItem('salesData');
                    salesData = [];
                } else {
                    // 管理员可以使用本地缓存
                    const localData = localStorage.getItem('salesData');
                    if (localData) {
                        salesData = JSON.parse(localData);
                        console.log('📱 管理员使用本地缓存数据');
                    } else {
                        salesData = [];
                    }
                }
            }
        }
        
        // 删除销售记录 - 新版本适配新数据库结构
        async function deleteSalesRecord(recordId) {
            if (!recordId) return false;

            try {
                console.log('🗑️ 删除销售记录:', recordId);

                // 检查删除权限
                if (!userPermissions.can_delete_all) {
                    // 检查是否是自己创建的记录
                    const record = salesData.find(r => r.id === recordId);
                    if (!record || record.createdBy !== currentUser.email) {
                        throw new Error('没有权限删除此记录');
                    }
                }

                // 从云端删除
                const { error } = await supabase
                    .from('sales_records')
                    .delete()
                    .eq('frontend_id', recordId);

                if (error) throw error;

                // 从本地数据中删除
                salesData = salesData.filter(record => record.id !== recordId);
                localStorage.setItem('salesData', JSON.stringify(salesData));

                console.log('✅ 销售记录删除成功');
                return true;

            } catch (error) {
                console.error('❌ 删除销售记录失败:', error);
                return false;
            }
        }

        // 统一同步函数 - 合并所有同步逻辑
        async function syncWithDatabase() {
            try {
                console.log('🔄 开始统一数据同步...');

                // 并行同步系统设置和销售记录
                await Promise.all([
                    loadSystemSettings(),
                    loadSalesRecordsFromDatabase()
                ]);

                // 更新界面
                updateFormOptions();
                refreshUI();

                console.log('✅ 统一数据同步完成');
                showToast('数据同步成功', 'success');

            } catch (error) {
                console.error('❌ 统一数据同步失败:', error);
                showToast('数据同步失败: ' + error.message, 'error');
            }
        }

        // 手动同步按钮处理 - 调用统一同步函数
        async function syncDataWithCloud() {
            if (!isSupabaseReady || !currentUser) {
                showToast('请先登录账户', 'error');
                return;
            }

            // 显示同步状态
            const syncBtn = document.querySelector('.sync-btn');
            if (!syncBtn) return;

            const originalText = syncBtn.innerHTML;
            syncBtn.innerHTML = '🔄';
            syncBtn.style.animation = 'spin 1s linear infinite';
            syncBtn.disabled = true;
            
            try {
                // 调用统一同步函数
                await syncWithDatabase();
                
            } catch (error) {
                console.error('❌ 数据同步失败:', error);
                let errorMessage = '数据同步失败';
                
                if (error.message.includes('network')) {
                    errorMessage = '网络连接失败，请检查网络';
                } else if (error.message.includes('unauthorized')) {
                    errorMessage = '认证失败，请重新登录';
                } else {
                    errorMessage = '数据同步失败，请稍后重试';
                }
                
                showToast(errorMessage, 'error');
            } finally {
                // 恢复同步按钮状态
                syncBtn.innerHTML = originalText;
                syncBtn.style.animation = '';
                syncBtn.disabled = false;
            }
        }

        // ====== 自动同步机制 ======
        
        let autoSyncInterval = null;
        let lastSyncTime = 0;
        const AUTO_SYNC_INTERVAL = 60000; // 🔧 改为60秒自动同步一次，减少网络压力
        let syncRetryCount = 0;
        const MAX_SYNC_RETRIES = 3;
        
        // 启动自动同步
        function startAutoSync() {
            if (!isSupabaseReady || autoSyncInterval) {
                if (autoSyncInterval) {
                    console.log('ℹ️ 自动同步已经在运行中，跳过重复启动');
                }
                return;
            }
            
            console.log('🔄 启动自动数据同步，间隔30秒');
            updateSyncIndicator('active');
            
            // 首次启动时立即进行一次同步检查
            setTimeout(() => silentSyncFromCloud(), 2000);
            
            autoSyncInterval = setInterval(async () => {
                try {
                    if (!isSupabaseReady || !currentUser) return;

                    // 🔧 增强错误处理：静默同步，不显示提示
                    await silentSyncFromCloud();
                    syncRetryCount = 0; // 成功后重置重试计数

                } catch (error) {
                    syncRetryCount++;
                    console.warn(`自动同步失败 (${syncRetryCount}/${MAX_SYNC_RETRIES}):`, error);

                    // 如果连续失败次数过多，暂停自动同步
                    if (syncRetryCount >= MAX_SYNC_RETRIES) {
                        console.error('🚨 自动同步连续失败，暂停自动同步');
                        stopAutoSync();
                        updateSyncIndicator('error', '网络连接异常，已暂停自动同步');
                        showToast('网络连接异常，已暂停自动同步', 'warning');
                    } else {
                        updateSyncIndicator('error', `同步失败 (${syncRetryCount}/${MAX_SYNC_RETRIES})`);
                    }
                }
            }, AUTO_SYNC_INTERVAL);
        }
        
        // 停止自动同步
        function stopAutoSync() {
            if (autoSyncInterval) {
                clearInterval(autoSyncInterval);
                autoSyncInterval = null;
                updateSyncIndicator('disabled');
                console.log('⏸️ 自动同步已停止');
            }
        }
        
        // 更新自动同步指示器状态
        function updateSyncIndicator(status, message) {
            const indicator = document.getElementById('autoSyncIndicator');
            if (!indicator) return;
            
            switch (status) {
                case 'active':
                    indicator.textContent = '🟢';
                    indicator.title = '自动同步开启 - ' + (message || '30秒间隔同步');
                    indicator.classList.remove('syncing');
                    break;
                case 'syncing':
                    indicator.textContent = '🔄';
                    indicator.title = '正在同步数据...';
                    indicator.classList.add('syncing');
                    break;
                case 'error':
                    indicator.textContent = '🔴';
                    indicator.title = '同步错误 - ' + (message || '请检查网络连接');
                    indicator.classList.remove('syncing');
                    break;
                case 'disabled':
                    indicator.textContent = '⚪';
                    indicator.title = '自动同步已关闭';
                    indicator.classList.remove('syncing');
                    break;
            }
        }
        
        // 静默同步 - 简化版本，只检查新记录
        async function silentSyncFromCloud() {
            if (!isSupabaseReady || !currentUser) return;

            updateSyncIndicator('syncing');

            try {
                // 简单检查是否有新记录
                await loadSalesRecordsFromDatabase();

                lastSyncTime = Date.now();
                updateSyncIndicator('active');

            } catch (error) {
                console.warn('静默同步失败:', error);
                updateSyncIndicator('error', error.message);
            }
        }
        
        // 监听页面可见性变化，页面重新获得焦点时立即同步
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && isSupabaseReady && currentUser) {
                // 页面重新可见时，如果距离上次同步超过5秒，立即执行一次同步
                const timeSinceLastSync = Date.now() - lastSyncTime;
                if (timeSinceLastSync > 5000) {
                    setTimeout(() => silentSyncFromCloud(), 1000);
                }
            }
        });

        // ====== 系统设置实时同步监听 ======
        
        let systemSettingsListener = null;
        
        // 设置系统设置实时监听
        function setupSystemSettingsListener() {
            if (!isSupabaseReady || systemSettingsListener) {
                if (systemSettingsListener) {
                    console.log('ℹ️ 系统设置监听已经在运行中，跳过重复启动');
                }
                return;
            }
            
            console.log('🔄 启动系统设置实时监听...');
            
            // 监听系统设置表的变化
            systemSettingsListener = supabase
                .channel('system_settings_changes')
                .on('postgres_changes', {
                    event: '*',
                    schema: 'public',
                    table: 'system_settings'
                }, (payload) => {
                    console.log('🔔 检测到系统设置变化:', payload);
                    handleSystemSettingsChange(payload);
                })
                .subscribe();
        }
        
        // 处理系统设置变化
        async function handleSystemSettingsChange(payload) {
            try {
                // 无论是什么操作（INSERT, UPDATE, DELETE），都重新加载最新的系统设置
                console.log('🔄 检测到系统设置变化，正在同步最新设置...', payload.eventType);
                
                const { data: settings, error } = await supabase
                    .from('system_settings')
                    .select('*')
                    .order('updated_at', { ascending: false })
                    .limit(1);
                
                if (error) throw error;
                
                if (settings && settings.length > 0) {
                    const cloudSettings = settings[0];
                    const newSettings = {
                        businessTypes: cloudSettings.business_types || [],
                        incomeTypes: cloudSettings.income_types || [],
                        customerSources: cloudSettings.customer_sources || [],
                        salespeople: cloudSettings.salespeople || []
                    };
                    
                    // 检查是否真的有变化
                    const currentSettings = JSON.stringify(systemSettings);
                    const updatedSettings = JSON.stringify(newSettings);
                    
                    if (currentSettings !== updatedSettings) {
                        console.log('✅ 系统设置已从云端同步更新:', {
                            业务类型: newSettings.businessTypes.length + '项',
                            收入类型: newSettings.incomeTypes.length + '项', 
                            客户来源: newSettings.customerSources.length + '项',
                            销售人员: newSettings.salespeople.length + '人'
                        });
                        
                        // 立即更新本地系统设置，覆盖原有数据
                        systemSettings = newSettings;
                        localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
                        
                        // 立即更新表单选项
                        updateFormOptions();
                        
                        // 显示更新提示，提醒用户数据已同步
                        showToast('⚙️ 系统设置已实时更新', 'info');
                        
                        console.log('📝 表单选项已更新为云端最新数据');
                    } else {
                        console.log('ℹ️ 系统设置无实质变化，跳过更新');
                    }
                } else {
                    console.log('⚠️ 云端系统设置数据被删除，恢复默认设置');
                    // 如果云端数据被删除，恢复默认设置
                    getSystemSettings();
                    updateFormOptions();
                    showToast('⚠️ 系统设置已重置为默认值', 'warning');
                }
                
            } catch (error) {
                console.error('❌ 处理系统设置变化失败:', error);
                showToast('❌ 系统设置同步失败', 'error');
            }
        }
        
        // 停止系统设置监听
        function stopSystemSettingsListener() {
            if (systemSettingsListener) {
                supabase.removeChannel(systemSettingsListener);
                systemSettingsListener = null;
                console.log('⏸️ 系统设置实时监听已停止');
            }
        }

        // 表单提交处理（改进版）
        document.getElementById('salesForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                id: Date.now(), // 使用时间戳作为唯一ID
                date: document.getElementById('date').value,
                customer: document.getElementById('customer').value,
                customerSource: document.getElementById('customerSource').value,
                businessType: document.getElementById('businessType').value,
                amount: parseFloat(document.getElementById('amount').value),
                incomeType: document.getElementById('incomeType').value,
                salesperson: document.getElementById('salesperson').value,
                contractAmount: parseFloat(document.getElementById('contractAmount').value) || 0,
                remarks: document.getElementById('remarks').value
            };

            // 先添加到本地数据
            getSalesData().push(formData);
            localStorage.setItem('salesData', JSON.stringify(getSalesData()));
            
            // 立即更新界面
            refreshUI({ analytics: true });
            
            // 异步保存到云端
            saveSalesRecordToDatabase(formData);
            
            // 如果当前在统计分析标签，则更新统计
            if (document.getElementById('statistics').classList.contains('active')) {
                setTimeout(() => updateAllStatistics(), 50);
            }
            
            // 显示喜报弹窗
            showCelebrationModal(formData);
            
            // 重置表单
            this.reset();
            document.getElementById('date').value = new Date().toISOString().split('T')[0];
        });

        // 性能优化：缓存和防重复渲染
        let lastTableDataHash = null;
        let lastTableRenderTime = 0;
        
        // 加载表格数据（添加缓存机制）
        function loadTableData() {
            const currentData = getSalesData();
            const currentHash = JSON.stringify(currentData);
            const now = Date.now();
            
            // 防抖：100ms内不重复渲染
            if (now - lastTableRenderTime < 100) {
                return;
            }
            
            // 缓存：如果数据没变化则不重新渲染
            if (currentHash === lastTableDataHash) {
                return;
            }
            
            lastTableDataHash = currentHash;
            lastTableRenderTime = now;
            
            const tbody = document.getElementById('salesTableBody');
            tbody.innerHTML = '';
            
            // 按日期倒序排列
            const sortedData = currentData.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            // 性能优化：使用DocumentFragment批量插入DOM
            const fragment = document.createDocumentFragment();
            const today = new Date().toISOString().split('T')[0];
            
            // 限制显示数量，避免大数据量时卡顿
            const maxDisplayCount = 1000;
            const displayData = sortedData.slice(0, maxDisplayCount);
            
            displayData.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.date}</td>
                    <td>${record.customer}</td>
                    <td>${record.customerSource || '-'}</td>
                    <td>${record.businessType}</td>
                    <td>¥${(Number(record.amount) || 0).toLocaleString()}</td>
                    <td>${record.incomeType}</td>
                    <td>${record.salesperson}</td>
                    <td>¥${(Number(record.contractAmount) || 0).toLocaleString()}</td>
                    <td>
                        <span class="payment-status ${getPaymentStatusClass(record)}">${getPaymentStatusText(record)}</span>
                    </td>
                    <td title="${record.remarks || '-'}">${record.remarks || '-'}</td>
                    <td>
                        <button class="payment-btn" onclick="openPaymentModal(${record.id})" title="管理收款记录">💰</button>
                        <button class="delete-btn" onclick="deleteRecord(${record.id})" title="删除记录">🗑️</button>
                    </td>
                `;
                
                // 如果是今天的记录，高亮显示
                if (record.date === today) {
                    row.classList.add('highlight');
                }
                
                fragment.appendChild(row);
            });
            
            tbody.appendChild(fragment);
            
            // 如果有更多数据未显示，添加提示
            if (sortedData.length > maxDisplayCount) {
                const moreRow = tbody.insertRow();
                moreRow.innerHTML = `
                    <td colspan="10" style="text-align: center; color: #666; font-style: italic;">
                        显示前${maxDisplayCount}条记录，共${sortedData.length}条记录
                    </td>
                `;
            }
            
            // 更新记录计数
            updateTableRecordCount();
        }

        // 更新表格记录计数
        function updateTableRecordCount() {
            const count = getSalesData().length;
            const countElement = document.getElementById('tableRecordCount');
            if (countElement) {
                countElement.textContent = `共 ${count} 条记录`;
            }
        }

        // 获取收款状态文本
        function getPaymentStatusText(record) {
            const contractAmount = Number(record.contractAmount) || 0;
            // 优先使用数据库中的total_received字段，如果没有则使用amount字段
            const receivedAmount = Number(record.total_received) || Number(record.amount) || 0;

            if (contractAmount === 0) return '无合同';
            if (receivedAmount === 0) return '未收款';
            if (receivedAmount < contractAmount) return '部分收款';
            if (receivedAmount === contractAmount) return '已收齐';
            return '超额收款';
        }

        // 获取收款状态样式类
        function getPaymentStatusClass(record) {
            const contractAmount = Number(record.contractAmount) || 0;
            // 优先使用数据库中的total_received字段，如果没有则使用amount字段
            const receivedAmount = Number(record.total_received) || Number(record.amount) || 0;

            if (contractAmount === 0) return 'status-no-contract';
            if (receivedAmount === 0) return 'status-unpaid';
            if (receivedAmount < contractAmount) return 'status-partial';
            if (receivedAmount === contractAmount) return 'status-paid';
            return 'status-overpaid';
        }

        // 刷新表格
        function refreshTable() {
            clearSearch(); // 清除搜索状态
            loadTableData();
            updateStatistics();
        }

        // 搜索客户功能
        function searchCustomer() {
            const searchInput = document.getElementById('customerSearchInput');
            const searchTerm = searchInput.value.trim();
            
            if (!searchTerm) {
                alert('请输入要搜索的客户名');
                searchInput.focus();
                return;
            }
            
            const allData = getSalesData();
            const filteredData = allData.filter(record => 
                record.customer && record.customer.toLowerCase().includes(searchTerm.toLowerCase())
            );
            
            displayFilteredResults(filteredData, searchTerm);
            
            // 显示清除按钮
            document.querySelector('.clear-search-btn').style.display = 'flex';
            
            // 更新记录计数
            const countElement = document.getElementById('tableRecordCount');
            if (countElement) {
                countElement.textContent = `搜索到 ${filteredData.length} 条记录`;
                countElement.style.color = '#ff4757';
                countElement.style.fontWeight = 'bold';
            }
        }

        // 显示过滤后的结果
        function displayFilteredResults(filteredData, searchTerm) {
            const tbody = document.querySelector('#salesTable tbody');
            tbody.innerHTML = '';
            
            if (filteredData.length === 0) {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td colspan="10" class="search-no-results">
                        😔 未找到包含 "${searchTerm}" 的客户记录
                    </td>
                `;
                return;
            }
            
            // 按日期倒序排列
            const sortedData = filteredData.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            sortedData.forEach(record => {
                const row = tbody.insertRow();
                
                // 高亮显示搜索关键词
                const highlightedCustomer = highlightSearchTerm(record.customer, searchTerm);
                
                row.innerHTML = `
                    <td>${record.date}</td>
                    <td>${highlightedCustomer}</td>
                    <td>${record.customerSource || ''}</td>
                    <td>${record.businessType}</td>
                    <td>¥${(Number(record.amount) || 0).toLocaleString()}</td>
                    <td>${record.incomeType}</td>
                    <td>${record.salesperson}</td>
                    <td>¥${(Number(record.contractAmount) || Number(record.amount) || 0).toLocaleString()}</td>
                    <td>${record.remarks || ''}</td>
                    <td>
                        <button class="btn btn-danger" onclick="deleteRecord('${record.id}')">删除</button>
                    </td>
                `;
                
                // 如果是今天的记录，添加高亮
                const today = new Date().toISOString().split('T')[0];
                if (record.date === today) {
                    row.classList.add('highlight');
                }
            });
        }

        // 高亮显示搜索关键词
        function highlightSearchTerm(text, searchTerm) {
            if (!text || !searchTerm) return text;
            
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        }

        // 清除搜索
        function clearSearch() {
            const searchInput = document.getElementById('customerSearchInput');
            searchInput.value = '';
            
            // 隐藏清除按钮
            document.querySelector('.clear-search-btn').style.display = 'none';
            
            // 重新加载完整数据
            loadTableData();
            
            // 恢复记录计数样式
            const countElement = document.getElementById('tableRecordCount');
            if (countElement) {
                countElement.style.color = '#666';
                countElement.style.fontWeight = '500';
            }
        }

        // 添加回车键搜索支持
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('customerSearchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchCustomer();
                    }
                });
                
                // 实时搜索（输入时自动搜索）
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    
                    const searchTerm = this.value.trim();
                    if (searchTerm === '') {
                        clearSearch();
                        return;
                    }
                    
                    // 延迟搜索，避免频繁触发
                    searchTimeout = setTimeout(() => {
                        if (searchTerm.length >= 1) { // 至少输入1个字符才开始搜索
                            searchCustomer();
                        }
                    }, 300);
                });
            }
        });

        // 删除记录（改进版，支持云端同步）
        async function deleteRecord(id) {
            if (confirm('确定要删除这条记录吗？')) {
                // 获取要删除的记录信息（用于检查是否需要更新弹窗）
                const currentData = getSalesData();
                const deletedRecord = currentData.find(record => record.id === id);
                
                // 调用新的删除函数，同时删除本地和云端记录
                const success = await deleteSalesRecord(id);
                
                if (success) {
                    // 更新界面
                    loadTableData();
                    updateStatistics();
                    updateSalaryDetails();
                    
                    // 如果当前在统计分析标签，则更新统计
                    if (document.getElementById('statistics').classList.contains('active')) {
                        setTimeout(() => updateAllStatistics(), 50);
                    }
                    
                    // 如果喜报弹窗当前显示，且删除的记录是同一个销售人员的，则更新弹窗统计
                    updateCelebrationModalIfOpen(deletedRecord);
                    
                    showToast('记录已删除', 'success');
                } else {
                    showToast('删除失败，请重试', 'error');
                }
            }
        }

        // 更新弹窗统计数据（如果弹窗当前显示）
        function updateCelebrationModalIfOpen(deletedRecord) {
            const modal = document.getElementById('celebrationModal');
            const detailsElement = document.getElementById('celebrationDetails');
            
            // 检查弹窗是否当前显示
            if (modal && modal.style.display === 'block' && detailsElement && deletedRecord) {
                // 从弹窗中提取当前显示的销售人员姓名
                const currentSalesperson = extractSalespersonFromModal(detailsElement);
                
                // 如果删除的记录是同一个销售人员的，更新统计数据
                if (currentSalesperson && deletedRecord.salesperson === currentSalesperson) {
                    refreshCelebrationModalStats(currentSalesperson, detailsElement);
                }
            }
        }

        // 从弹窗中提取销售人员姓名
        function extractSalespersonFromModal(detailsElement) {
            // 从新布局的关键信息区域提取销售人员姓名
            const salespersonElements = detailsElement.querySelectorAll('.key-info-item');
            
            for (let element of salespersonElements) {
                const label = element.querySelector('.key-label');
                const value = element.querySelector('.key-value');
                
                if (label && label.textContent.trim() === '销售员' && value) {
                    return value.textContent.trim();
                }
            }
            return null;
        }

        // 刷新弹窗中的统计数据
        function refreshCelebrationModalStats(salesperson, detailsElement) {
            // 重新计算该销售人员的统计数据
            const salespersonRecords = salesData.filter(record => record.salesperson === salesperson);
            const totalAmount = salespersonRecords.reduce((sum, record) => sum + record.amount, 0);
            const totalDeals = salespersonRecords.length;
            const avgDealAmount = totalDeals > 0 ? totalAmount / totalDeals : 0;
            
            // 计算本月业绩
            const currentMonth = new Date().toISOString().substring(0, 7);
            const monthRecords = salespersonRecords.filter(record => record.date.startsWith(currentMonth));
            const monthAmount = monthRecords.reduce((sum, record) => sum + record.amount, 0);
            const monthDeals = monthRecords.length;
            
            // 更新新垂直布局下的统计数据显示
            const statsElements = {
                // 历史总业绩数据
                totalAmount: detailsElement.querySelector('.celebration-column:nth-child(2) .stat-number'),
                totalDeals: detailsElement.querySelector('.celebration-column:nth-child(2) .stat-small:nth-child(1) .stat-small-num'),
                avgDeal: detailsElement.querySelector('.celebration-column:nth-child(2) .stat-small:nth-child(2) .stat-small-num'),
                // 本月业绩数据
                monthAmount: detailsElement.querySelector('.celebration-column:nth-child(3) .stat-number'),
                monthDeals: detailsElement.querySelector('.celebration-column:nth-child(3) .stat-small:nth-child(1) .stat-small-num'),
                completion: detailsElement.querySelector('.celebration-column:nth-child(3) .stat-small:nth-child(2) .stat-small-num')
            };
            
            // 更新显示的数值
            if (statsElements.totalAmount) statsElements.totalAmount.textContent = `¥${(Number(totalAmount) || 0).toLocaleString()}`;
            if (statsElements.totalDeals) statsElements.totalDeals.textContent = totalDeals;
            if (statsElements.avgDeal) statsElements.avgDeal.textContent = `¥${(Number(avgDealAmount) || 0).toLocaleString()}`;
            if (statsElements.monthAmount) statsElements.monthAmount.textContent = `¥${(Number(monthAmount) || 0).toLocaleString()}`;
            if (statsElements.monthDeals) statsElements.monthDeals.textContent = monthDeals;
            if (statsElements.completion) {
                const completionRate = totalDeals > 0 ? ((monthDeals / totalDeals) * 100).toFixed(1) : 0;
                statsElements.completion.textContent = `${completionRate}%`;
            }
            
            console.log(`✅ 已更新弹窗中 ${salesperson} 的统计数据`);
        }

        // 更新统计信息
        function updateStatistics() {
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().toISOString().substring(0, 7);
            
            const todayRecords = salesData.filter(record => record.date === today);
            const monthRecords = salesData.filter(record => record.date.startsWith(currentMonth));
            
            const todayTotal = todayRecords.reduce((sum, record) => sum + record.amount, 0);
            const monthTotal = monthRecords.reduce((sum, record) => sum + record.amount, 0);
            
            document.getElementById('todayTotal').textContent = `¥${todayTotal.toFixed(2)}`;
            document.getElementById('monthTotal').textContent = `¥${monthTotal.toFixed(2)}`;
            document.getElementById('recordCount').textContent = salesData.length;
        }

        // 更新所有统计分析
        function updateAllStatistics() {
            updateKPICards();
            updateTrendAnalysis();
            updateChartVisualizations();
            updateSmartInsights();
        }

        // 时间周期选择器
        function updateAnalyticsPeriod() {
            const period = document.getElementById('analyticsPeriod').value;
            console.log('切换分析周期:', period);
            updateAllStatistics();
        }

        // 卡片展开/折叠控制
        function toggleCardContent(cardId) {
            const content = document.getElementById(cardId + 'Content');
            const toggle = content.parentElement.querySelector('.card-toggle');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.textContent = '−';
            } else {
                content.classList.add('collapsed');
                toggle.textContent = '+';
            }
        }

        // 获取分析周期数据
        function getAnalyticsData() {
            const period = document.getElementById('analyticsPeriod')?.value || 'month';
            const now = new Date();
            let startDate;
            
            switch (period) {
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'quarter':
                    const quarterStart = Math.floor(now.getMonth() / 3) * 3;
                    startDate = new Date(now.getFullYear(), quarterStart, 1);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(2000, 0, 1); // 全部数据
            }
            
            return salesData.filter(record => new Date(record.date) >= startDate);
        }

        // 1. 更新KPI卡片
        function updateKPICards() {
            const currentData = getAnalyticsData();
            const previousPeriodData = getPreviousPeriodData();
            
            // 总收入
            const totalRevenue = currentData.reduce((sum, record) => sum + record.amount, 0);
            const prevRevenue = previousPeriodData.reduce((sum, record) => sum + record.amount, 0);
            const revenueChange = prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue * 100) : 0;
            
            document.getElementById('kpiTotalRevenue').textContent = `¥${(Number(totalRevenue) || 0).toLocaleString()}`;
            updateKPIChange('kpiRevenueChange', revenueChange);
            
            // 成交笔数
            const totalDeals = currentData.length;
            const prevDeals = previousPeriodData.length;
            const dealsChange = prevDeals > 0 ? ((totalDeals - prevDeals) / prevDeals * 100) : 0;
            
            document.getElementById('kpiTotalDeals').textContent = totalDeals;
            updateKPIChange('kpiDealsChange', dealsChange);
            
            // 活跃销售人员
            const activeSales = new Set(currentData.map(record => record.salesperson)).size;
            const prevActiveSales = new Set(previousPeriodData.map(record => record.salesperson)).size;
            const salesChange = prevActiveSales > 0 ? ((activeSales - prevActiveSales) / prevActiveSales * 100) : 0;
            
            document.getElementById('kpiActiveSales').textContent = `${activeSales}人`;
            updateKPIChange('kpiSalesChange', salesChange);
            
            // 线索成交（运营线索和运营转介绍的综合）
            const leadSources = ['运营线索', '运营转介绍'];
            const leadConversions = currentData.filter(record => 
                leadSources.includes(record.customerSource)
            ).length;
            const prevLeadConversions = previousPeriodData.filter(record => 
                leadSources.includes(record.customerSource)
            ).length;
            const leadChange = prevLeadConversions > 0 ? ((leadConversions - prevLeadConversions) / prevLeadConversions * 100) : 0;
            
            document.getElementById('kpiLeadConversion').textContent = `${leadConversions}笔`;
            updateKPIChange('kpiLeadChange', leadChange);
            
            // 欠款总金额（使用新的收款数据）
            const unpaidAmount = currentData.reduce((sum, record) => {
                const contractAmount = record.contractAmount || 0;
                // 优先使用total_received字段，如果没有则使用amount字段
                const receivedAmount = record.total_received || record.amount || 0;
                return sum + Math.max(0, contractAmount - receivedAmount);
            }, 0);
            const prevUnpaidAmount = previousPeriodData.reduce((sum, record) => {
                const contractAmount = record.contractAmount || 0;
                // 优先使用total_received字段，如果没有则使用amount字段
                const receivedAmount = record.total_received || record.amount || 0;
                return sum + Math.max(0, contractAmount - receivedAmount);
            }, 0);
            const unpaidAmountChange = prevUnpaidAmount > 0 ? ((unpaidAmount - prevUnpaidAmount) / prevUnpaidAmount * 100) : 0;
            
            document.getElementById('kpiUnpaidAmount').textContent = `¥${(Number(unpaidAmount) || 0).toLocaleString()}`;
            updateKPIChange('kpiUnpaidAmountChange', unpaidAmountChange);
            
            // 欠款数量（使用新的收款数据）
            const unpaidCount = currentData.filter(record => {
                const contractAmount = record.contractAmount || 0;
                // 优先使用total_received字段，如果没有则使用amount字段
                const receivedAmount = record.total_received || record.amount || 0;
                return contractAmount > receivedAmount;
            }).length;
            const prevUnpaidCount = previousPeriodData.filter(record => {
                const contractAmount = record.contractAmount || 0;
                // 优先使用total_received字段，如果没有则使用amount字段
                const receivedAmount = record.total_received || record.amount || 0;
                return contractAmount > receivedAmount;
            }).length;
            const unpaidCountChange = prevUnpaidCount > 0 ? ((unpaidCount - prevUnpaidCount) / prevUnpaidCount * 100) : 0;
            
            document.getElementById('kpiUnpaidCount').textContent = `${unpaidCount}笔`;
            updateKPIChange('kpiUnpaidCountChange', unpaidCountChange);
        }
        
        function updateKPIChange(elementId, changePercent) {
            const element = document.getElementById(elementId);
            const isPositive = changePercent >= 0;
            
            element.textContent = `${isPositive ? '+' : ''}${changePercent.toFixed(1)}%`;
            element.className = `kpi-change ${isPositive ? '' : 'negative'}`;
        }
        
        function getPreviousPeriodData() {
            const period = document.getElementById('analyticsPeriod')?.value || 'month';
            const now = new Date();
            let startDate, endDate;
            
            switch (period) {
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    endDate = new Date(now.getFullYear(), now.getMonth(), 0);
                    break;
                case 'quarter':
                    const quarterStart = Math.floor(now.getMonth() / 3) * 3;
                    startDate = new Date(now.getFullYear(), quarterStart - 3, 1);
                    endDate = new Date(now.getFullYear(), quarterStart, 0);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear() - 1, 0, 1);
                    endDate = new Date(now.getFullYear() - 1, 11, 31);
                    break;
                default:
                    return []; // 全部数据时不比较
            }
            
            return salesData.filter(record => {
                const date = new Date(record.date);
                return date >= startDate && date <= endDate;
            });
        }

        // 2. 更新趋势分析
        function updateTrendAnalysis() {
            updateMonthlyTrends();
            updateWeeklyTrends();
        }
        
        function updateMonthlyTrends() {
            const monthlyData = {};
            
            salesData.forEach(record => {
                const month = record.date.substring(0, 7);
                if (!monthlyData[month]) {
                    monthlyData[month] = { amount: 0, count: 0 };
                }
                monthlyData[month].amount += record.amount;
                monthlyData[month].count += 1;
            });
            
            const monthlyTrendsDiv = document.getElementById('monthlyTrends');
            monthlyTrendsDiv.innerHTML = '';
            
            const months = Object.keys(monthlyData).sort().reverse().slice(0, 6);
            if (months.length === 0) {
                monthlyTrendsDiv.innerHTML = '<div class="stat-item-compact"><span class="stat-label-compact">暂无数据</span></div>';
                return;
            }
            
            months.forEach(month => {
                const data = monthlyData[month];
                const avg = data.count > 0 ? data.amount / data.count : 0;
                
                const statItem = document.createElement('div');
                statItem.className = 'stat-item-compact';
                statItem.innerHTML = `
                    <span class="stat-label-compact">${month}</span>
                    <div>
                        <span class="stat-value-compact">¥${(Number(data.amount) || 0).toLocaleString()}</span>
                        <span class="stat-percentage">(${data.count}笔, 均¥${(Number(avg) || 0).toLocaleString()})</span>
                    </div>
                `;
                monthlyTrendsDiv.appendChild(statItem);
            });
        }
        
        function updateWeeklyTrends() {
            const currentData = getAnalyticsData();
            const period = document.getElementById('analyticsPeriod')?.value || 'month';
            
            const weeklyData = {};
            currentData.forEach(record => {
                const date = new Date(record.date);
                let weekKey;
                
                if (period === 'month') {
                    const weekNumber = Math.ceil(date.getDate() / 7);
                    weekKey = `第${weekNumber}周`;
                } else {
                    const weekOfYear = getWeekOfYear(date);
                    weekKey = `第${weekOfYear}周`;
                }
                
                if (!weeklyData[weekKey]) {
                    weeklyData[weekKey] = { amount: 0, count: 0 };
                }
                weeklyData[weekKey].amount += record.amount;
                weeklyData[weekKey].count += 1;
            });
            
            const weeklyTrendsDiv = document.getElementById('weeklyTrends');
            weeklyTrendsDiv.innerHTML = '';
            
            if (Object.keys(weeklyData).length === 0) {
                weeklyTrendsDiv.innerHTML = '<div class="stat-item-compact"><span class="stat-label-compact">暂无数据</span></div>';
                return;
            }
            
            Object.keys(weeklyData).forEach(week => {
                const data = weeklyData[week];
                const avg = data.count > 0 ? data.amount / data.count : 0;
                
                const statItem = document.createElement('div');
                statItem.className = 'stat-item-compact';
                statItem.innerHTML = `
                    <span class="stat-label-compact">${week}</span>
                    <div>
                        <span class="stat-value-compact">¥${(Number(data.amount) || 0).toLocaleString()}</span>
                        <span class="stat-percentage">(${data.count}笔, 均¥${(Number(avg) || 0).toLocaleString()})</span>
                    </div>
                `;
                weeklyTrendsDiv.appendChild(statItem);
            });
        }
        
        function getWeekOfYear(date) {
            const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
            const dayNum = d.getUTCDay() || 7;
            d.setUTCDate(d.getUTCDate() + 4 - dayNum);
            const yearStart = new Date(Date.UTC(d.getUTCFullYear(),0,1));
            return Math.ceil((((d - yearStart) / 86400000) + 1)/7);
        }





        // 3. 更新图表可视化
        function updateChartVisualizations() {
            updatePieChart();
            updateCustomerSourceAnalysis();
            updatePerformanceRanking();
            updateUnpaidStats();
        }
        
        // 饼图 - 业务类型分布
        function updatePieChart() {
            const currentData = getAnalyticsData();
            const businessTypeData = {};
            
            currentData.forEach(record => {
                businessTypeData[record.businessType] = (businessTypeData[record.businessType] || 0) + record.amount;
            });
            
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FFB347', '#98D8C8'];
            const sortedTypes = Object.keys(businessTypeData).sort((a, b) => businessTypeData[b] - businessTypeData[a]);
            const total = Object.values(businessTypeData).reduce((sum, val) => sum + val, 0);
            
            // 创建饼图渐变色
            let currentAngle = 0;
            const gradientSegments = sortedTypes.map((type, index) => {
                const percentage = (businessTypeData[type] / total) * 100;
                const angle = (percentage / 100) * 360;
                const segment = `${colors[index % colors.length]} ${currentAngle}deg ${currentAngle + angle}deg`;
                currentAngle += angle;
                return segment;
            }).join(', ');
            
            const pieChart = document.getElementById('businessTypePieChart');
            if (pieChart) {
                pieChart.style.background = `conic-gradient(from 0deg, ${gradientSegments})`;
            }
            
            // 创建图例
            const legend = document.getElementById('pieChartLegend');
            if (legend) {
                legend.innerHTML = sortedTypes.map((type, index) => `
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${colors[index % colors.length]}"></div>
                        <span class="legend-label">${type}</span>
                        <span class="legend-value">¥${(Number(businessTypeData[type]) || 0).toLocaleString()}</span>
                    </div>
                `).join('');
            }
        }
        
        // 客户来源统计分析
        function updateCustomerSourceAnalysis() {
            const currentData = getAnalyticsData();
            const sourceStats = {};
            const totalCustomers = currentData.length;
            
            // 统计各来源的客户数量
            currentData.forEach(record => {
                const source = record.customerSource || '未设置';
                sourceStats[source] = (sourceStats[source] || 0) + 1;
            });
            
            // 预设的客户来源和图标
            const sourceIcons = {
                '自有客户': '👤',
                '运营线索': '📞', 
                '运营转介绍': '🔄',
                '客户转介绍': '🤝',
                '管理内推': '👑'
            };
            
            const container = document.getElementById('customerSourceAnalysis');
            if (container) {
                if (totalCustomers === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; color: #666; font-size: 14px; padding: 40px; grid-column: 1 / -1;">
                            暂无客户数据
                        </div>
                    `;
                } else {
                    // 按预设顺序显示所有来源
                    const allSources = ['自有客户', '运营线索', '运营转介绍', '客户转介绍', '管理内推'];
                    
                    container.innerHTML = allSources.map(source => {
                        const count = sourceStats[source] || 0;
                        const percentage = totalCustomers > 0 ? ((count / totalCustomers) * 100).toFixed(1) : '0.0';
                        const icon = sourceIcons[source] || '📊';
                        
                        return `
                            <div class="source-stat-card">
                                <div class="source-icon">${icon}</div>
                                <div class="source-title">${source}</div>
                                <div class="source-number">${count}</div>
                                <div class="source-percentage">${percentage}%</div>
                            </div>
                        `;
                    }).join('');
                }
            }
        }
        
        // 业绩排名 - 团队业绩对比
        function updatePerformanceRanking() {
            const currentData = getAnalyticsData();
            const salespersonData = {};
            const salespersonCount = {};
            
            currentData.forEach(record => {
                salespersonData[record.salesperson] = (salespersonData[record.salesperson] || 0) + record.amount;
                salespersonCount[record.salesperson] = (salespersonCount[record.salesperson] || 0) + 1;
            });
            
            const sortedPersons = Object.keys(salespersonData).sort((a, b) => salespersonData[b] - salespersonData[a]);
            
            const rankingContainer = document.getElementById('teamPerformanceRanking');
            if (rankingContainer) {
                if (sortedPersons.length === 0) {
                    rankingContainer.innerHTML = '<div class="ranking-empty">暂无业绩数据</div>';
                    return;
                }
                
                rankingContainer.innerHTML = sortedPersons.map((person, index) => {
                    const amount = salespersonData[person];
                    const count = salespersonCount[person];
                    const ranking = index + 1;
                    
                    return `
                        <div class="ranking-item">
                            <div class="ranking-number">${ranking}</div>
                            <div class="ranking-info">
                                <div class="ranking-name">${person}</div>
                                <div class="ranking-stats">
                                    <div class="ranking-amount">¥${(Number(amount) || 0).toLocaleString()}</div>
                                    <div class="ranking-count">${count}笔</div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        }
        
        // 未收款统计
        function updateUnpaidStats() {
            const currentData = getAnalyticsData();
            const unpaidRecords = [];
            let totalUnpaid = 0;
            
            // 计算未收款记录（使用新的收款数据）
            currentData.forEach(record => {
                const contractAmount = record.contractAmount || 0;
                // 优先使用total_received字段，如果没有则使用amount字段
                const receivedAmount = record.total_received || record.amount || 0;
                const unpaidAmount = contractAmount - receivedAmount;

                if (unpaidAmount > 0) {
                    unpaidRecords.push({
                        customer: record.customer,
                        salesperson: record.salesperson,
                        contractAmount: contractAmount,
                        receivedAmount: receivedAmount,
                        unpaidAmount: unpaidAmount,
                        date: record.date,
                        paymentCount: record.payment_count || 0,
                        lastPaymentDate: record.last_payment_date
                    });
                    totalUnpaid += unpaidAmount;
                }
            });
            
            // 按未收款金额排序
            unpaidRecords.sort((a, b) => b.unpaidAmount - a.unpaidAmount);
            
            const container = document.getElementById('unpaidStats');
            if (container) {
                if (unpaidRecords.length === 0) {
                    container.innerHTML = `
                        <div class="unpaid-summary">
                            <div class="unpaid-total">¥0</div>
                            <div class="unpaid-label">恭喜！无未收款项</div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="unpaid-summary">
                            <div class="unpaid-total">¥${(Number(totalUnpaid) || 0).toLocaleString()}</div>
                            <div class="unpaid-label">总未收款金额 • ${unpaidRecords.length}个客户</div>
                        </div>
                        ${unpaidRecords.slice(0, 8).map(record => `
                            <div class="unpaid-detail">
                                <div class="unpaid-customer">${record.customer} (${record.salesperson})</div>
                                <div class="unpaid-amounts">
                                    <span class="contract-amount">合同: ¥${(Number(record.contractAmount) || 0).toLocaleString()}</span>
                                    <span class="received-amount">已收: ¥${(Number(record.receivedAmount) || 0).toLocaleString()}</span>
                                    <span class="unpaid-amount">未收: ¥${(Number(record.unpaidAmount) || 0).toLocaleString()}</span>
                                </div>
                            </div>
                        `).join('')}
                        ${unpaidRecords.length > 8 ? `<div style="text-align: center; color: #666; font-size: 12px; margin-top: 10px;">还有 ${unpaidRecords.length - 8} 个客户...</div>` : ''}
                    `;
                }
            }
        }
        

        
        // 获取分析周期数据的辅助函数
        function getAnalyticsData() {
            const period = document.getElementById('analyticsPeriod')?.value || 'month';
            const now = new Date();
            
            switch (period) {
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return salesData.filter(record => new Date(record.date) >= weekAgo);
                    
                case 'month':
                    const currentMonth = now.toISOString().substring(0, 7);
                    return salesData.filter(record => record.date.startsWith(currentMonth));
                    
                case 'quarter':
                    const currentQuarter = Math.floor(now.getMonth() / 3) + 1;
                    const quarterStart = new Date(now.getFullYear(), (currentQuarter - 1) * 3, 1);
                    return salesData.filter(record => new Date(record.date) >= quarterStart);
                    
                case 'year':
                    const currentYear = now.getFullYear().toString();
                    return salesData.filter(record => record.date.startsWith(currentYear));
                    
                case 'all':
                default:
                    return salesData;
            }
        }
        
        // 获取上一周期数据用于对比
        function getPreviousPeriodData() {
            const period = document.getElementById('analyticsPeriod')?.value || 'month';
            const now = new Date();
            
            switch (period) {
                case 'week':
                    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
                    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return salesData.filter(record => {
                        const recordDate = new Date(record.date);
                        return recordDate >= twoWeeksAgo && recordDate < oneWeekAgo;
                    });
                    
                case 'month':
                    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    const lastMonthStr = lastMonth.toISOString().substring(0, 7);
                    return salesData.filter(record => record.date.startsWith(lastMonthStr));
                    
                case 'quarter':
                    const currentQuarter = Math.floor(now.getMonth() / 3) + 1;
                    const lastQuarter = currentQuarter === 1 ? 4 : currentQuarter - 1;
                    const lastQuarterYear = currentQuarter === 1 ? now.getFullYear() - 1 : now.getFullYear();
                    const lastQuarterStart = new Date(lastQuarterYear, (lastQuarter - 1) * 3, 1);
                    const lastQuarterEnd = new Date(lastQuarterYear, lastQuarter * 3, 0);
                    return salesData.filter(record => {
                        const recordDate = new Date(record.date);
                        return recordDate >= lastQuarterStart && recordDate <= lastQuarterEnd;
                    });
                    
                case 'year':
                    const lastYear = (now.getFullYear() - 1).toString();
                    return salesData.filter(record => record.date.startsWith(lastYear));
                    
                case 'all':
                default:
                    return [];
            }
        }

        // 4. 更新智能洞察
        function updateSmartInsights() {
            const currentData = getAnalyticsData();
            const previousData = getPreviousPeriodData();
            const insights = [];
            
            if (currentData.length === 0) {
                insights.push({
                    icon: '📊',
                    text: '暂无数据，请添加销售记录开始分析'
                });
            } else {
                // 基于核心业绩指标进行深度分析
                const kpiData = calculateKPIData(currentData);
                const prevKpiData = calculateKPIData(previousData);
                
                // 1. 总收入分析
                const revenueGrowth = prevKpiData.totalRevenue > 0 ? 
                    ((kpiData.totalRevenue - prevKpiData.totalRevenue) / prevKpiData.totalRevenue * 100) : 0;
                    
                if (revenueGrowth > 30) {
                    insights.push({
                        icon: '🚀',
                        text: `总收入暴涨 ${revenueGrowth.toFixed(1)}%！业绩爆发式增长，建议迅速扩大团队规模，抢占市场份额。`
                    });
                } else if (revenueGrowth > 15) {
                    insights.push({
                        icon: '📈',
                        text: `总收入强劲增长 ${revenueGrowth.toFixed(1)}%，发展势头良好。建议加大优质渠道投入，巩固增长基础。`
                    });
                } else if (revenueGrowth > 0) {
                    insights.push({
                        icon: '✅',
                        text: `总收入温和增长 ${revenueGrowth.toFixed(1)}%，保持稳定。建议分析增长瓶颈，寻找新的突破点。`
                    });
                } else if (revenueGrowth < -10) {
                    insights.push({
                        icon: '🔥',
                        text: `总收入下降 ${Math.abs(revenueGrowth).toFixed(1)}%，需紧急行动！建议立即召开业务复盘会议，制定挽回策略。`
                    });
                }
                
                // 2. 成交笔数分析
                const dealGrowth = prevKpiData.totalDeals > 0 ? 
                    ((kpiData.totalDeals - prevKpiData.totalDeals) / prevKpiData.totalDeals * 100) : 0;
                    
                if (kpiData.totalDeals === 0) {
                    insights.push({
                        icon: '⚠️',
                        text: '成交笔数为零，业务停滞！建议立即启动客户开发计划，激活销售活动。'
                    });
                } else if (dealGrowth > 20) {
                    insights.push({
                        icon: '🎯',
                        text: `成交笔数激增 ${dealGrowth.toFixed(1)}%，客户开发成效显著。建议优化成交流程，提升转化效率。`
                    });
                } else if (dealGrowth < -15) {
                    insights.push({
                        icon: '📞',
                        text: `成交笔数减少 ${Math.abs(dealGrowth).toFixed(1)}%，客户开发乏力。建议加强线索获取，提升跟进频率。`
                    });
                }
                
                // 3. 活跃销售分析
                if (kpiData.activeSalespeople === 0) {
                    insights.push({
                        icon: '🚨',
                        text: '团队无活跃销售人员，组织运转异常！建议立即检查团队状态，重新激活销售动力。'
                    });
                } else if (kpiData.activeSalespeople === 1) {
                    insights.push({
                        icon: '⚡',
                        text: '仅1人活跃销售，团队依赖风险高。建议加强团队培训，提升整体参与度。'
                    });
                } else if (kpiData.activeSalespeople >= 5) {
                    insights.push({
                        icon: '👥',
                        text: `${kpiData.activeSalespeople}人活跃销售，团队动力充沛！建议设置团队竞赛，进一步激发潜能。`
                    });
                }
                
                // 4. 线索成交分析（运营线索+运营转介绍）
                const leadConversionRate = kpiData.totalDeals > 0 ? (kpiData.leadDeals / kpiData.totalDeals * 100) : 0;
                
                if (leadConversionRate > 60) {
                    insights.push({
                        icon: '🎖️',
                        text: `线索成交占比 ${leadConversionRate.toFixed(1)}%，运营效果优秀！建议扩大线索投入，放大运营优势。`
                    });
                } else if (leadConversionRate > 30) {
                    insights.push({
                        icon: '🌟',
                        text: `线索成交占比 ${leadConversionRate.toFixed(1)}%，运营贡献稳定。建议优化线索质量，提升转化精度。`
                    });
                } else if (leadConversionRate < 10) {
                    insights.push({
                        icon: '🔍',
                        text: `线索成交占比仅 ${leadConversionRate.toFixed(1)}%，运营效果待提升。建议重新评估线索渠道质量。`
                    });
                }
                
                // 5. 欠款风险分析
                const debtRatio = kpiData.totalRevenue > 0 ? (kpiData.totalDebt / kpiData.totalRevenue * 100) : 0;
                
                if (kpiData.totalDebt === 0) {
                    insights.push({
                        icon: '💎',
                        text: '无欠款记录，资金回收完美！财务健康状况优秀，现金流充沛。'
                    });
                } else if (debtRatio > 50) {
                    insights.push({
                        icon: '⚠️',
                        text: `欠款比例高达 ${debtRatio.toFixed(1)}%，资金风险严重！建议立即启动催收计划，加强合同付款条款。`
                    });
                } else if (debtRatio > 30) {
                    insights.push({
                        icon: '💰',
                        text: `欠款比例 ${debtRatio.toFixed(1)}%，需关注回款进度。建议优化付款流程，提升客户付款体验。`
                    });
                } else if (debtRatio < 15) {
                    insights.push({
                        icon: '✨',
                        text: `欠款比例仅 ${debtRatio.toFixed(1)}%，回款管理良好。财务风险控制优秀，值得维持。`
                    });
                }
                
                // 6. 平均订单价值分析
                const avgOrderValue = kpiData.totalDeals > 0 ? kpiData.totalRevenue / kpiData.totalDeals : 0;
                
                if (avgOrderValue > 50000) {
                    insights.push({
                        icon: '👑',
                        text: `平均订单价值达 ¥${(Number(avgOrderValue) || 0).toLocaleString()}，高端客户集中。建议深耕大客户市场，提供定制化服务。`
                    });
                } else if (avgOrderValue > 20000) {
                    insights.push({
                        icon: '💼',
                        text: `平均订单价值 ¥${(Number(avgOrderValue) || 0).toLocaleString()}，中高端客户为主。建议平衡发展，扩大客户群体。`
                    });
                } else if (avgOrderValue < 5000) {
                    insights.push({
                        icon: '📈',
                        text: `平均订单价值仅 ¥${(Number(avgOrderValue) || 0).toLocaleString()}，客单价偏低。建议开发高价值产品，提升客单价。`
                    });
                }
                
                // 7. 综合健康度评估
                const healthScore = calculateBusinessHealthScore(kpiData, revenueGrowth, dealGrowth);
                
                if (healthScore >= 85) {
                    insights.push({
                        icon: '🏆',
                        text: `业务健康度 ${healthScore}分，表现卓越！建议保持当前策略，适度扩张规模，巩固竞争优势。`
                    });
                } else if (healthScore >= 70) {
                    insights.push({
                        icon: '💪',
                        text: `业务健康度 ${healthScore}分，运营良好。建议重点优化薄弱环节，提升整体水平。`
                    });
                } else if (healthScore >= 50) {
                    insights.push({
                        icon: '⚖️',
                        text: `业务健康度 ${healthScore}分，发展平稳。建议制定改进计划，重点突破关键指标。`
                    });
                } else {
                    insights.push({
                        icon: '🔧',
                        text: `业务健康度 ${healthScore}分，需要改进！建议全面审视业务流程，制定紧急优化方案。`
                    });
                }
                
                // 8. 客户来源价值分析
                const sourceAnalysis = analyzeCustomerSources(currentData);
                if (sourceAnalysis.bestSource) {
                    insights.push({
                        icon: '🎯',
                        text: `${sourceAnalysis.bestSource}是黄金渠道，贡献 ${sourceAnalysis.bestSourcePercent}% 业绩。建议加大投入，深度挖掘。`
                    });
                }
                
                // 9. 业务结构风险预警
                const businessRisk = analyzeBusinessRisk(currentData);
                if (businessRisk.isHighRisk) {
                    insights.push({
                        icon: '⚠️',
                        text: `${businessRisk.dominantType}业务占比过高(${businessRisk.dominantPercent}%)，存在集中风险。建议分散业务结构。`
                    });
                }
            }
            
            const smartInsightsDiv = document.getElementById('smartInsights');
            smartInsightsDiv.innerHTML = insights.map(insight => `
                <div class="insight-item">
                    <span class="insight-icon">${insight.icon}</span>
                    <span class="insight-text">${insight.text}</span>
                </div>
            `).join('');
        }

        // 计算KPI数据的辅助函数
        function calculateKPIData(data) {
            if (!data || data.length === 0) {
                return {
                    totalRevenue: 0,
                    totalDeals: 0,
                    activeSalespeople: 0,
                    leadDeals: 0,
                    totalDebt: 0,
                    debtCount: 0
                };
            }

            const totalRevenue = data.reduce((sum, record) => sum + record.amount, 0);
            const totalDeals = data.length;
            
            // 活跃销售人员数量
            const salespeople = new Set();
            data.forEach(record => salespeople.add(record.salesperson));
            const activeSalespeople = salespeople.size;
            
            // 线索成交数量（运营线索+运营转介绍）
            const leadDeals = data.filter(record => {
                const source = record.customerSource || '';
                return source.includes('运营线索') || source.includes('运营转介绍');
            }).length;
            
            // 欠款统计
            let totalDebt = 0;
            let debtCount = 0;
            data.forEach(record => {
                const debt = (record.contractAmount || 0) - (record.amount || 0);
                if (debt > 0) {
                    totalDebt += debt;
                    debtCount++;
                }
            });

            return {
                totalRevenue,
                totalDeals,
                activeSalespeople,
                leadDeals,
                totalDebt,
                debtCount
            };
        }

        // 计算业务健康度评分
        function calculateBusinessHealthScore(kpiData, revenueGrowth, dealGrowth) {
            let score = 50; // 基础分

            // 收入增长评分 (30分)
            if (revenueGrowth > 20) score += 30;
            else if (revenueGrowth > 10) score += 20;
            else if (revenueGrowth > 0) score += 10;
            else if (revenueGrowth > -10) score += 5;
            else score -= 10;

            // 成交笔数评分 (20分)
            if (dealGrowth > 15) score += 20;
            else if (dealGrowth > 5) score += 15;
            else if (dealGrowth > 0) score += 10;
            else if (dealGrowth > -10) score += 5;
            else score -= 10;

            // 团队活跃度评分 (20分)
            if (kpiData.activeSalespeople >= 5) score += 20;
            else if (kpiData.activeSalespeople >= 3) score += 15;
            else if (kpiData.activeSalespeople >= 2) score += 10;
            else if (kpiData.activeSalespeople >= 1) score += 5;
            else score -= 20;

            // 欠款风险评分 (20分)
            const debtRatio = kpiData.totalRevenue > 0 ? (kpiData.totalDebt / kpiData.totalRevenue) : 0;
            if (debtRatio === 0) score += 20;
            else if (debtRatio < 0.1) score += 15;
            else if (debtRatio < 0.3) score += 10;
            else if (debtRatio < 0.5) score += 5;
            else score -= 10;

            // 线索转化评分 (10分)
            const leadConversionRate = kpiData.totalDeals > 0 ? (kpiData.leadDeals / kpiData.totalDeals) : 0;
            if (leadConversionRate > 0.5) score += 10;
            else if (leadConversionRate > 0.3) score += 8;
            else if (leadConversionRate > 0.1) score += 5;
            else score += 2;

            return Math.max(0, Math.min(100, Math.round(score)));
        }

        // 分析客户来源价值
        function analyzeCustomerSources(data) {
            const sources = {};
            let totalRevenue = 0;

            data.forEach(record => {
                const source = record.customerSource || '未设置';
                sources[source] = (sources[source] || 0) + record.amount;
                totalRevenue += record.amount;
            });

            if (totalRevenue === 0 || Object.keys(sources).length === 0) {
                return { bestSource: null, bestSourcePercent: 0 };
            }

            const sortedSources = Object.keys(sources).sort((a, b) => sources[b] - sources[a]);
            const bestSource = sortedSources[0];
            const bestSourcePercent = ((sources[bestSource] / totalRevenue) * 100).toFixed(1);

            // 只有当最佳来源不是"未设置"且占比超过15%时才推荐
            if (bestSource !== '未设置' && parseFloat(bestSourcePercent) > 15) {
                return { bestSource, bestSourcePercent };
            }

            return { bestSource: null, bestSourcePercent: 0 };
        }

        // 分析业务结构风险
        function analyzeBusinessRisk(data) {
            const businessTypes = {};
            let totalRevenue = 0;

            data.forEach(record => {
                businessTypes[record.businessType] = (businessTypes[record.businessType] || 0) + record.amount;
                totalRevenue += record.amount;
            });

            if (totalRevenue === 0 || Object.keys(businessTypes).length === 0) {
                return { isHighRisk: false, dominantType: null, dominantPercent: 0 };
            }

            const sortedTypes = Object.keys(businessTypes).sort((a, b) => businessTypes[b] - businessTypes[a]);
            const dominantType = sortedTypes[0];
            const dominantPercent = ((businessTypes[dominantType] / totalRevenue) * 100).toFixed(1);

            // 当某一业务类型占比超过70%时认为是高风险
            const isHighRisk = parseFloat(dominantPercent) > 70;

            return { isHighRisk, dominantType, dominantPercent };
        }

        // 更新工资计算
        function updateSalaryDetails() {
            const currentMonth = new Date().toISOString().substring(0, 7);
            const monthRecords = salesData.filter(record => record.date.startsWith(currentMonth));
            
            const salaryData = {};
            
            // 🔧 修复：初始化所有销售人员的数据 - 正确使用字段名
            systemSettings.salespeople.forEach(person => {
                salaryData[person.name] = {
                    totalSales: 0,
                    // 🔧 修复：兼容新旧字段名格式
                    baseSalary: Number(person.baseSalary || person.base_salary) || 0,
                    commissionType: person.commissionType || person.commission_type || 'fixed',
                    fixedRate: Number(person.fixedRate || person.fixed_rate) || 0,
                    ladderRates: person.ladderRates || person.ladder_rates || [],
                    commission: 0,
                    totalSalary: 0
                };

                // 🔍 调试信息：检查销售人员数据
                console.log(`💰 初始化销售人员 ${person.name}:`, {
                    baseSalary: salaryData[person.name].baseSalary,
                    commissionType: salaryData[person.name].commissionType,
                    fixedRate: salaryData[person.name].fixedRate,
                    ladderRates: salaryData[person.name].ladderRates.length
                });
            });
            
            // 统计销售额
            monthRecords.forEach(record => {
                if (salaryData[record.salesperson]) {
                salaryData[record.salesperson].totalSales += record.amount;
                }
            });
            
            // 计算提成和总工资
            Object.keys(salaryData).forEach(person => {
                const data = salaryData[person];
                
                if (data.commissionType === 'ladder' && data.ladderRates.length > 0) {
                    // 阶梯式提成计算
                    data.commission = calculateLadderCommission(data.totalSales, data.ladderRates);
                } else {
                    // 固定比例提成
                    data.commission = data.totalSales * data.fixedRate;
                }
                
                data.totalSalary = data.baseSalary + data.commission;
            });
            
            const salaryDetailsDiv = document.getElementById('salaryDetails');
            salaryDetailsDiv.innerHTML = '';
            
            Object.keys(salaryData).forEach(person => {
                const data = salaryData[person];
                let commissionText = '';
                let commissionDetail = '';
                
                // 🔧 修复：添加详细的调试信息
                console.log(`💰 处理 ${person} 的工资详情:`, {
                    totalSales: data.totalSales,
                    commissionType: data.commissionType,
                    ladderRates: data.ladderRates.length,
                    fixedRate: data.fixedRate
                });

                if (data.commissionType === 'ladder') {
                    commissionText = '阶梯式';
                    // 🔧 修复：确保阶梯式提成详情显示
                    if (data.ladderRates.length > 0) {
                        if (data.totalSales > 0) {
                            const ladderDetails = calculateLadderCommissionDetails(data.totalSales, data.ladderRates);
                            commissionDetail = `<div style="font-size: 12px; color: #666; margin-top: 8px; padding: 8px; background: #f0f8ff; border-radius: 4px; border-left: 3px solid #2196F3;"><strong>📊 阶梯式提成计算:</strong><br>${ladderDetails}</div>`;
                            console.log(`✅ ${person} 阶梯详情已生成: ${ladderDetails}`);
                        } else {
                            commissionDetail = `<div style="font-size: 12px; color: #ff9800; margin-top: 8px; padding: 8px; background: #fff3e0; border-radius: 4px; border-left: 3px solid #ff9800;"><strong>💡 阶梯式提成说明:</strong><br>本月暂无销售业绩，提成为¥0</div>`;
                            console.log(`ℹ️ ${person} 本月无销售业绩`);
                        }
                    } else {
                        commissionDetail = `<div style="font-size: 12px; color: #ff9800; margin-top: 8px; padding: 8px; background: #fff3e0; border-radius: 4px; border-left: 3px solid #ff9800;"><strong>⚠️ 提醒:</strong><br>该员工设置为阶梯式提成，但未配置阶梯比例</div>`;
                        console.log(`⚠️ ${person} 未配置阶梯比例`);
                    }
                } else {
                    commissionText = `${(data.fixedRate*100).toFixed(1)}%`;
                    // 🔧 修复：确保固定比例提成详情显示
                    if (data.fixedRate > 0) {
                        if (data.totalSales > 0) {
                            const salesText = data.totalSales.toLocaleString();
                            const rateText = (data.fixedRate * 100).toFixed(1);
                            const commissionAmount = data.commission.toFixed(2);
                            commissionDetail = `<div style="font-size: 12px; color: #666; margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #28a745;"><strong>📊 固定比例提成计算:</strong><br>¥${salesText} × ${rateText}% = ¥${commissionAmount}</div>`;
                            console.log(`✅ ${person} 固定比例详情已生成`);
                        } else {
                            commissionDetail = `<div style="font-size: 12px; color: #ff9800; margin-top: 8px; padding: 8px; background: #fff3e0; border-radius: 4px; border-left: 3px solid #ff9800;"><strong>💡 固定比例提成说明:</strong><br>本月暂无销售业绩，提成为¥0</div>`;
                            console.log(`ℹ️ ${person} 本月无销售业绩`);
                        }
                    } else {
                        commissionDetail = `<div style="font-size: 12px; color: #ff9800; margin-top: 8px; padding: 8px; background: #fff3e0; border-radius: 4px; border-left: 3px solid #ff9800;"><strong>⚠️ 提醒:</strong><br>该员工的提成比例为0%，请检查设置</div>`;
                        console.log(`⚠️ ${person} 提成比例为0%`);
                    }
                }
                
                const detailDiv = document.createElement('div');
                detailDiv.style.marginBottom = '20px';
                detailDiv.style.padding = '15px';
                detailDiv.style.backgroundColor = '#f8f9fa';
                detailDiv.style.borderRadius = '8px';
                detailDiv.innerHTML = `
                    <h4>${person}</h4>
                    <div class="stat-item">
                        <span class="stat-label">销售业绩:</span>
                        <span class="stat-value">¥${data.totalSales.toFixed(2)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">基础工资:</span>
                        <span class="stat-value">¥${data.baseSalary.toFixed(2)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">提成 (${commissionText}):</span>
                        <span class="stat-value">¥${data.commission.toFixed(2)}</span>
                    </div>
                    ${commissionDetail}
                    <div class="stat-item">
                        <span class="stat-label">总工资:</span>
                        <span class="stat-value" style="font-size: 1.2em; color: #4CAF50;">¥${data.totalSalary.toFixed(2)}</span>
                    </div>
                `;
                salaryDetailsDiv.appendChild(detailDiv);
            });
        }
        
        // 计算阶梯式提成（累进式计算）
        function calculateLadderCommission(totalSales, ladderRates) {
            if (!ladderRates || ladderRates.length === 0) {
                return 0;
            }
            
            let commission = 0;
            
            // 按金额从小到大排序
            const sortedRates = ladderRates.sort((a, b) => a.minAmount - b.minAmount);
            
            for (let i = 0; i < sortedRates.length; i++) {
                const current = sortedRates[i];
                const next = sortedRates[i + 1];
                
                // 如果销售额还没达到当前阶梯的起始金额，跳过
                if (totalSales <= current.minAmount) {
                    break;
                }
                
                // 计算当前阶梯适用的金额
                let applicableAmount;
                if (next) {
                    // 有下一个阶梯，计算到下一个阶梯起始金额的部分
                    applicableAmount = Math.min(totalSales, next.minAmount) - current.minAmount;
                } else {
                    // 最高阶梯，计算超出当前阶梯起始金额的所有部分
                    applicableAmount = totalSales - current.minAmount;
                }
                
                // 如果适用金额大于0，计算提成
                if (applicableAmount > 0) {
                    commission += applicableAmount * current.rate;
                }
            }
            
            return commission;
        }

        // 计算阶梯式提成并返回详细计算过程
        function calculateLadderCommissionDetails(totalSales, ladderRates) {
            if (!ladderRates || ladderRates.length === 0) {
                return '未设置阶梯';
            }
            
            let details = [];
            let commission = 0;
            
            // 按金额从小到大排序
            const sortedRates = ladderRates.sort((a, b) => a.minAmount - b.minAmount);
            
            for (let i = 0; i < sortedRates.length; i++) {
                const current = sortedRates[i];
                const next = sortedRates[i + 1];
                
                // 如果销售额还没达到当前阶梯的起始金额，跳过
                if (totalSales <= current.minAmount) {
                    break;
                }
                
                // 计算当前阶梯适用的金额
                let applicableAmount;
                if (next) {
                    // 有下一个阶梯，计算到下一个阶梯起始金额的部分
                    applicableAmount = Math.min(totalSales, next.minAmount) - current.minAmount;
                } else {
                    // 最高阶梯，计算超出当前阶梯起始金额的所有部分
                    applicableAmount = totalSales - current.minAmount;
                }
                
                // 如果适用金额大于0，计算提成并记录详情
                if (applicableAmount > 0) {
                    const stepCommission = applicableAmount * current.rate;
                    commission += stepCommission;
                    
                    const amountText = (applicableAmount / 10000).toFixed(2);
                    const rateText = (current.rate * 100).toFixed(1);
                    const commissionText = stepCommission.toFixed(2);
                    
                    details.push(`${amountText}万×${rateText}%=${commissionText}元`);
                }
            }
            
            if (details.length > 0) {
                const totalCommissionText = commission.toFixed(2);
                return `计算: ${details.join(' + ')} = ¥${totalCommissionText}`;
            } else {
                return '暂无提成';
            }
        }

        // 生成日报
        function generateDailySummary() {
            const today = new Date().toISOString().split('T')[0];
            const todayRecords = salesData.filter(record => record.date === today);
            
            if (todayRecords.length === 0) {
                alert('今日暂无入账记录！');
                    return;
                }

            const totalAmount = todayRecords.reduce((sum, record) => sum + record.amount, 0);
            const salespersonSummary = {};
            
            todayRecords.forEach(record => {
                if (!salespersonSummary[record.salesperson]) {
                    salespersonSummary[record.salesperson] = 0;
                }
                salespersonSummary[record.salesperson] += record.amount;
            });
            
            let summaryText = `📅 ${today} 日入账统计\n\n`;
            summaryText += `💰 今日总入账: ¥${totalAmount.toFixed(2)}\n`;
            summaryText += `📊 入账笔数: ${todayRecords.length}笔\n\n`;
            summaryText += `👥 销售业绩:\n`;
            
            Object.keys(salespersonSummary).forEach(person => {
                summaryText += `   ${person}: ¥${salespersonSummary[person].toFixed(2)}\n`;
            });
            
            document.getElementById('summaryContent').innerHTML = summaryText.replace(/\n/g, '<br>');
            document.getElementById('dailySummary').style.display = 'block';
            
            // 保存到全局变量，用于复制
            window.currentSummary = summaryText;
        }

        // 复制到剪贴板
        function copyToClipboard() {
            if (window.currentSummary) {
                navigator.clipboard.writeText(window.currentSummary).then(() => {
                    alert('✅ 日报已复制到剪贴板！');
                });
            }
        }

        // 标签页切换（优化性能 + 密码保护）
        function showTab(tabName) {
            // 需要密码保护的页面
            const protectedTabs = ['statistics', 'salary', 'settings'];
            
            if (protectedTabs.includes(tabName)) {
                // 检查是否已经验证过密码
                const isAuthenticated = sessionStorage.getItem('adminAuthenticated') === 'true';
                
                if (!isAuthenticated) {
                    // 弹出密码输入框
                    const password = prompt('🔒 请输入管理员密码访问此功能：');
                    
                    // 用户取消输入
                    if (password === null) {
                        return;
                    }
                    
                    // 固定密码：litang688
                    if (password !== 'litang688') {
                        alert('❌ 密码错误，无法访问此页面！\n\n提示：如需访问权限，请联系系统管理员。');
                        return;
                    }
                    
                    // 密码正确，设置会话标记
                    sessionStorage.setItem('adminAuthenticated', 'true');
                    alert('✅ 验证成功！已获得管理员权限。\n\n注意：权限将在24小时后自动过期。');
                    
                    // 管理员权限功能已迁移到云端用户系统
                    console.log('ℹ️ 管理员权限功能已迁移到云端用户系统');
                    
                    // 设置24小时后自动过期（保留用于兼容性）
                    setTimeout(() => {
                        sessionStorage.removeItem('adminAuthenticated');
                        console.log('ℹ️ 旧版管理员权限已清理');
                        console.log('管理员权限已过期');
                    }, 24 * 60 * 60 * 1000);
                }
            }
            
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 根据不同标签页加载相应数据
            if (tabName === 'statistics') {
                // 延迟计算统计数据，避免阻塞UI
                setTimeout(() => {
                    updateAllStatistics();
                }, 50);
            } else if (tabName === 'settings') {
                // 切换到系统设置时重新加载设置数据
                setTimeout(() => {
                    loadSettingsData();
                }, 50);
            } else if (tabName === 'salary') {
                // 切换到工资计算时更新工资详情
                setTimeout(() => {
                    updateSalaryDetails();
                }, 50);
            }
        }

        // 管理员权限管理 - 已迁移到云端用户系统
        function updateAdminStatus() {
            // 该功能已被云端用户认证系统替代，保留函数避免调用错误
            console.log('ℹ️ 管理员权限已迁移到云端用户系统');
        }

        function logoutAdmin() {
            if (confirm('确定要退出管理员权限吗？\n\n退出后需要重新输入密码才能访问受保护的页面。\n权限有效期：24小时')) {
                sessionStorage.removeItem('adminAuthenticated');
                console.log('ℹ️ 旧版管理员权限已退出');
                
                // 如果当前在受保护页面，自动切换到数据展示页面
                const currentTab = document.querySelector('.tab.active');
                if (currentTab && (currentTab.textContent.includes('统计分析') || 
                                  currentTab.textContent.includes('工资计算') || 
                                  currentTab.textContent.includes('系统设置'))) {
                    showTab('records');
                }
                
                alert('✅ 已成功退出管理员权限！');
            }
        }

        // 页面加载时检查管理员状态（已迁移到云端用户系统）
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ℹ️ 管理员状态检查已迁移到云端用户系统');
        });

        // 销售励志名言库
        const salesMottos = [
            "凝心聚力 勇争第一",
            "业绩为王 永不服输",
            "拼搏奋斗 共创辉煌",
            "超越自我 追求卓越",
            "团结协作 勇攀高峰",
            "精诚所至 金石为开",
            "志存高远 脚踏实地",
            "努力今天 成就明天",
            "专业专注 永创佳绩",
            "激情燃烧 梦想成真",
            "勇往直前 永不言败",
            "诚信经营 服务至上",
            "创新突破 引领未来",
            "厚德载物 自强不息",
            "心无旁骛 专注目标",
            "百折不挠 勇创新高",
            "精益求精 追求完美",
            "携手并进 共赢未来",
            "敢为人先 勇立潮头",
            "坚持不懈 终获成功"
        ];

        // 获取随机销售名言
        function getRandomMotto() {
            const randomIndex = Math.floor(Math.random() * salesMottos.length);
            return salesMottos[randomIndex];
        }

        // 喜报弹窗功能
                // 全局存储当前弹窗的记录数据
        let currentCelebrationRecord = null;

        function showCelebrationModal(recordData) {
            console.log('🎉 触发喜报弹窗', recordData);
            
            // 存储当前记录数据
            currentCelebrationRecord = recordData;
            
            const modal = document.getElementById('celebrationModal');
            const timeElement = document.getElementById('celebrationTime');
            const detailsElement = document.getElementById('celebrationDetails');
            const mottoElement = document.getElementById('celebrationMotto');
                
            if (!modal) {
                console.error('❌ 找不到喜报弹窗元素');
                    return;
                }

            // 设置随机励志名言
            if (mottoElement) {
                mottoElement.textContent = getRandomMotto();
            }
            
            // 设置时间
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = `记录时间：${timeStr}`;
            
            // 计算该销售人员的总业绩统计
            const salespersonRecords = salesData.filter(record => record.salesperson === recordData.salesperson);
            const totalAmount = salespersonRecords.reduce((sum, record) => sum + record.amount, 0);
            const totalDeals = salespersonRecords.length;
            const avgDealAmount = totalDeals > 0 ? totalAmount / totalDeals : 0;
            
            // 计算本月业绩
            const currentMonth = new Date().toISOString().substring(0, 7);
            const monthRecords = salespersonRecords.filter(record => record.date.startsWith(currentMonth));
            const monthAmount = monthRecords.reduce((sum, record) => sum + record.amount, 0);
            const monthDeals = monthRecords.length;
            
            // 设置详细信息 - 优化为手机9:16垂直布局
            detailsElement.innerHTML = `
                <!-- 主要信息区域 -->
                <div class="celebration-main-info">
                    <div class="celebration-highlight-amount">
                        <div class="amount-label">本笔收入</div>
                        <div class="amount-value">¥${(Number(recordData.amount) || 0).toLocaleString()}</div>
                    </div>
                    <div class="celebration-key-info">
                        <div class="key-info-item">
                            <span class="key-label">客户</span>
                            <span class="key-value">${recordData.customer}</span>
                        </div>
                        <div class="key-info-item">
                            <span class="key-label">销售员</span>
                            <span class="key-value">${recordData.salesperson}</span>
                        </div>
                        <div class="key-info-item">
                            <span class="key-label">业务类型</span>
                            <span class="key-value">${recordData.businessType}</span>
                        </div>
                    </div>
                </div>
                
                <!-- 垂直信息布局 -->
                <div class="celebration-three-columns">
                    <!-- 当前记录详情 -->
                    <div class="celebration-column">
                        <h5 class="column-title">📋 本次记录详情</h5>
                        <div class="mini-info-grid">
                            <div class="mini-info-item">
                                <span class="mini-label">来源</span>
                                <span class="mini-value">${recordData.customerSource}</span>
                            </div>
                            <div class="mini-info-item">
                                <span class="mini-label">类型</span>
                                <span class="mini-value">${recordData.incomeType}</span>
                            </div>
                            ${recordData.contractAmount ? `
                            <div class="mini-info-item">
                                <span class="mini-label">合同</span>
                                <span class="mini-value">¥${(Number(recordData.contractAmount) || 0).toLocaleString()}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    <!-- 业绩统计卡片 - 横向排列 -->
                    <div class="performance-cards">
                        <!-- 历史总业绩卡片 -->
                        <div class="performance-card">
                            <h5 class="card-title">🏆 历史总业绩</h5>
                            <div class="card-stats">
                                <div class="card-stat-item">
                                    <div class="card-stat-number">¥${(Number(totalAmount) || 0).toLocaleString()}</div>
                                    <div class="card-stat-desc">总收入</div>
                                </div>
                                <div class="card-stat-item">
                                    <div class="card-stat-number">${totalDeals}</div>
                                    <div class="card-stat-desc">笔数</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 本月业绩卡片 -->
                        <div class="performance-card">
                            <h5 class="card-title">📅 本月业绩</h5>
                            <div class="card-stats">
                                <div class="card-stat-item">
                                    <div class="card-stat-number">¥${(Number(monthAmount) || 0).toLocaleString()}</div>
                                    <div class="card-stat-desc">本月收入</div>
                                </div>
                                <div class="card-stat-item">
                                    <div class="card-stat-number">${monthDeals}</div>
                                    <div class="card-stat-desc">笔数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                ${recordData.remarks ? `
                <div class="celebration-remarks">
                    <span class="remarks-label">💬</span>
                    <span class="remarks-text">${recordData.remarks}</span>
                </div>
                ` : ''}
            `;
            
            // 显示弹窗
            modal.style.display = 'block';
            
            // 播放成功音效（如果浏览器支持）
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmMcBjuX2fDGeSsFJoHO8deIOAcZaLvt559NEAxPqOPwt2McBjuP2fDIfCsFJoHO8deIOAcZaLvt559NEAxPpuPwuGQcBjiR1/LMeS0FI3fH8N2QQAoUXrTp66hVFApGnt/zv2QcBjiR1/LNeSsFJYDO8daIOAcZaLvt559MEAxPpuPwuGQcBjiR1/LNeSsFJYDO8daIOAcZaLvt559MEAxPpuPwuGQcBjiR1/LNeSsFJYDO8daIOAcZaLvt559MEAxPpuPwuGQcBjiR1/LNeSsFJYDO8daIOA==');
                audio.play().catch(() => {}); // 忽略播放失败
            } catch (e) {}
            
            // 弹窗将永久显示，只能手动关闭（便于用户截图）
            console.log('💡 喜报弹窗已显示，只能手动关闭，便于截图保存');
        }

        function closeCelebrationModal() {
            const modal = document.getElementById('celebrationModal');
            modal.style.display = 'none';
            
            // 清理存储的记录数据
            currentCelebrationRecord = null;
        }

        // Toast 提示函数
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 300px;
                animation: slideInRight 0.3s ease-out;
            `;
            toast.textContent = message;
            
            // 添加样式
            if (!document.getElementById('toast-styles')) {
                const style = document.createElement('style');
                style.id = 'toast-styles';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOutRight {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(toast);
            
            // 3秒后移除
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 记录html2canvas失败次数
        let html2canvasFailCount = parseInt(localStorage.getItem('html2canvasFailCount') || '0');
        
        // 复制庆祝弹窗图片到剪贴板
        async function copyModalImage() {
            console.log('📋 复制按钮被点击');
            
            try {
                const modal = document.querySelector('.celebration-content');
                if (!modal) {
                    throw new Error('弹窗元素未找到');
                }

                // 显示复制状态
                const copyBtn = document.querySelector('.celebration-copy');
                if (!copyBtn) {
                    throw new Error('复制按钮未找到');
                }
                
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '⏳';
                copyBtn.style.pointerEvents = 'none';
                
                console.log('🔄 开始处理截图...');
                showToast('正在生成战报图片...', 'info');
                
                // 智能模式：如果html2canvas连续失败3次以上，直接使用降级方案
                if (html2canvasFailCount >= 3) {
                    console.log('🔀 检测到html2canvas多次失败，直接使用降级方案');
                    showToast('使用优化模式生成图片...', 'info');
                    
                    const currentRecord = extractRecordDataFromModal(modal);
                    const canvas = await createTextBasedReport(currentRecord);
                    console.log('✅ 智能降级方案生成完成，尺寸:', canvas.width, 'x', canvas.height);
                    
                    await handleCanvasCopy(canvas, copyBtn, originalText);
                    return;
                }

                // 检查html2canvas库
                if (typeof html2canvas === 'undefined') {
                    console.log('⏰ 等待html2canvas库加载...');
                    showToast('正在加载截图库，请稍候...', 'info');
                    
                    try {
                        await waitForLibrary(() => typeof html2canvas !== 'undefined', 15000);
                        console.log('✅ html2canvas库加载成功');
                        showToast('截图库加载成功！', 'success');
                    } catch (loadError) {
                        console.error('❌ html2canvas库加载失败，使用降级方案');
                        showToast('截图库加载失败，使用简化模式生成图片...', 'info');
                        
                        // 使用降级方案：创建文本版战报
                        const currentRecord = extractRecordDataFromModal(modal);
                        
                        console.log('📝 使用文本版战报生成...');
                        const canvas = await createTextBasedReport(currentRecord);
                        console.log('✅ 文本版战报生成完成，尺寸:', canvas.width, 'x', canvas.height);
                        
                        // 直接跳到复制逻辑
                        await handleCanvasCopy(canvas, copyBtn, originalText);
                        return;
                    }
                }

                let canvas;
                try {
                    // 临时移除可能干扰截图的样式
                    const originalStyles = {};
                    const elementsToFix = modal.querySelectorAll('*');
                    
                    // 保存并修改样式以确保html2canvas兼容
                    elementsToFix.forEach((el, index) => {
                        originalStyles[index] = {
                            backdropFilter: el.style.backdropFilter,
                            webkitBackdropFilter: el.style.webkitBackdropFilter,
                            filter: el.style.filter
                        };
                        
                        // 临时移除毛玻璃效果
                        el.style.backdropFilter = 'none';
                        el.style.webkitBackdropFilter = 'none';
                    });
                    
                    // 创建高质量截图
                    console.log('📸 开始截图...');
                    canvas = await html2canvas(modal, {
                        backgroundColor: '#ffffff',
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        logging: false,
                        width: modal.offsetWidth,
                        height: modal.offsetHeight,
                        foreignObjectRendering: false,
                        removeContainer: false,
                        async: true,
                        proxy: null,
                        letterRendering: true,
                        imageTimeout: 0,
                        ignoreElements: function(element) {
                            // 忽略可能导致渲染问题的元素
                            return element.classList && (
                                element.classList.contains('celebration-close') ||
                                element.classList.contains('celebration-copy')
                            );
                        },
                        onclone: function(clonedDoc) {
                            // 在克隆的文档中强制应用正确的样式
                            const clonedModal = clonedDoc.querySelector('.celebration-content');
                            if (clonedModal) {
                                // 强制设置模态框样式
                                clonedModal.style.cssText = `
                                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
                                    border-radius: 28px !important;
                                    overflow: hidden !important;
                                    box-shadow: 0 24px 48px rgba(0, 0, 0, 0.15) !important;
                                    border: 1px solid rgba(0, 0, 0, 0.08) !important;
                                `;
                                
                                // 强制设置头部样式
                                const header = clonedModal.querySelector('.celebration-header');
                                if (header) {
                                    header.style.cssText = `
                                        background: linear-gradient(135deg, #ff0022 0%, #fa5a5a 100%) !important;
                                        color: white !important;
                                        padding: 24px 20px 28px !important;
                                        text-align: center !important;
                                        position: relative !important;
                                        border-radius: 28px 28px 0 0 !important;
                                        backdrop-filter: none !important;
                                        -webkit-backdrop-filter: none !important;
                                    `;
                                    
                                    // 确保头部文字颜色
                                    const headerElements = header.querySelectorAll('*');
                                    headerElements.forEach(el => {
                                        el.style.color = 'white !important';
                                    });
                                }
                                
                                // 强制设置身体部分样式
                                const body = clonedModal.querySelector('.celebration-body');
                                if (body) {
                                    body.style.cssText = `
                                        padding: 24px !important;
                                        background: transparent !important;
                                    `;
                                }
                                
                                // 强制设置本次收入样式
                                const incomeHighlight = clonedModal.querySelector('.celebration-three-columns');
                                if (incomeHighlight) {
                                    incomeHighlight.style.cssText = `
                                        text-align: center !important;
                                        padding: 20px !important;
                                        background: linear-gradient(135deg, #f94141 0%, #f54c4c 100%) !important;
                                        border-radius: 16px !important;
                                        color: white !important;
                                        margin: 16px 0 !important;
                                    `;
                                    
                                    // 确保收入文字颜色
                                    const incomeElements = incomeHighlight.querySelectorAll('*');
                                    incomeElements.forEach(el => {
                                        el.style.color = 'white !important';
                                    });
                                }
                                
                                // 移除所有可能干扰的效果
                                const allElements = clonedModal.querySelectorAll('*');
                                allElements.forEach(el => {
                                    el.style.backdropFilter = 'none !important';
                                    el.style.webkitBackdropFilter = 'none !important';
                                    el.style.filter = 'none !important';
                                    
                                    // 移除按钮（避免影响截图）
                                    if (el.classList.contains('celebration-close') || 
                                        el.classList.contains('celebration-copy')) {
                                        el.style.display = 'none !important';
                                    }
                                });
                            }
                        }
                    });
                    
                    // 恢复原始样式
                    elementsToFix.forEach((el, index) => {
                        if (originalStyles[index]) {
                            el.style.backdropFilter = originalStyles[index].backdropFilter || '';
                            el.style.webkitBackdropFilter = originalStyles[index].webkitBackdropFilter || '';
                            el.style.filter = originalStyles[index].filter || '';
                        }
                    });
                    
                    console.log('✅ 截图完成，尺寸:', canvas.width, 'x', canvas.height);
                    
                    // 检查截图是否为空白或颜色异常
                    const ctx = canvas.getContext('2d');
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    const data = imageData.data;
                    
                    // 检查是否有彩色内容（特别是红色头部）
                    let hasColorContent = false;
                    let redPixelCount = 0;
                    let totalPixels = data.length / 4;
                    
                    for (let i = 0; i < data.length; i += 4) {
                        const r = data[i];
                        const g = data[i + 1]; 
                        const b = data[i + 2];
                        const a = data[i + 3];
                        
                        // 检查是否有红色像素（头部背景应该是红色）
                        if (a > 100 && r > 200 && g < 100 && b < 100) {
                            redPixelCount++;
                            hasColorContent = true;
                        }
                        
                        // 检查是否有其他非白色、非灰色的内容
                        if (a > 50 && (
                            (r > 50 && g < r - 50) || // 红色倾向
                            (Math.abs(r - g) > 30 || Math.abs(r - b) > 30 || Math.abs(g - b) > 30) // 彩色内容
                        )) {
                            hasColorContent = true;
                        }
                    }
                    
                    const redPixelRatio = redPixelCount / totalPixels;
                    
                    // 如果没有彩色内容或红色像素比例太少，说明截图质量不佳
                    if (!hasColorContent || redPixelRatio < 0.01) {
                        console.warn('⚠️ 检测到截图颜色异常或内容为空白，切换到降级方案');
                        console.log(`红色像素比例: ${(redPixelRatio * 100).toFixed(2)}%, 有彩色内容: ${hasColorContent}`);
                        throw new Error('截图颜色异常，使用降级方案');
                    }
                    
                    console.log('✅ 截图内容和颜色验证通过', `红色像素比例: ${(redPixelRatio * 100).toFixed(2)}%`);
                    
                    // 重置失败计数
                    html2canvasFailCount = 0;
                    localStorage.setItem('html2canvasFailCount', '0');
                } catch (screenshotError) {
                    console.error('❌ 截图失败，使用降级方案:', screenshotError);
                    showToast('截图失败，使用简化模式生成图片...', 'info');
                    
                    // 增加失败计数
                    html2canvasFailCount++;
                    localStorage.setItem('html2canvasFailCount', html2canvasFailCount.toString());
                    console.log(`📊 html2canvas失败计数: ${html2canvasFailCount}`);
                    
                    // 使用降级方案，从弹窗中提取真实数据
                    const currentRecord = extractRecordDataFromModal(modal);
                    
                    canvas = await createTextBasedReport(currentRecord);
                    console.log('✅ 降级方案生成完成，尺寸:', canvas.width, 'x', canvas.height);
                }

                // 处理Canvas复制
                await handleCanvasCopy(canvas, copyBtn, originalText);

            } catch (error) {
                console.error('❌ 复制图片失败:', error);
                
                // 恢复按钮状态
                const copyBtn = document.querySelector('.celebration-copy');
                if (copyBtn) {
                    copyBtn.innerHTML = '❌';
                    setTimeout(() => {
                        copyBtn.innerHTML = '📋';
                        copyBtn.style.pointerEvents = 'auto';
                    }, 2000);
                }
                
                showToast(`复制失败: ${error.message}`, 'error');
            }
        }

        // 处理Canvas复制的辅助函数
        async function handleCanvasCopy(canvas, copyBtn, originalText) {
            try {
                // 将canvas转换为blob
                canvas.toBlob(async (blob) => {
                    if (!blob) {
                        throw new Error('图片生成失败');
                    }
                    
                    console.log('🖼️ 图片blob生成成功，大小:', blob.size, 'bytes');
                    
                    try {
                        // 检查浏览器是否支持剪贴板API
                        if (navigator.clipboard && navigator.clipboard.write && window.ClipboardItem) {
                            console.log('📋 尝试复制到剪贴板...');
                            await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                            ]);
                            
                            console.log('✅ 复制到剪贴板成功');
                            
                            // 显示成功状态
                            copyBtn.innerHTML = '✅';
                            setTimeout(() => {
                                copyBtn.innerHTML = originalText;
                                copyBtn.style.pointerEvents = 'auto';
                            }, 2000);
                            
                            showToast('战报图片已复制到剪贴板！可以直接粘贴发送', 'success');
                        } else {
                            console.log('💾 剪贴板API不支持，使用下载方案');
                            // 降级方案：创建下载链接
                            const url = canvas.toDataURL('image/png');
                            const link = document.createElement('a');
                            const fileName = `销售战报_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.png`;
                            link.download = fileName;
                            link.href = url;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            
                            copyBtn.innerHTML = '💾';
                            setTimeout(() => {
                                copyBtn.innerHTML = originalText;
                                copyBtn.style.pointerEvents = 'auto';
                            }, 2000);
                            
                            showToast(`图片已保存为 ${fileName}`, 'success');
                        }
                    } catch (clipboardError) {
                        console.warn('❌ 复制到剪贴板失败，尝试下载:', clipboardError);
                        
                        // 降级方案：创建下载链接
                        const url = canvas.toDataURL('image/png');
                        const link = document.createElement('a');
                        const fileName = `销售战报_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.png`;
                        link.download = fileName;
                        link.href = url;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        copyBtn.innerHTML = '💾';
                        setTimeout(() => {
                            copyBtn.innerHTML = originalText;
                            copyBtn.style.pointerEvents = 'auto';
                        }, 2000);
                        
                        showToast(`图片已保存为 ${fileName}`, 'success');
                    }
                }, 'image/png', 0.95);
            } catch (error) {
                console.error('❌ 处理Canvas复制失败:', error);
                
                // 恢复按钮状态
                if (copyBtn) {
                    copyBtn.innerHTML = '❌';
                    setTimeout(() => {
                        copyBtn.innerHTML = '📋';
                        copyBtn.style.pointerEvents = 'auto';
                    }, 2000);
                }
                
                showToast(`复制失败: ${error.message}`, 'error');
            }
        }

        // 等待库加载的辅助函数
        function waitForLibrary(condition, timeout = 15000) {
            return new Promise((resolve, reject) => {
                console.log('⏰ 开始等待库加载...');
                const startTime = Date.now();
                const checkInterval = setInterval(() => {
                    if (condition()) {
                        console.log('✅ 库加载完成，耗时:', Date.now() - startTime, 'ms');
                        clearInterval(checkInterval);
                        resolve();
                    } else if (Date.now() - startTime > timeout) {
                        console.error('❌ 库加载超时:', timeout, 'ms');
                        clearInterval(checkInterval);
                        reject(new Error('库加载超时'));
                    }
                }, 200);
            });
        }

        // 从弹窗中提取记录数据
        function extractRecordDataFromModal(modal) {
            try {
                console.log('🔍 开始从弹窗提取数据...');
                
                // 优先使用存储的真实记录数据
                if (currentCelebrationRecord) {
                    console.log('✅ 使用存储的真实记录数据:', currentCelebrationRecord);
                    return {
                        customer: currentCelebrationRecord.customer || '未知客户',
                        customerSource: currentCelebrationRecord.customerSource || '未知',
                        businessType: currentCelebrationRecord.businessType || '销售业务',
                        amount: currentCelebrationRecord.amount || 0,
                        incomeType: currentCelebrationRecord.incomeType || '回款',
                        salesperson: currentCelebrationRecord.salesperson || '销售人员',
                        contractAmount: currentCelebrationRecord.contractAmount || currentCelebrationRecord.amount || 0,
                        date: currentCelebrationRecord.date || new Date().toISOString().split('T')[0],
                        remarks: currentCelebrationRecord.remarks || '销售记录'
                    };
                }
                
                console.log('⚠️ 没有存储的记录数据，尝试从DOM中提取...');
                
                let extractedData = {
                    customer: '未知客户',
                    customerSource: '未知',
                    businessType: '销售业务',
                    amount: 0,
                    incomeType: '回款',
                    salesperson: '销售人员',
                    contractAmount: 0,
                    date: new Date().toISOString().split('T')[0],
                    remarks: '通过复制功能生成'
                };
                
                // 方法1：从三列信息中提取
                const miniInfoItems = modal.querySelectorAll('.mini-info-item');
                console.log('找到mini-info-item数量:', miniInfoItems.length);
                
                miniInfoItems.forEach((item, index) => {
                    const label = item.querySelector('.mini-info-label')?.textContent?.trim();
                    const value = item.querySelector('.mini-info-value')?.textContent?.trim();
                    
                    console.log(`第${index + 1}个信息项 - 标签:`, label, '值:', value);
                    
                    if (label && value) {
                        if (label.includes('客户')) {
                            extractedData.customer = value;
                        } else if (label.includes('销售员') || label.includes('销售人员')) {
                            extractedData.salesperson = value;
                        } else if (label.includes('业务类型')) {
                            extractedData.businessType = value;
                        }
                    }
                });
                
                // 方法2：从本次记录详情中提取
                const detailInfoItems = modal.querySelectorAll('.detail-info-item');
                console.log('找到detail-info-item数量:', detailInfoItems.length);
                
                detailInfoItems.forEach((item, index) => {
                    const label = item.querySelector('.detail-info-label')?.textContent?.trim();
                    const value = item.querySelector('.detail-info-value')?.textContent?.trim();
                    
                    console.log(`详情第${index + 1}个 - 标签:`, label, '值:', value);
                    
                    if (label && value) {
                        if (label.includes('来源')) {
                            extractedData.customerSource = value;
                        } else if (label.includes('类型')) {
                            extractedData.incomeType = value;
                        } else if (label.includes('合同')) {
                            const contractMatch = value.match(/[¥￥]?([0-9,]+)/);
                            if (contractMatch) {
                                extractedData.contractAmount = parseFloat(contractMatch[1].replace(/,/g, '')) || 0;
                            }
                        }
                    }
                });
                
                // 方法3：从高亮金额区域提取本次收入
                const amountElement = modal.querySelector('.celebration-three-columns .amount');
                if (amountElement) {
                    const amountText = amountElement.textContent || amountElement.innerText || '';
                    console.log('金额元素文本:', amountText);
                    const amountMatch = amountText.match(/[¥￥]?([0-9,]+)/);
                    if (amountMatch) {
                        extractedData.amount = parseFloat(amountMatch[1].replace(/,/g, '')) || 0;
                    }
                }
                
                // 方法4：如果上面没有找到金额，尝试其他选择器
                if (extractedData.amount === 0) {
                    const amountElements = modal.querySelectorAll('*');
                    for (let el of amountElements) {
                        if (el.textContent && el.textContent.includes('¥') && el.textContent.match(/¥[0-9,]+/)) {
                            const amountMatch = el.textContent.match(/¥([0-9,]+)/);
                            if (amountMatch && parseFloat(amountMatch[1].replace(/,/g, '')) > 0) {
                                extractedData.amount = parseFloat(amountMatch[1].replace(/,/g, ''));
                                console.log('从通用元素中提取到金额:', extractedData.amount);
                                break;
                            }
                        }
                    }
                }
                
                // 方法5：从时间元素提取日期
                const timeElement = modal.querySelector('.celebration-time');
                if (timeElement) {
                    const timeText = timeElement.textContent || timeElement.innerText || '';
                    console.log('时间元素文本:', timeText);
                    const dateMatch = timeText.match(/(\d{4}[-/]\d{2}[-/]\d{2})/);
                    if (dateMatch) {
                        extractedData.date = dateMatch[1].replace(/\//g, '-');
                    }
                }
                
                // 方法6：从备注区域提取备注
                const remarksElement = modal.querySelector('.celebration-remarks') || 
                                      modal.querySelector('[class*="remark"]') ||
                                      modal.querySelector('[class*="note"]');
                if (remarksElement) {
                    const remarksText = remarksElement.textContent?.trim();
                    if (remarksText && remarksText !== '通过复制功能生成') {
                        extractedData.remarks = remarksText;
                    }
                }
                
                // 如果合同金额没有值，使用收入金额
                if (extractedData.contractAmount === 0 && extractedData.amount > 0) {
                    extractedData.contractAmount = extractedData.amount;
                }
                
                console.log('✅ 最终提取的记录数据:', extractedData);
                return extractedData;
                
            } catch (error) {
                console.error('❌ 提取弹窗数据失败:', error);
                return {
                    customer: '数据提取失败',
                    customerSource: '未知',
                    businessType: '未知业务',
                    amount: 0,
                    incomeType: '回款',
                    salesperson: '未知销售',
                    contractAmount: 0,
                    date: new Date().toISOString().split('T')[0],
                    remarks: '数据提取过程中出现错误'
                };
            }
        }

        // 创建文本战报图片（降级方案）
        function createTextBasedReport(recordData) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 设置画布尺寸，匹配弹窗比例
                canvas.width = 720;
                canvas.height = 1280;
                
                // 1. 绘制白色背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 2. 绘制头部红色区域
                const headerHeight = 250;
                const headerGradient = ctx.createLinearGradient(0, 0, canvas.width, headerHeight);
                headerGradient.addColorStop(0, '#ff0022');
                headerGradient.addColorStop(1, '#fa5a5a');
                ctx.fillStyle = headerGradient;
                ctx.fillRect(0, 0, canvas.width, headerHeight);
                
                // 3. 头部内容
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
                ctx.fillText('🏆 战报', canvas.width/2, 80);
                
                ctx.font = 'bold 36px Arial, "Microsoft YaHei"';
                ctx.fillText('🎉 恭喜！销售记录添加成功', canvas.width/2, 140);
                
                ctx.font = '24px Arial, "Microsoft YaHei"';
                ctx.fillText('凝心聚力 勇争第一', canvas.width/2, 180);
                
                // 时间
                ctx.font = '20px Arial, "Microsoft YaHei"';
                ctx.fillText(`记录时间：${recordData.date} ${new Date().toLocaleTimeString()}`, canvas.width/2, 220);
                
                // 4. 本次收入高亮区域
                const incomeY = headerHeight + 40;
                const incomeHeight = 120;
                const incomeGradient = ctx.createLinearGradient(0, incomeY, canvas.width, incomeY + incomeHeight);
                incomeGradient.addColorStop(0, '#f94141');
                incomeGradient.addColorStop(1, '#f54c4c');
                ctx.fillStyle = incomeGradient;
                ctx.fillRect(50, incomeY, canvas.width - 100, incomeHeight);
                
                // 收入金额
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.font = '24px Arial, "Microsoft YaHei"';
                ctx.fillText('本次收入', canvas.width/2, incomeY + 30);
                ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
                ctx.fillText(`¥${(Number(recordData.amount) || 0).toLocaleString()}`, canvas.width/2, incomeY + 80);
                
                // 5. 详细信息区域
                let detailY = incomeY + incomeHeight + 60;
                const leftX = 80;
                const centerX = canvas.width / 2;
                const rightX = canvas.width - 80;
                
                // 设置详细信息样式
                ctx.fillStyle = '#333333';
                ctx.textAlign = 'center';
                ctx.font = 'bold 28px Arial, "Microsoft YaHei"';
                
                // 三列信息
                const col1X = leftX + 60;
                const col2X = centerX;
                const col3X = rightX - 60;
                
                // 标题行
                ctx.fillStyle = '#666666';
                ctx.font = '20px Arial, "Microsoft YaHei"';
                ctx.fillText('客户', col1X, detailY);
                ctx.fillText('销售员', col2X, detailY);
                ctx.fillText('业务类型', col3X, detailY);
                
                // 内容行
                detailY += 40;
                ctx.fillStyle = '#333333';
                ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
                ctx.fillText(recordData.customer, col1X, detailY);
                ctx.fillText(recordData.salesperson, col2X, detailY);
                ctx.fillText(recordData.businessType, col3X, detailY);
                
                // 6. 本次记录详情
                detailY += 80;
                ctx.fillStyle = '#007AFF';
                ctx.textAlign = 'center';
                ctx.font = '22px Arial, "Microsoft YaHei"';
                ctx.fillText('📋 本次记录详情', canvas.width/2, detailY);
                
                // 详情信息
                detailY += 50;
                ctx.fillStyle = '#333333';
                ctx.textAlign = 'left';
                ctx.font = '24px Arial, "Microsoft YaHei"';
                
                const detailItems = [
                    { label: '来源', value: recordData.customerSource || '未知' },
                    { label: '类型', value: recordData.incomeType },
                    { label: '合同', value: `¥${recordData.contractAmount || recordData.amount}` }
                ];
                
                detailItems.forEach((item, index) => {
                    const itemX = 80 + (index * 180);
                    ctx.fillStyle = '#666666';
                    ctx.font = '18px Arial, "Microsoft YaHei"';
                    ctx.fillText(item.label, itemX, detailY);
                    
                    ctx.fillStyle = '#333333';
                    ctx.font = 'bold 22px Arial, "Microsoft YaHei"';
                    ctx.fillText(item.value, itemX, detailY + 30);
                });
                
                // 7. 底部备注
                if (recordData.remarks && recordData.remarks !== '通过复制功能生成') {
                    detailY += 100;
                    ctx.fillStyle = '#999999';
                    ctx.textAlign = 'center';
                    ctx.font = '18px Arial, "Microsoft YaHei"';
                    ctx.fillText(`备注：${recordData.remarks}`, canvas.width/2, detailY);
                }
                
                // 8. 底部祝贺
                ctx.fillStyle = '#007AFF';
                ctx.textAlign = 'center';
                ctx.font = 'bold 32px Arial, "Microsoft YaHei"';
                ctx.fillText('🎉 恭喜成交！', canvas.width/2, canvas.height - 80);
                
                resolve(canvas);
            });
        }

        // 禁用点击弹窗外部关闭功能（保护截图操作）
        window.onclick = function(event) {
            const modal = document.getElementById('celebrationModal');
            if (event.target == modal) {
                // 不关闭弹窗，保持显示状态便于截图
                console.log('💡 点击外部区域不会关闭弹窗，请点击关闭按钮');
            }
        }

        // 导出到Excel (.xlsx格式) - 性能优化版本
        async function exportToExcel() {
            // 显示加载提示
            const exportBtn = event.target;
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<span>⏳</span><span>加载导出库...</span>';
            exportBtn.disabled = true;
            
            try {
                // 动态加载XLSX库
                await loadXLSX();
                
                exportBtn.innerHTML = '<span>⏳</span><span>导出中...</span>';
                
                // 检查XLSX库是否加载成功
                if (typeof XLSX === 'undefined') {
                    throw new Error('XLSX库加载失败');
                }
                
                // 创建工作簿
                const wb = XLSX.utils.book_new();
                
                // 1. 准备销售记录数据
                const excelData = salesData.map(record => ({
                    '收款日期': record.date,
                    '客户名': record.customer,
                    '客户来源': record.customerSource || '未设置',
                    '业务类型': record.businessType,
                    '收入金额': record.amount,
                    '收入类型': record.incomeType,
                    '销售人员': record.salesperson,
                    '合同金额': record.contractAmount,
                    '备注': record.remarks || ''
                }));
                
                // 创建销售记录工作表
                const ws = XLSX.utils.json_to_sheet(excelData);
                
                // 设置列宽
                const colWidths = [
                    { wch: 12 }, // 收款日期
                    { wch: 15 }, // 客户名
                    { wch: 12 }, // 客户来源
                    { wch: 12 }, // 业务类型
                    { wch: 12 }, // 收入金额
                    { wch: 12 }, // 收入类型
                    { wch: 10 }, // 销售人员
                    { wch: 12 }, // 合同金额
                    { wch: 20 }  // 备注
                ];
                ws['!cols'] = colWidths;
                
                // 设置样式（表头）
                if (ws['!ref']) {
                    const range = XLSX.utils.decode_range(ws['!ref']);
                    for (let C = range.s.c; C <= range.e.c; ++C) {
                        const address = XLSX.utils.encode_cell({ r: 0, c: C });
                        if (!ws[address]) continue;
                        ws[address].s = {
                            fill: { fgColor: { rgb: "4CAF50" } },
                            font: { color: { rgb: "FFFFFF" }, bold: true },
                            alignment: { horizontal: "center" }
                        };
                    }
                }
                
                // 添加销售记录工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, "销售记录");
                
                // 2. 准备工资计算数据
                const currentMonth = new Date().toISOString().substring(0, 7);
                const monthRecords = salesData.filter(record => record.date.startsWith(currentMonth));
                
                // 计算工资数据
                const salaryData = {};
                
                // 初始化所有销售人员的数据
                systemSettings.salespeople.forEach(person => {
                    salaryData[person.name] = {
                        totalSales: 0,
                        baseSalary: person.baseSalary,
                        commissionType: person.commissionType,
                        fixedRate: person.fixedRate || 0,
                        ladderRates: person.ladderRates || [],
                        commission: 0,
                        totalSalary: 0
                    };
                });
                
                // 统计销售额
                monthRecords.forEach(record => {
                    if (salaryData[record.salesperson]) {
                        salaryData[record.salesperson].totalSales += record.amount;
                    }
                });
                
                // 计算提成和总工资
                const salaryExcelData = [];
                Object.keys(salaryData).forEach(person => {
                    const data = salaryData[person];
                    
                    if (data.commissionType === 'ladder' && data.ladderRates.length > 0) {
                        // 阶梯式提成计算
                        data.commission = calculateLadderCommission(data.totalSales, data.ladderRates);
                        } else {
                        // 固定比例提成
                        data.commission = data.totalSales * data.fixedRate;
                    }
                    
                    data.totalSalary = data.baseSalary + data.commission;
                    
                    // 准备Excel数据
                    let commissionTypeText = '';
                    let commissionDetailText = '';
                    
                    if (data.commissionType === 'ladder') {
                        commissionTypeText = '阶梯式';
                        if (data.totalSales > 0 && data.ladderRates.length > 0) {
                            commissionDetailText = calculateLadderCommissionDetails(data.totalSales, data.ladderRates);
                        } else {
                            commissionDetailText = '无销售业绩';
                        }
                    } else {
                        commissionTypeText = `固定${(data.fixedRate*100).toFixed(1)}%`;
                        commissionDetailText = `${data.totalSales.toFixed(2)} × ${(data.fixedRate*100).toFixed(1)}% = ${data.commission.toFixed(2)}元`;
                    }
                    
                    salaryExcelData.push({
                        '销售人员': person,
                        '销售业绩(元)': data.totalSales.toFixed(2),
                        '基础工资(元)': data.baseSalary.toFixed(2),
                        '提成类型': commissionTypeText,
                        '提成金额(元)': data.commission.toFixed(2),
                        '总工资(元)': data.totalSalary.toFixed(2),
                        '提成计算详情': commissionDetailText
                    });
                });
                
                // 创建工资计算工作表
                const salaryWs = XLSX.utils.json_to_sheet(salaryExcelData);
                
                // 设置工资计算工作表列宽
                const salaryColWidths = [
                    { wch: 12 }, // 销售人员
                    { wch: 15 }, // 销售业绩
                    { wch: 15 }, // 基础工资
                    { wch: 12 }, // 提成类型
                    { wch: 15 }, // 提成金额
                    { wch: 15 }, // 总工资
                    { wch: 40 }  // 提成计算详情
                ];
                salaryWs['!cols'] = salaryColWidths;
                
                // 设置工资计算工作表样式
                if (salaryWs['!ref']) {
                    const salaryRange = XLSX.utils.decode_range(salaryWs['!ref']);
                    for (let C = salaryRange.s.c; C <= salaryRange.e.c; ++C) {
                        const address = XLSX.utils.encode_cell({ r: 0, c: C });
                        if (!salaryWs[address]) continue;
                        salaryWs[address].s = {
                            fill: { fgColor: { rgb: "FF9800" } },
                            font: { color: { rgb: "FFFFFF" }, bold: true },
                            alignment: { horizontal: "center" }
                        };
                    }
                }
                
                // 添加工资计算工作表到工作簿
                XLSX.utils.book_append_sheet(wb, salaryWs, "工资计算");
                
                // 3. 创建统计汇总工作表
                const monthTotal = monthRecords.reduce((sum, record) => sum + record.amount, 0);
                const todayTotal = salesData.filter(record => record.date === new Date().toISOString().split('T')[0])
                    .reduce((sum, record) => sum + record.amount, 0);
                const totalSalary = salaryExcelData.reduce((sum, row) => sum + parseFloat(row['总工资(元)']), 0);
                const totalCommission = salaryExcelData.reduce((sum, row) => sum + parseFloat(row['提成金额(元)']), 0);
                
                const statsData = [
                    { '统计项目': '本月总入账', '金额(元)': monthTotal.toFixed(2), '说明': `${currentMonth}月份` },
                    { '统计项目': '今日总入账', '金额(元)': todayTotal.toFixed(2), '说明': new Date().toISOString().split('T')[0] },
                    { '统计项目': '记录总数', '金额(元)': salesData.length, '说明': '全部记录' },
                    { '统计项目': '本月记录数', '金额(元)': monthRecords.length, '说明': `${currentMonth}月份` },
                    { '统计项目': '本月总工资', '金额(元)': totalSalary.toFixed(2), '说明': '基础工资+提成' },
                    { '统计项目': '本月总提成', '金额(元)': totalCommission.toFixed(2), '说明': '所有销售人员提成' }
                ];
                
                const statsWs = XLSX.utils.json_to_sheet(statsData);
                statsWs['!cols'] = [{ wch: 15 }, { wch: 15 }, { wch: 15 }];
                
                // 设置统计汇总工作表样式
                if (statsWs['!ref']) {
                    const statsRange = XLSX.utils.decode_range(statsWs['!ref']);
                    for (let C = statsRange.s.c; C <= statsRange.e.c; ++C) {
                        const address = XLSX.utils.encode_cell({ r: 0, c: C });
                        if (!statsWs[address]) continue;
                        statsWs[address].s = {
                            fill: { fgColor: { rgb: "2196F3" } },
                            font: { color: { rgb: "FFFFFF" }, bold: true },
                            alignment: { horizontal: "center" }
                        };
                    }
                }
                
                XLSX.utils.book_append_sheet(wb, statsWs, "统计汇总");
                
                // 延迟处理，避免阻塞UI
                setTimeout(() => {
                    try {
                        // 下载文件
                        const fileName = `销售记录_${new Date().toISOString().split('T')[0]}.xlsx`;
                        XLSX.writeFile(wb, fileName);
                        
                        // 恢复按钮状态
                        exportBtn.innerHTML = originalText;
                        exportBtn.disabled = false;
                        
                        alert('✅ Excel文件导出成功！包含销售记录、工资计算和统计汇总三个工作表');
                    } catch (writeError) {
                        console.error('文件写入失败:', writeError);
                        exportBtn.innerHTML = originalText;
                        exportBtn.disabled = false;
                        alert('❌ 文件保存失败，请检查浏览器下载权限！');
                    }
                }, 100);
                
            } catch (error) {
                console.error('Excel导出失败:', error);
                // 恢复按钮状态
                if (typeof exportBtn !== 'undefined') {
                    exportBtn.innerHTML = originalText;
                    exportBtn.disabled = false;
                }
                
                // 尝试备用方案
                if (confirm('高级Excel导出失败，是否使用简化版导出？\n简化版包含基本数据但没有多工作表功能。')) {
                    exportToExcelSimple();
                } else {
                    alert('❌ Excel导出失败：' + error.message + '。请尝试刷新页面重试！');
                }
            }
        }



        // 导出PDF报表 (支持中文) - 性能优化版本
        async function exportToPDF() {
            // 显示加载提示
            const loadingAlert = document.createElement('div');
            loadingAlert.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                text-align: center;
            `;
            loadingAlert.innerHTML = '<p>📊 正在加载PDF库...</p>';
            document.body.appendChild(loadingAlert);

            try {
                // 动态加载必需的库
                await Promise.all([loadHTML2Canvas(), loadJSPDF()]);
                
                loadingAlert.innerHTML = '<p>📊 正在生成PDF报表，请稍候...</p>';
            const currentMonth = new Date().toISOString().substring(0, 7);
            const monthRecords = salesData.filter(record => record.date.startsWith(currentMonth));
            const monthTotal = monthRecords.reduce((sum, record) => sum + record.amount, 0);
                const todayTotal = salesData.filter(record => record.date === new Date().toISOString().split('T')[0])
                    .reduce((sum, record) => sum + record.amount, 0);

                // 创建临时的报表HTML
                const reportHTML = createPDFReportHTML(currentMonth, monthRecords, monthTotal, todayTotal);
                
                // 创建临时元素
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = reportHTML;
                tempDiv.style.cssText = `
                    position: absolute;
                    left: -10000px;
                    top: 0;
                    width: 800px;
                    background: white;
                    font-family: 'Microsoft YaHei', sans-serif;
                `;
                document.body.appendChild(tempDiv);

                // 使用html2canvas + jsPDF
                setTimeout(() => {
                    html2canvas(tempDiv, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff'
                    }).then(canvas => {
                        const imgData = canvas.toDataURL('image/png');
                        const { jsPDF } = window.jspdf;
                        
                        // 计算PDF尺寸
                        const imgWidth = 210;
                        const pageHeight = 295;
                        const imgHeight = (canvas.height * imgWidth) / canvas.width;
                        let heightLeft = imgHeight;
                        
                        const doc = new jsPDF('p', 'mm');
                        let position = 0;
                        
                        // 添加第一页
                        doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pageHeight;
                        
                        // 如果内容超过一页，添加更多页面
                        while (heightLeft >= 0) {
                            position = heightLeft - imgHeight;
                            doc.addPage();
                            doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                            heightLeft -= pageHeight;
                        }
                        
                        // 保存PDF
                        const fileName = `销售报表_${currentMonth}.pdf`;
                        doc.save(fileName);
                        
                        // 清理
                        document.body.removeChild(tempDiv);
                        document.body.removeChild(loadingAlert);
                        alert('✅ PDF报表导出成功！');
                        
                }).catch(error => {
                        console.error('PDF生成失败:', error);
                        document.body.removeChild(tempDiv);
                        document.body.removeChild(loadingAlert);
                        alert('❌ PDF生成失败，请重试！');
                    });
                }, 500);

            } catch (error) {
                console.error('PDF导出失败:', error);
                document.body.removeChild(loadingAlert);
                alert('❌ PDF导出失败，请检查浏览器是否支持！');
            }
        }

        // 创建PDF报表HTML内容
        function createPDFReportHTML(currentMonth, monthRecords, monthTotal, todayTotal) {
            // 计算销售人员业绩
            const salespersonStats = {};
            monthRecords.forEach(record => {
                if (!salespersonStats[record.salesperson]) {
                    salespersonStats[record.salesperson] = 0;
                }
                salespersonStats[record.salesperson] += record.amount;
            });

            // 计算工资数据
            const salaryData = {};
            systemSettings.salespeople.forEach(person => {
                salaryData[person.name] = {
                    totalSales: 0,
                    baseSalary: Number(person.base_salary) || 0,
                    commissionType: person.commission_type || 'fixed',
                    fixedRate: Number(person.fixed_rate) || 0,
                    ladderRates: person.ladder_rates || [],
                    commission: 0,
                    totalSalary: 0
                };
            });

            // 统计销售额
            monthRecords.forEach(record => {
                if (salaryData[record.salesperson]) {
                    salaryData[record.salesperson].totalSales += record.amount;
                }
            });

            // 计算提成和总工资
            Object.keys(salaryData).forEach(person => {
                const data = salaryData[person];
                if (data.commissionType === 'ladder' && data.ladderRates.length > 0) {
                    data.commission = calculateLadderCommission(data.totalSales, data.ladderRates);
                } else {
                    data.commission = data.totalSales * data.fixedRate;
                }
                data.totalSalary = data.baseSalary + data.commission;
            });

            const totalSalarySum = Object.values(salaryData).reduce((sum, data) => sum + data.totalSalary, 0);
            const totalCommissionSum = Object.values(salaryData).reduce((sum, data) => sum + data.commission, 0);

            return `
                <div style="padding: 30px; font-family: 'Microsoft YaHei', sans-serif;">
                    <!-- 报表标题 -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #4CAF50; font-size: 28px; margin: 0;">📊 销售报表</h1>
                        <h2 style="color: #666; font-size: 18px; margin: 10px 0;">${currentMonth} 月份统计</h2>
                        <p style="color: #999; font-size: 12px;">生成时间: ${new Date().toLocaleString()}</p>
                    </div>

                    <!-- 统计概览 -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
                        <h3 style="color: #333; margin-top: 0;">📈 统计概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px;">
                            <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                                <div style="font-size: 12px; color: #666;">本月总入账</div>
                                <div style="font-size: 18px; color: #4CAF50; font-weight: bold;">¥${monthTotal.toFixed(2)}</div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #2196F3;">
                                <div style="font-size: 12px; color: #666;">今日总入账</div>
                                <div style="font-size: 18px; color: #2196F3; font-weight: bold;">¥${todayTotal.toFixed(2)}</div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #FF9800;">
                                <div style="font-size: 12px; color: #666;">本月记录数</div>
                                <div style="font-size: 18px; color: #FF9800; font-weight: bold;">${monthRecords.length} 笔</div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #9C27B0;">
                                <div style="font-size: 12px; color: #666;">总记录数</div>
                                <div style="font-size: 18px; color: #9C27B0; font-weight: bold;">${salesData.length} 笔</div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #E91E63;">
                                <div style="font-size: 12px; color: #666;">本月总工资</div>
                                <div style="font-size: 18px; color: #E91E63; font-weight: bold;">¥${totalSalarySum.toFixed(2)}</div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 8px; border-left: 4px solid #795548;">
                                <div style="font-size: 12px; color: #666;">本月总提成</div>
                                <div style="font-size: 18px; color: #795548; font-weight: bold;">¥${totalCommissionSum.toFixed(2)}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细记录表格 -->
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #333;">📋 详细记录</h3>
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden;">
                            <thead>
                                <tr style="background: #4CAF50; color: white;">
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">收款日期</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">客户名</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">客户来源</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">业务类型</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">收入金额</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">收入类型</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">销售人员</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${monthRecords.map((record, index) => `
                                    <tr style="background: ${index % 2 === 0 ? '#f9f9f9' : 'white'};">
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.date}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.customer}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.customerSource || '未设置'}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.businessType}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px; color: #4CAF50; font-weight: bold;">¥${record.amount.toFixed(2)}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.incomeType}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.salesperson}</td>
                                        <td style="padding: 8px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${record.remarks || '-'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <!-- 工资计算统计 -->
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #333;">💰 工资计算统计</h3>
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden;">
                            <thead>
                                <tr style="background: #FF9800; color: white;">
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">销售人员</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">销售业绩</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">基础工资</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">提成类型</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">提成金额</th>
                                    <th style="padding: 10px 6px; text-align: left; font-size: 11px;">总工资</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.keys(salaryData).map((person, index) => {
                                    const data = salaryData[person];
                                    const commissionTypeText = data.commissionType === 'ladder' ? '阶梯式' : `固定${(data.fixedRate*100).toFixed(1)}%`;
                                    return `
                                        <tr style="background: ${index % 2 === 0 ? '#f9f9f9' : 'white'};">
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${person}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #eee; font-size: 10px; color: #4CAF50; font-weight: bold;">¥${data.totalSales.toFixed(2)}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #eee; font-size: 10px;">¥${data.baseSalary.toFixed(2)}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #eee; font-size: 10px;">${commissionTypeText}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #eee; font-size: 10px; color: #FF9800; font-weight: bold;">¥${data.commission.toFixed(2)}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #eee; font-size: 10px; color: #2196F3; font-weight: bold;">¥${data.totalSalary.toFixed(2)}</td>
                                        </tr>
                                    `;
                                }).join('')}
                                <tr style="background: #e8f5e8; font-weight: bold;">
                                    <td style="padding: 12px 6px; border-top: 2px solid #4CAF50; font-size: 11px;">合计</td>
                                    <td style="padding: 12px 6px; border-top: 2px solid #4CAF50; font-size: 11px; color: #4CAF50;">¥${monthTotal.toFixed(2)}</td>
                                    <td style="padding: 12px 6px; border-top: 2px solid #4CAF50; font-size: 11px;">-</td>
                                    <td style="padding: 12px 6px; border-top: 2px solid #4CAF50; font-size: 11px;">-</td>
                                    <td style="padding: 12px 6px; border-top: 2px solid #4CAF50; font-size: 11px; color: #FF9800;">¥${totalCommissionSum.toFixed(2)}</td>
                                    <td style="padding: 12px 6px; border-top: 2px solid #4CAF50; font-size: 11px; color: #2196F3;">¥${totalSalarySum.toFixed(2)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 销售人员业绩统计 -->
                    <div>
                        <h3 style="color: #333;">👥 销售人员业绩统计</h3>
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden;">
                            <thead>
                                <tr style="background: #4CAF50; color: white;">
                                    <th style="padding: 12px; text-align: left;">销售人员</th>
                                    <th style="padding: 12px; text-align: left;">销售金额</th>
                                    <th style="padding: 12px; text-align: left;">占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.keys(salespersonStats).map((person, index) => `
                                    <tr style="background: ${index % 2 === 0 ? '#f9f9f9' : 'white'};">
                                        <td style="padding: 12px; border-bottom: 1px solid #eee;">${person}</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #eee; color: #4CAF50; font-weight: bold;">¥${salespersonStats[person].toFixed(2)}</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #eee;">${monthTotal > 0 ? ((salespersonStats[person] / monthTotal) * 100).toFixed(1) : '0'}%</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <!-- 页脚 -->
                    <div style="margin-top: 30px; text-align: center; color: #999; font-size: 12px;">
                        <p>销售记录管理系统 自动生成 | 生成时间: ${new Date().toLocaleString()}</p>
                    </div>
                </div>
            `;
        }

                // 导出CSV格式 (简单数据格式)
        function exportToCSV() {
            try {
                // 添加BOM以支持中文
                let csvContent = "\uFEFF";
                csvContent += "收款日期,客户名,客户来源,业务类型,收入金额,收入类型,销售人员,合同金额,备注\n";
                
                salesData.forEach(record => {
                    csvContent += `${record.date},"${record.customer}","${record.customerSource || '未设置'}","${record.businessType}",${record.amount},"${record.incomeType}","${record.salesperson}",${record.contractAmount},"${record.remarks || ''}"\n`;
                });
                
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `销售记录_${new Date().toISOString().split('T')[0]}.csv`;
                link.click();
                
                alert('✅ CSV文件导出成功！');
            } catch (error) {
                console.error('CSV导出失败:', error);
                alert('❌ CSV导出失败！');
            }
        }

        // 简化版Excel导出（备用方案）
        function exportToExcelSimple() {
            try {
                // 创建HTML表格数据
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <style>
                            table { border-collapse: collapse; width: 100%; }
                            th, td { border: 1px solid #000; padding: 8px; text-align: left; }
                            th { background-color: #4CAF50; color: white; }
                        </style>
                    </head>
                    <body>
                        <h2>销售记录</h2>
                        <table>
                            <tr>
                                <th>收款日期</th>
                                <th>客户名</th>
                                <th>客户来源</th>
                                <th>业务类型</th>
                                <th>收入金额</th>
                                <th>收入类型</th>
                                <th>销售人员</th>
                                <th>合同金额</th>
                                <th>备注</th>
                            </tr>
                `;
                
                salesData.forEach(record => {
                    htmlContent += `
                        <tr>
                            <td>${record.date}</td>
                            <td>${record.customer}</td>
                            <td>${record.customerSource || '未设置'}</td>
                            <td>${record.businessType}</td>
                            <td>${record.amount}</td>
                            <td>${record.incomeType}</td>
                            <td>${record.salesperson}</td>
                            <td>${record.contractAmount}</td>
                            <td>${record.remarks || ''}</td>
                        </tr>
                    `;
                });
                
                                  htmlContent += `
                          </table>
                `;
                
                const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `销售记录_${new Date().toISOString().split('T')[0]}.xls`;
            link.click();
                
                alert('✅ Excel文件导出成功！（简化版）');
            } catch (error) {
                console.error('简化Excel导出失败:', error);
                alert('❌ 导出失败！');
            }
        }

        // 备份数据
        function backupData() {
            const backupData = {
                timestamp: new Date().toISOString(),
                data: salesData
            };
            
            const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `销售数据备份_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 导入数据
        function importData() {
            const file = document.getElementById('importFile').files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);
                    if (importedData.data && Array.isArray(importedData.data)) {
                        salesData = importedData.data;
                        localStorage.setItem('salesData', JSON.stringify(salesData));
                        loadTableData();
                        updateStatistics();
                        updateSalaryDetails();
                        
                        // 如果当前在统计分析标签，则更新统计
                        if (document.getElementById('statistics').classList.contains('active')) {
                            setTimeout(() => updateAllStatistics(), 50);
                        }
                        
                        // 关闭可能存在的喜报弹窗（因为数据已全量替换）
                        closeCelebrationModal();
                        
                        alert('✅ 数据导入成功！');
                    } else {
                        alert('❌ 导入文件格式错误！');
                    }
                } catch (error) {
                    alert('❌ 导入失败，请检查文件格式！');
                }
            };
            reader.readAsText(file);
        }

        // 清空所有数据
        function clearAllData() {
            if (confirm('⚠️ 确定要清空所有数据吗？此操作不可恢复！')) {
                salesData = [];
                localStorage.removeItem('salesData');
                loadTableData();
                updateStatistics();
                updateSalaryDetails();
                
                // 如果当前在统计分析标签，则更新统计
                if (document.getElementById('statistics').classList.contains('active')) {
                    setTimeout(() => updateAllStatistics(), 50);
                }
                
                // 关闭可能存在的喜报弹窗（因为数据已全部清空）
                closeCelebrationModal();
                
                alert('✅ 所有数据已清空！');
            }
        }

        // ===== 系统设置相关函数 =====
        
        // 保存系统设置
        function saveSystemSettings() {
            localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
        }
        
        // 🔧 修复：更新表单选项 - 正确处理字符串数组和对象数组
        function updateFormOptions() {
            console.log('🔄 更新表单选项...', systemSettings);

            // 🔧 修复：更新业务类型选项
            const businessTypeSelect = document.getElementById('businessType');
            if (businessTypeSelect) {
                businessTypeSelect.innerHTML = '<option value="">请选择业务类型</option>';
                if (systemSettings.businessTypes && systemSettings.businessTypes.length > 0) {
                    systemSettings.businessTypes.forEach(type => {
                        // 🔧 修复：正确处理字符串和对象两种格式
                        const typeName = typeof type === 'string' ? type : type.name;
                        businessTypeSelect.innerHTML += `<option value="${typeName}">${typeName}</option>`;
                    });
                }
                console.log('✅ 业务类型选项已更新:', systemSettings.businessTypes.length, '个');
            }

            // 🔧 修复：更新收入类型选项
            const incomeTypeSelect = document.getElementById('incomeType');
            if (incomeTypeSelect) {
                incomeTypeSelect.innerHTML = '<option value="">请选择收入类型</option>';
                if (systemSettings.incomeTypes && systemSettings.incomeTypes.length > 0) {
                    systemSettings.incomeTypes.forEach(type => {
                        // 🔧 修复：正确处理字符串和对象两种格式
                        const typeName = typeof type === 'string' ? type : type.name;
                        incomeTypeSelect.innerHTML += `<option value="${typeName}">${typeName}</option>`;
                    });
                }
                console.log('✅ 收入类型选项已更新:', systemSettings.incomeTypes.length, '个');
            }

            // 🔧 修复：更新客户来源选项
            const customerSourceSelect = document.getElementById('customerSource');
            if (customerSourceSelect) {
                customerSourceSelect.innerHTML = '<option value="">请选择客户来源</option>';
                if (systemSettings.customerSources && systemSettings.customerSources.length > 0) {
                    systemSettings.customerSources.forEach(source => {
                        // 🔧 修复：正确处理字符串和对象两种格式
                        const sourceName = typeof source === 'string' ? source : source.name;
                        customerSourceSelect.innerHTML += `<option value="${sourceName}">${sourceName}</option>`;
                    });
                }
                console.log('✅ 客户来源选项已更新:', systemSettings.customerSources.length, '个');
            }

            // 🔧 修复：更新销售人员选项
            const salespersonSelect = document.getElementById('salesperson');
            if (salespersonSelect) {
                salespersonSelect.innerHTML = '<option value="">请选择销售人员</option>';
                if (systemSettings.salespeople && systemSettings.salespeople.length > 0) {
                    systemSettings.salespeople.forEach(person => {
                        // 销售人员通常是对象格式，但也要兼容字符串格式
                        const personName = typeof person === 'string' ? person : person.name;
                        salespersonSelect.innerHTML += `<option value="${personName}">${personName}</option>`;
                    });
                }
                salespersonSelect.innerHTML += '<option value="其他">其他</option>';
                console.log('✅ 销售人员选项已更新:', systemSettings.salespeople.length, '个');
            }

            console.log('✅ 表单选项更新完成');
        }
        
        // 加载设置数据到界面
        function loadSettingsData() {
            console.log('开始加载设置数据...', systemSettings);
            
            // 确保DOM元素已经加载
            if (document.readyState === 'loading') {
                console.log('DOM还在加载中，等待加载完成...');
                document.addEventListener('DOMContentLoaded', loadSettingsData);
                return;
            }
            
            // 强制刷新数据
            try {
                loadBusinessTypes();
                loadIncomeTypes();
                loadCustomerSources();
                loadSalespersonList();
                console.log('设置数据加载完成');
            } catch (error) {
                console.error('加载设置数据时出错:', error);
            }
        }
        
        // 重复的加载函数已删除，使用统一的 SettingsManager
        
        // 加载销售人员列表
        function loadSalespersonList() {
            const list = document.getElementById('salespersonList');
            if (!list) {
                console.error('找不到 salespersonList 元素');
                return;
            }
            
            console.log('加载销售人员:', systemSettings.salespeople);
            list.innerHTML = '';
            
            if (systemSettings.salespeople.length === 0) {
                list.innerHTML = '<div class="empty-state">暂无销售人员，请添加</div>';
                return;
            }
            
            systemSettings.salespeople.forEach((person, index) => {
                const item = document.createElement('div');
                item.className = 'settings-item';

                // 🔧 修复：兼容新旧字段名格式
                const commissionType = person.commissionType || person.commission_type || 'fixed';
                const fixedRate = Number(person.fixedRate || person.fixed_rate) || 0;
                const baseSalary = Number(person.baseSalary || person.base_salary) || 0;
                const commissionText = commissionType === 'ladder' ? '阶梯式' : `${(fixedRate*100).toFixed(1)}%`;

                // 🔍 调试信息：检查销售人员数据
                console.log(`👤 显示销售人员 ${person.name}:`, {
                    baseSalary: baseSalary,
                    commissionType: commissionType,
                    fixedRate: fixedRate,
                    原始数据: person
                });

                item.innerHTML = `
                    <div class="settings-item-content">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span class="item-index">${index + 1}</span>
                            <div>
                                <div style="font-weight: bold; font-size: 16px; color: #333;">👤 ${person.name}</div>
                                <div style="font-size: 13px; color: #666; margin-top: 4px;">
                                    💰 基础工资: ¥${baseSalary.toLocaleString()} |
                                    📊 提成方式: ${commissionText}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="settings-item-actions">
                        <button class="btn btn-secondary btn-small" onclick="editSalesperson(${index})" title="编辑基本信息">
                            <span>✏️</span>
                        </button>
                        ${commissionType === 'ladder' ?
                            `<button class="btn btn-warning btn-small" onclick="editLadder(${index})" title="设置阶梯提成">
                                <span>🎯</span>
                            </button>` : ''}
                        <button class="btn btn-tertiary btn-small" onclick="viewSalespersonStats(${index})" title="查看业绩">
                            <span>📊</span>
                        </button>
                        <button class="btn btn-danger btn-small" onclick="removeSalesperson(${index})" title="删除">
                            <span>🗑️</span>
                        </button>
                    </div>
                `;
                list.appendChild(item);
            });
            console.log('销售人员列表已加载，共', systemSettings.salespeople.length, '人');
        }
        
        // =====================================================
        // 通用系统设置管理器 - 消除重复代码
        // =====================================================
        class SettingsManager {
            constructor(settingType, settingKey, displayName, inputId, listId) {
                this.type = settingType;
                this.key = settingKey;
                this.displayName = displayName;
                this.inputId = inputId;
                this.listId = listId;
            }

            // 通用添加逻辑
            add() {
                const input = document.getElementById(this.inputId);
                const value = input.value.trim();

                if (!value) {
                    alert(`⚠️ 请输入${this.displayName}`);
                    input.focus();
                    return;
                }

                if (value.length > 20) {
                    alert(`⚠️ ${this.displayName}名称不能超过20个字符`);
                    input.focus();
                    return;
                }

                // 检查是否重复（兼容新旧数据结构）
                const existingNames = systemSettings[this.key].map(item =>
                    typeof item === 'object' ? item.name : item
                );
                if (existingNames.includes(value)) {
                    alert(`⚠️ 该${this.displayName}已存在`);
                    input.focus();
                    return;
                }

                systemSettings[this.key].push(value);
                saveSystemSettings();

                // 同步到数据库 - 使用新架构直接添加到对应表
                this.syncToDatabase('add', value);

                updateFormOptions();
                this.load();
                input.value = '';
                alert(`✅ ${this.displayName}已添加！`);
            }

            // 通用编辑逻辑
            edit(index) {
                const currentItem = systemSettings[this.key][index];
                const currentValue = typeof currentItem === 'object' ? currentItem.name : currentItem;
                const newValue = prompt(`请输入新的${this.displayName}名称:`, currentValue);

                if (newValue && newValue.trim() !== '' && newValue !== currentValue) {
                    const trimmedValue = newValue.trim();

                    // 检查是否重复
                    const existingNames = systemSettings[this.key].map(item =>
                        typeof item === 'object' ? item.name : item
                    );
                    if (existingNames.includes(trimmedValue)) {
                        alert(`该${this.displayName}已存在！`);
                        return;
                    }

                    // 更新为新的数据结构
                    systemSettings[this.key][index] = typeof currentItem === 'object'
                        ? { ...currentItem, name: trimmedValue }
                        : trimmedValue;

                    saveSystemSettings();

                    // 同步到数据库 - 使用新架构直接更新对应表
                    this.syncToDatabase('edit', trimmedValue, currentValue);

                    updateFormOptions();
                    this.load();
                    alert(`✅ ${this.displayName}已更新！`);
                }
            }

            // 通用删除逻辑
            remove(index) {
                const item = systemSettings[this.key][index];
                const itemName = typeof item === 'object' ? item.name : item;

                // 特殊处理客户来源的关联检查
                let confirmMessage = `⚠️ 确定要删除${this.displayName}"${itemName}"吗？\n\n`;
                let hasRelatedRecords = false;

                if (this.key === 'customerSources') {
                    const usedRecords = salesData.filter(record => record.customerSource === itemName);
                    if (usedRecords.length > 0) {
                        confirmMessage += `注意：有 ${usedRecords.length} 条销售记录正在使用此${this.displayName}。\n删除后这些记录的${this.displayName}将被清空。\n\n`;
                        hasRelatedRecords = true;
                    }
                }

                confirmMessage += '删除后将无法恢复！';

                if (confirm(confirmMessage)) {
                    // 清空相关销售记录（仅客户来源）
                    if (this.key === 'customerSources' && hasRelatedRecords) {
                        salesData.forEach(record => {
                            if (record.customerSource === itemName) {
                                record.customerSource = '';
                            }
                        });
                        localStorage.setItem('salesData', JSON.stringify(salesData));
                    }

                    systemSettings[this.key].splice(index, 1);
                    saveSystemSettings();

                    // 同步到数据库 - 使用新架构直接删除对应表记录
                    this.syncToDatabase('remove', itemName);

                    updateFormOptions();
                    this.load();

                    if (this.key === 'customerSources') {
                        loadTableData(); // 刷新表格显示
                    }

                    const successMsg = hasRelatedRecords
                        ? `✅ ${this.displayName}已删除！已清空相关记录的${this.displayName}字段。`
                        : `✅ ${this.displayName}已删除！`;
                    alert(successMsg);
                }
            }

            // 通用加载逻辑
            load() {
                const list = document.getElementById(this.listId);
                if (!list) return;

                list.innerHTML = '';

                if (!systemSettings[this.key] || systemSettings[this.key].length === 0) {
                    list.innerHTML = `<div class="settings-item"><div class="settings-item-content">暂无${this.displayName}</div></div>`;
                    return;
                }

                systemSettings[this.key].forEach((item, index) => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'settings-item';
                    // 处理新数据库结构：item可能是对象或字符串
                    const itemName = typeof item === 'object' ? item.name : item;
                    itemElement.innerHTML = `
                        <div class="settings-item-content">
                            <span class="item-index">${index + 1}</span>
                            <span class="item-text" id="${this.type}_${index}">${itemName}</span>
                        </div>
                        <div class="settings-item-actions">
                            <button class="btn btn-secondary btn-small" onclick="${this.type}Manager.edit(${index})" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button class="btn btn-danger btn-small" onclick="${this.type}Manager.remove(${index})" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    `;
                    list.appendChild(itemElement);
                });
            }

            // 同步到数据库 - 使用新架构
            async syncToDatabase(action, value, oldValue = null) {
                if (!isSupabaseReady || !currentUser || userPermissions.role !== 'admin') {
                    console.log('🔒 跳过数据库同步：权限不足或未登录');
                    return;
                }

                try {
                    const tableName = this.getTableName();

                    switch (action) {
                        case 'add':
                            await this.addToDatabase(tableName, value);
                            break;
                        case 'edit':
                            await this.updateInDatabase(tableName, oldValue, value);
                            break;
                        case 'remove':
                            await this.removeFromDatabase(tableName, value);
                            break;
                    }

                    console.log(`✅ ${this.displayName}已同步到数据库`);

                } catch (error) {
                    console.error(`❌ ${this.displayName}数据库同步失败:`, error);
                    // 不抛出错误，允许本地操作继续
                }
            }

            // 获取对应的数据库表名
            getTableName() {
                const tableMap = {
                    'businessTypes': 'business_types',
                    'incomeTypes': 'income_types',
                    'customerSources': 'customer_sources'
                };
                return tableMap[this.key] || this.key;
            }

            // 添加到数据库 - 安全的列检查
            async addToDatabase(tableName, value) {
                try {
                    // 构建插入对象，只包含必要的列
                    const insertData = {
                        name: value,
                        is_active: true
                    };

                    // 安全地添加可选列
                    if (currentUser?.email) {
                        insertData.created_by = currentUser.email;
                    }

                    const { error } = await supabase
                        .from(tableName)
                        .insert([insertData]);

                    if (error) throw error;
                } catch (error) {
                    console.warn(`添加到 ${tableName} 表时出错，可能是列不存在:`, error.message);
                    // 尝试简化插入，只插入必要字段
                    const { error: simpleError } = await supabase
                        .from(tableName)
                        .insert([{
                            name: value,
                            is_active: true
                        }]);

                    if (simpleError) throw simpleError;
                }
            }

            // 更新数据库记录 - 安全的列检查
            async updateInDatabase(tableName, oldValue, newValue) {
                try {
                    // 构建更新对象，只包含存在的列
                    const updateData = {
                        name: newValue
                    };

                    // 安全地添加可选列
                    if (currentUser?.email) {
                        updateData.updated_by = currentUser.email;
                    }

                    const { error } = await supabase
                        .from(tableName)
                        .update(updateData)
                        .eq('name', oldValue)
                        .eq('is_active', true);

                    if (error) throw error;
                } catch (error) {
                    console.warn(`更新 ${tableName} 表时出错，可能是列不存在:`, error.message);
                    // 尝试简化更新，只更新名称
                    const { error: simpleError } = await supabase
                        .from(tableName)
                        .update({ name: newValue })
                        .eq('name', oldValue)
                        .eq('is_active', true);

                    if (simpleError) throw simpleError;
                }
            }

            // 从数据库删除记录 - 安全的列检查
            async removeFromDatabase(tableName, value) {
                try {
                    // 软删除：设置 is_active = false
                    const updateData = {
                        is_active: false
                    };

                    // 安全地添加可选列
                    if (currentUser?.email) {
                        updateData.updated_by = currentUser.email;
                    }

                    const { error } = await supabase
                        .from(tableName)
                        .update(updateData)
                        .eq('name', value);

                    if (error) throw error;
                } catch (error) {
                    console.warn(`删除 ${tableName} 表记录时出错，可能是列不存在:`, error.message);
                    // 尝试简化删除，只设置 is_active
                    const { error: simpleError } = await supabase
                        .from(tableName)
                        .update({ is_active: false })
                        .eq('name', value);

                    if (simpleError) throw simpleError;
                }
            }
        }

        // 创建设置管理器实例
        const businessTypeManager = new SettingsManager('businessType', 'businessTypes', '业务类型', 'newBusinessType', 'businessTypesList');
        const incomeTypeManager = new SettingsManager('incomeType', 'incomeTypes', '收入类型', 'newIncomeType', 'incomeTypesList');
        const customerSourceManager = new SettingsManager('customerSource', 'customerSources', '客户来源', 'newCustomerSource', 'customerSourcesList');

        // 兼容性函数 - 保持原有函数名可用
        function addBusinessType() { businessTypeManager.add(); }
        function editBusinessType(index) { businessTypeManager.edit(index); }
        function removeBusinessType(index) { businessTypeManager.remove(index); }
        function loadBusinessTypes() { businessTypeManager.load(); }

        function addIncomeType() { incomeTypeManager.add(); }
        function editIncomeType(index) { incomeTypeManager.edit(index); }
        function removeIncomeType(index) { incomeTypeManager.remove(index); }
        function loadIncomeTypes() { incomeTypeManager.load(); }

        function addCustomerSource() { customerSourceManager.add(); }
        function editCustomerSource(index) { customerSourceManager.edit(index); }
        function removeCustomerSource(index) { customerSourceManager.remove(index); }
        function loadCustomerSources() { customerSourceManager.load(); }

        // =====================================================
        // 销售人员数据库同步函数
        // =====================================================
        async function syncSalespersonToDatabase(action, data, oldName = null) {
            if (!isSupabaseReady || !currentUser || userPermissions.role !== 'admin') {
                console.log('🔒 跳过销售人员数据库同步：权限不足或未登录');
                return;
            }

            try {
                switch (action) {
                    case 'add':
                        await addSalespersonToDatabase(data);
                        break;
                    case 'edit':
                        await updateSalespersonInDatabase(data, oldName);
                        break;
                    case 'remove':
                        await removeSalespersonFromDatabase(data);
                        break;
                }

                console.log(`✅ 销售人员数据已同步到数据库`);

            } catch (error) {
                console.error(`❌ 销售人员数据库同步失败:`, error);
                // 不抛出错误，允许本地操作继续
            }
        }

        async function addSalespersonToDatabase(person) {
            try {
                const insertData = {
                    name: person.name,
                    base_salary: person.baseSalary || person.base_salary || 0,
                    commission_type: person.commissionType || person.commission_type || 'fixed',
                    fixed_rate: person.fixedRate || person.fixed_rate || 0.06,
                    ladder_rates: person.ladderRates || person.ladder_rates || [],
                    is_active: true
                };

                // 安全地添加可选列
                if (currentUser?.email) {
                    insertData.created_by = currentUser.email;
                }

                const { error } = await supabase
                    .from('salespeople')
                    .insert([insertData]);

                if (error) throw error;
            } catch (error) {
                console.warn('添加销售人员时出错，尝试简化插入:', error.message);
                // 尝试简化插入
                const { error: simpleError } = await supabase
                    .from('salespeople')
                    .insert([{
                        name: person.name,
                        is_active: true
                    }]);

                if (simpleError) throw simpleError;
            }
        }

        async function updateSalespersonInDatabase(person, oldName) {
            try {
                const updateData = {
                    name: person.name,
                    base_salary: person.baseSalary || person.base_salary || 0,
                    commission_type: person.commissionType || person.commission_type || 'fixed',
                    fixed_rate: person.fixedRate || person.fixed_rate || 0.06,
                    ladder_rates: person.ladderRates || person.ladder_rates || []
                };

                // 安全地添加可选列
                if (currentUser?.email) {
                    updateData.updated_by = currentUser.email;
                }

                const { error } = await supabase
                    .from('salespeople')
                    .update(updateData)
                    .eq('name', oldName || person.name)
                    .eq('is_active', true);

                if (error) throw error;
            } catch (error) {
                console.warn('更新销售人员时出错，尝试简化更新:', error.message);
                // 尝试简化更新
                const { error: simpleError } = await supabase
                    .from('salespeople')
                    .update({ name: person.name })
                    .eq('name', oldName || person.name)
                    .eq('is_active', true);

                if (simpleError) throw simpleError;
            }
        }

        async function removeSalespersonFromDatabase(personName) {
            try {
                // 软删除：设置 is_active = false
                const updateData = {
                    is_active: false
                };

                // 安全地添加可选列
                if (currentUser?.email) {
                    updateData.updated_by = currentUser.email;
                }

                const { error } = await supabase
                    .from('salespeople')
                    .update(updateData)
                    .eq('name', personName);

                if (error) throw error;
            } catch (error) {
                console.warn('删除销售人员时出错，尝试简化删除:', error.message);
                // 尝试简化删除
                const { error: simpleError } = await supabase
                    .from('salespeople')
                    .update({ is_active: false })
                    .eq('name', personName);

                if (simpleError) throw simpleError;
            }
        }

        // 重置设置同步函数
        async function syncResetSettingsToDatabase() {
            if (!isSupabaseReady || !currentUser || userPermissions.role !== 'admin') {
                console.log('🔒 跳过重置设置数据库同步：权限不足或未登录');
                return;
            }

            try {
                // 将所有现有记录设为非活跃
                const tables = ['business_types', 'income_types', 'customer_sources', 'salespeople'];

                for (const table of tables) {
                    await supabase
                        .from(table)
                        .update({
                            is_active: false,
                            updated_by: currentUser.email,
                            updated_at: new Date().toISOString()
                        })
                        .eq('is_active', true);
                }

                console.log('✅ 重置设置已同步到数据库');

            } catch (error) {
                console.error('❌ 重置设置数据库同步失败:', error);
            }
        }

        // 导入设置同步函数
        async function syncImportedSettingsToDatabase() {
            if (!isSupabaseReady || !currentUser || userPermissions.role !== 'admin') {
                console.log('🔒 跳过导入设置数据库同步：权限不足或未登录');
                return;
            }

            try {
                // 这里可以实现导入设置的数据库同步逻辑
                // 由于导入操作比较复杂，暂时只保存到本地
                console.log('ℹ️ 导入设置已保存到本地，数据库同步功能待完善');

            } catch (error) {
                console.error('❌ 导入设置数据库同步失败:', error);
            }
        }

        // =====================================================
        // 统一界面刷新函数 - 消除重复调用
        // =====================================================
        function refreshUI(options = {}) {
            // 默认刷新所有界面，可通过options控制
            const defaults = {
                table: true,
                statistics: true,
                salary: true,
                settings: false,
                forms: false,
                analytics: false
            };

            const config = { ...defaults, ...options };

            try {
                if (config.table) {
                    loadTableData();
                }

                if (config.statistics) {
                    updateStatistics();
                }

                if (config.salary) {
                    updateSalaryDetails();
                }

                if (config.settings) {
                    loadSettingsData();
                }

                if (config.forms) {
                    updateFormOptions();
                }

                if (config.analytics && document.getElementById('statistics').classList.contains('active')) {
                    setTimeout(() => updateAllStatistics(), 50);
                }

                console.log('✅ 界面刷新完成', config);

            } catch (error) {
                console.error('❌ 界面刷新失败:', error);
            }
        }

        // 兼容性函数 - 保持原有调用方式，避免重复刷新
        function refreshTableAndStats() {
            if (!refreshUI.isRefreshing) {
                refreshUI({ table: true, statistics: true, salary: true });
            }
        }

        function refreshAll() {
            if (!refreshUI.isRefreshing) {
                refreshUI({ table: true, statistics: true, salary: true, settings: true, forms: true });
            }
        }

        // 查看销售人员业绩
        function viewSalespersonStats(index) {
            const person = systemSettings.salespeople[index];
            const currentMonth = new Date().toISOString().substring(0, 7);
            const monthRecords = salesData.filter(record =>
                record.date.startsWith(currentMonth) && record.salesperson === person.name
            );

            const totalSales = monthRecords.reduce((sum, record) => sum + record.amount, 0);
            const recordCount = monthRecords.length;
            const avgSale = recordCount > 0 ? totalSales / recordCount : 0;

            // 计算提成
            let commission = 0;
            const commissionType = person.commission_type || 'fixed';
            const fixedRate = Number(person.fixed_rate) || 0;
            const ladderRates = person.ladder_rates || [];

            if (commissionType === 'ladder' && ladderRates.length > 0) {
                commission = calculateLadderCommission(totalSales, ladderRates);
            } else {
                commission = totalSales * fixedRate;
            }

            const baseSalary = Number(person.base_salary) || 0;
            const totalSalary = baseSalary + commission;

            alert(`📊 ${person.name} 本月业绩统计\n\n` +
                  `🏆 销售总额: ¥${(Number(totalSales) || 0).toLocaleString()}\n` +
                  `📈 成交笔数: ${recordCount} 笔\n` +
                  `💰 平均订单: ¥${(Number(avgSale) || 0).toLocaleString()}\n` +
                  `🎯 提成金额: ¥${(Number(commission) || 0).toLocaleString()}\n` +
                  `💵 预计总工资: ¥${(Number(totalSalary) || 0).toLocaleString()}`);
        }
        
        // 切换提成输入框显示
        function toggleCommissionInput() {
            const typeSelect = document.getElementById('newCommissionType');
            const rateInput = document.getElementById('newFixedRate');
            
            if (typeSelect.value === 'fixed') {
                rateInput.style.display = 'inline-block';
                rateInput.placeholder = '提成%';
            } else {
                rateInput.style.display = 'none';
            }
        }

        // 添加销售人员
        function addSalesperson() {
            const nameInput = document.getElementById('newSalesperson');
            const salaryInput = document.getElementById('newBaseSalary');
            const typeSelect = document.getElementById('newCommissionType');
            const rateInput = document.getElementById('newFixedRate');
            
            const name = nameInput.value.trim();
            const baseSalary = parseFloat(salaryInput.value) || 0;
            const commissionType = typeSelect.value;
            const fixedRate = commissionType === 'fixed' ? (parseFloat(rateInput.value) || 6) / 100 : 0;
            
            if (!name) {
                alert('请输入销售人员姓名');
                return;
            }
            
            if (baseSalary <= 0) {
                alert('请输入有效的基础工资');
                return;
            }
            
            if (commissionType === 'fixed' && (parseFloat(rateInput.value) || 0) <= 0) {
                alert('请输入有效的提成比例');
                return;
            }
            
            if (systemSettings.salespeople.some(p => p.name === name)) {
                alert('该销售人员已存在');
                return;
            }
            
            const newPerson = {
                name: name,
                baseSalary: baseSalary,
                commissionType: commissionType,
                fixedRate: fixedRate,
                ladderRates: []
            };
            
            systemSettings.salespeople.push(newPerson);
            saveSystemSettings();
            
            // 同步到数据库 - 销售人员通过独立表管理
            syncSalespersonToDatabase('add', newPerson);

            updateFormOptions();
            loadSalespersonList();
            updateSalaryDetails();
            
            // 如果当前在统计分析标签，则更新统计
            if (document.getElementById('statistics').classList.contains('active')) {
                setTimeout(() => updateAllStatistics(), 50);
            }
            
            // 清空输入
            nameInput.value = '';
            salaryInput.value = '';
            rateInput.value = '';
            typeSelect.value = 'fixed';
            toggleCommissionInput();
        }
        
        // 编辑销售人员
        function editSalesperson(index) {
            const person = systemSettings.salespeople[index];
            
            // 创建编辑表单的HTML
            const editForm = `
                <div style="background: white; padding: 20px; border-radius: 10px; max-width: 400px; margin: 20px auto;">
                    <h4>编辑销售人员</h4>
                    <div style="margin-bottom: 15px;">
                        <label>姓名:</label><br>
                        <input type="text" id="editName" value="${person.name}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label>基础工资:</label><br>
                        <input type="number" id="editSalary" value="${Number(person.baseSalary || person.base_salary) || 0}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label>提成类型:</label><br>
                        <select id="editCommissionType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" onchange="toggleEditCommissionInput()">
                            <option value="fixed" ${(person.commissionType || person.commission_type || 'fixed') === 'fixed' ? 'selected' : ''}>固定比例</option>
                            <option value="ladder" ${(person.commissionType || person.commission_type || 'fixed') === 'ladder' ? 'selected' : ''}>阶梯式</option>
                        </select>
                    </div>
                    <div id="editFixedRateDiv" style="margin-bottom: 15px; ${(person.commissionType || person.commission_type || 'fixed') === 'fixed' ? '' : 'display: none;'}">
                        <label>提成比例(%):</label><br>
                        <input type="number" id="editFixedRate" value="${((Number(person.fixedRate || person.fixed_rate) || 0) * 100).toFixed(1)}" step="0.1" min="0" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="text-align: center;">
                        <button class="btn" onclick="saveEditPerson(${index})">保存</button>
                        <button class="btn btn-secondary" onclick="closeEditForm()">取消</button>
                    </div>
                </div>
            `;
            
            // 创建模态框
            const modal = document.createElement('div');
            modal.id = 'editPersonModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            modal.innerHTML = editForm;
            document.body.appendChild(modal);
        }
        
        // 切换编辑时的提成输入框
        function toggleEditCommissionInput() {
            const typeSelect = document.getElementById('editCommissionType');
            const rateDiv = document.getElementById('editFixedRateDiv');
            
            if (typeSelect.value === 'fixed') {
                rateDiv.style.display = 'block';
            } else {
                rateDiv.style.display = 'none';
            }
        }
        
        // 保存编辑的销售人员
        function saveEditPerson(index) {
            const person = systemSettings.salespeople[index];
            const newName = document.getElementById('editName').value.trim();
            const newBaseSalary = parseFloat(document.getElementById('editSalary').value) || 0;
            const newCommissionType = document.getElementById('editCommissionType').value;
            const newFixedRate = newCommissionType === 'fixed' ? (parseFloat(document.getElementById('editFixedRate').value) || 0) / 100 : (Number(person.fixedRate || person.fixed_rate) || 0);

            if (!newName) {
                alert('请输入销售人员姓名');
                return;
            }

            if (newBaseSalary <= 0) {
                alert('请输入有效的基础工资');
                return;
            }

            if (newCommissionType === 'fixed' && newFixedRate <= 0) {
                alert('请输入有效的提成比例');
                return;
            }

            // 检查姓名是否重复
            if (newName !== person.name && systemSettings.salespeople.some(p => p.name === newName)) {
                alert('该销售人员姓名已存在');
                return;
            }

            // 🔧 修复：更新数据 - 同时更新新旧字段名格式
            person.name = newName;
            person.baseSalary = newBaseSalary;
            person.base_salary = newBaseSalary; // 兼容数据库格式
            person.commissionType = newCommissionType;
            person.commission_type = newCommissionType; // 兼容数据库格式
            person.fixedRate = newFixedRate;
            person.fixed_rate = newFixedRate; // 兼容数据库格式

            // 如果从阶梯式改为固定比例，清空阶梯数据
            if (newCommissionType === 'fixed') {
                person.ladderRates = [];
                person.ladder_rates = []; // 兼容数据库格式
            }
            
            saveSystemSettings();
            
            // 同步到数据库 - 销售人员通过独立表管理
            syncSalespersonToDatabase('edit', person, person.name);

            // 🔧 修复：立即刷新工资计算
            console.log('🔄 销售人员信息更新，强制刷新工资计算...');
            updateSalaryDetails();

            refreshUI({ forms: true, analytics: true });
            loadSalespersonList();
            closeEditForm();

            alert('✅ 销售人员信息已更新！工资计算已同步更新。');
        }
        
        // 关闭编辑表单
        function closeEditForm() {
            const modal = document.getElementById('editPersonModal');
            if (modal) {
                modal.remove();
            }
        }
        
        // 删除销售人员
        function removeSalesperson(index) {
            const person = systemSettings.salespeople[index];
            
            // 检查是否有销售记录使用此销售人员
            const usedRecords = salesData.filter(record => record.salesperson === person.name);
            
            let confirmMessage = `⚠️ 确定要删除销售人员"${person.name}"吗？\n\n`;
            if (usedRecords.length > 0) {
                confirmMessage += `注意：有 ${usedRecords.length} 条销售记录属于此销售人员。\n删除后这些记录的销售人员将设置为"其他"。\n\n`;
            }
            confirmMessage += '删除后将无法恢复！\n相关的工资计算和统计数据也会受到影响。';
            
            if (confirm(confirmMessage)) {
                // 将相关销售记录的销售人员设置为"其他"
                if (usedRecords.length > 0) {
                    salesData.forEach(record => {
                        if (record.salesperson === person.name) {
                            record.salesperson = '其他';
                        }
                    });
                    localStorage.setItem('salesData', JSON.stringify(salesData));
                }
                
                systemSettings.salespeople.splice(index, 1);
                saveSystemSettings();
                
                // 同步到数据库 - 销售人员通过独立表管理
                syncSalespersonToDatabase('remove', person.name);

                refreshUI({ forms: true, analytics: true });
                loadSalespersonList();
                
                if (usedRecords.length > 0) {
                    alert(`✅ 销售人员已删除！已将 ${usedRecords.length} 条记录的销售人员设置为"其他"。`);
                } else {
                    alert('✅ 销售人员已删除！');
                }
            }
        }
        
        // 编辑阶梯式提成
        function editLadder(index) {
            currentEditPersonIndex = index;
            const person = systemSettings.salespeople[index];
            
            document.getElementById('currentEditPerson').textContent = person.name;
            document.getElementById('ladderModal').style.display = 'block';
            
            loadLadderEditor(person.ladder_rates || []);
        }
        
        // 加载阶梯编辑器
        function loadLadderEditor(ladderRates) {
            const editor = document.getElementById('ladderEditor');
            editor.innerHTML = '';
            
            ladderRates.forEach((ladder, index) => {
                addLadderItem(ladder.minAmount, ladder.rate, index);
            });
            
            // 如果没有设置阶梯，加载标准阶梯模板
            if (ladderRates.length === 0) {
                addLadderItem(0, 0.06, 0);          // ≤ 3万元：6%
                addLadderItem(30000, 0.15, 1);      // 3万-10万元：15%
                addLadderItem(100000, 0.20, 2);     // 10万-20万元：20%
                addLadderItem(200000, 0.25, 3);     // > 20万元：25%
            }
        }
        
        // 添加阶梯项目
        function addLadderItem(minAmount = 0, rate = 0.06, index = -1) {
            const editor = document.getElementById('ladderEditor');
            const item = document.createElement('div');
            item.className = 'ladder-item';
            // 将小数转换为百分比显示
            const displayRate = (rate * 100).toFixed(1);
            
            // 根据金额显示不同的提示文字
            let amountLabel = '';
            if (minAmount === 0) {
                amountLabel = '销售额 ≤ 3万元';
            } else if (minAmount === 30000) {
                amountLabel = '销售额 > 3万元';
            } else if (minAmount === 100000) {
                amountLabel = '销售额 > 10万元';
            } else if (minAmount === 200000) {
                amountLabel = '销售额 > 20万元';
            } else {
                amountLabel = `销售额 ≥ ${(minAmount/10000).toFixed(1)}万元`;
            }
            
            item.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">
                    <span style="min-width: 100px; font-size: 12px; color: #666;">${amountLabel}</span>
                    <input type="number" class="min-amount" value="${minAmount}" placeholder="起始金额" style="width: 80px;">
                    <span>元，提成:</span>
                    <input type="number" class="rate" value="${displayRate}" step="0.1" placeholder="比例" min="0" style="width: 60px;">
                    <span>%</span>
                    <button class="btn btn-danger btn-small" onclick="removeLadderItem(this)">删除</button>
                </div>
            `;
            editor.appendChild(item);
        }
        
        // 删除阶梯项目
        function removeLadderItem(button) {
            button.parentElement.remove();
        }
        
        // 添加新阶梯等级
        function addLadderLevel() {
            addLadderItem();
        }

        // 设置标准阶梯
        function setStandardLadder() {
            if (confirm('确定要设置为标准阶梯吗？这将清除当前所有阶梯设置。')) {
                const editor = document.getElementById('ladderEditor');
                editor.innerHTML = '';
                
                // 添加标准阶梯
                addLadderItem(0, 0.06, 0);          // ≤ 3万元：6%
                addLadderItem(30000, 0.15, 1);      // 3万-10万元：15%
                addLadderItem(100000, 0.20, 2);     // 10万-20万元：20%
                addLadderItem(200000, 0.25, 3);     // > 20万元：25%
            }
        }

        // 清空所有阶梯
        function clearAllLadders() {
            if (confirm('确定要清空所有阶梯设置吗？')) {
                const editor = document.getElementById('ladderEditor');
                editor.innerHTML = '';
            }
        }
        
        // 保存阶梯设置
        function saveLadderSettings() {
            if (currentEditPersonIndex === -1) return;
            
            const items = document.querySelectorAll('.ladder-item');
            const ladderRates = [];
            
            items.forEach(item => {
                // 🔧 修复：安全地获取元素值，避免空值错误
                const minAmountElement = item.querySelector('.min-amount');
                const rateElement = item.querySelector('.rate');

                if (minAmountElement && rateElement) {
                    const minAmount = parseFloat(minAmountElement.value) || 0;
                    const ratePercent = parseFloat(rateElement.value) || 0;
                    const rate = ratePercent / 100; // 将百分比转换为小数

                    if (minAmount >= 0 && rate >= 0) {
                        ladderRates.push({ minAmount, rate });
                    }
                } else {
                    console.warn('⚠️ 阶梯设置项缺少必要的输入元素');
                }
            });
            
            // 按最低金额排序
            ladderRates.sort((a, b) => a.minAmount - b.minAmount);
            
            systemSettings.salespeople[currentEditPersonIndex].ladder_rates = ladderRates;
            saveSystemSettings();
            
            // 同步到数据库 - 销售人员阶梯设置通过独立表管理
            syncSalespersonToDatabase('edit', systemSettings.salespeople[currentEditPersonIndex]);

            // 🔧 修复：立即刷新工资计算
            console.log('🔄 阶梯设置更新，强制刷新工资计算...');
            updateSalaryDetails();

            refreshUI({ salary: true, analytics: true });
            loadSalespersonList();
            closeLadderModal();
            alert('✅ 阶梯式提成设置已保存！工资计算已更新。');
        }
        
        // 关闭阶梯模态框
        function closeLadderModal() {
            document.getElementById('ladderModal').style.display = 'none';
            currentEditPersonIndex = -1;
        }

        // =====================================================
        // 收款管理功能
        // =====================================================

        let currentSalesRecordId = null;
        let currentPaymentRecords = [];

        // 打开收款管理弹窗
        async function openPaymentModal(salesRecordId) {
            currentSalesRecordId = salesRecordId;

            // 查找销售记录
            const salesRecord = salesData.find(record => record.id == salesRecordId);
            if (!salesRecord) {
                alert('❌ 找不到销售记录');
                return;
            }

            // 显示合同信息
            document.getElementById('contractCustomer').textContent = salesRecord.customer;
            document.getElementById('contractAmount').textContent = `¥${(Number(salesRecord.contractAmount) || 0).toLocaleString()}`;

            // 设置默认收款日期为今天
            document.getElementById('newPaymentDate').value = new Date().toISOString().split('T')[0];

            // 清空表单
            clearPaymentForm();

            // 加载收款记录
            await loadPaymentRecords(salesRecordId);

            // 显示弹窗
            document.getElementById('paymentModal').style.display = 'block';
        }

        // 关闭收款管理弹窗
        function closePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
            currentSalesRecordId = null;
            currentPaymentRecords = [];
        }

        // 清空收款表单
        function clearPaymentForm() {
            document.getElementById('newPaymentAmount').value = '';
            document.getElementById('newPaymentMethod').value = '银行转账';
            document.getElementById('newPaymentBank').value = '';
            document.getElementById('newPaymentTransaction').value = '';
            document.getElementById('newPaymentRemarks').value = '';
        }

        // 加载收款记录
        async function loadPaymentRecords(salesRecordId) {
            try {
                if (!isSupabaseReady || !currentUser) {
                    // 如果Supabase未就绪，显示提示信息
                    updatePaymentSummary(0, 0, 0);
                    document.getElementById('paymentRecordsBody').innerHTML =
                        '<tr><td colspan="7" style="text-align: center; color: #666;">请先登录以查看收款记录</td></tr>';
                    return;
                }

                // 从Supabase查询收款记录 - 使用备用查询策略
                let paymentRecords = null;
                let error = null;

                // 首先尝试从payment_details_view查询
                try {
                    const result = await supabase
                        .from('payment_details_view')
                        .select('*')
                        .eq('sales_record_id', salesRecordId)
                        .order('payment_date', { ascending: true });

                    paymentRecords = result.data;
                    error = result.error;
                } catch (viewError) {
                    console.warn('payment_details_view查询失败，尝试备用查询:', viewError);

                    // 备用查询：直接从payment_records表查询
                    try {
                        const fallbackResult = await supabase
                            .from('payment_records')
                            .select('*')
                            .eq('sales_record_id', salesRecordId)
                            .order('payment_date', { ascending: true });

                        paymentRecords = fallbackResult.data;
                        error = fallbackResult.error;
                    } catch (fallbackError) {
                        console.error('备用查询也失败:', fallbackError);
                        error = fallbackError;
                    }
                }

                if (error) {
                    console.error('查询收款记录失败:', error);
                    showToast('查询收款记录失败，请检查数据库连接', 'error');
                    return;
                }

                currentPaymentRecords = paymentRecords || [];

                // 更新收款汇总信息
                const totalReceived = currentPaymentRecords.reduce((sum, record) => sum + Number(record.payment_amount), 0);
                const contractAmount = Number(document.getElementById('contractAmount').textContent.replace(/[¥,]/g, '')) || 0;
                const remainingAmount = contractAmount - totalReceived;

                updatePaymentSummary(totalReceived, remainingAmount, currentPaymentRecords.length);

                // 更新收款记录表格
                updatePaymentRecordsTable();

            } catch (error) {
                console.error('加载收款记录失败:', error);
                showToast('加载收款记录失败', 'error');
            }
        }

        // 更新收款汇总信息
        function updatePaymentSummary(totalReceived, remainingAmount, paymentCount) {
            document.getElementById('totalReceived').textContent = `¥${totalReceived.toLocaleString()}`;
            document.getElementById('remainingAmount').textContent = `¥${remainingAmount.toLocaleString()}`;
            document.getElementById('paymentCount').textContent = `${paymentCount}次`;

            // 更新收款状态
            const contractAmount = Number(document.getElementById('contractAmount').textContent.replace(/[¥,]/g, '')) || 0;
            let status = '未收款';
            let statusClass = 'status-unpaid';

            if (contractAmount === 0) {
                status = '无合同';
                statusClass = 'status-no-contract';
            } else if (totalReceived === 0) {
                status = '未收款';
                statusClass = 'status-unpaid';
            } else if (totalReceived < contractAmount) {
                status = '部分收款';
                statusClass = 'status-partial';
            } else if (totalReceived === contractAmount) {
                status = '已收齐';
                statusClass = 'status-paid';
            } else {
                status = '超额收款';
                statusClass = 'status-overpaid';
            }

            document.getElementById('contractStatus').innerHTML = `<span class="payment-status ${statusClass}">${status}</span>`;
        }

        // 更新收款记录表格
        function updatePaymentRecordsTable() {
            const tbody = document.getElementById('paymentRecordsBody');
            tbody.innerHTML = '';

            if (currentPaymentRecords.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #666;">暂无收款记录</td></tr>';
                return;
            }

            currentPaymentRecords.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.payment_date}</td>
                    <td>¥${Number(record.payment_amount).toLocaleString()}</td>
                    <td>${record.payment_method}</td>
                    <td>${record.bank_info || '-'}</td>
                    <td>${record.transaction_no || '-'}</td>
                    <td title="${record.payment_remarks || '-'}">${record.payment_remarks || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deletePaymentRecord('${record.payment_id}')" title="删除收款记录">🗑️</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 添加收款记录
        async function addPaymentRecord() {
            try {
                // 验证表单
                const paymentDate = document.getElementById('newPaymentDate').value;
                const paymentAmount = parseFloat(document.getElementById('newPaymentAmount').value);
                const paymentMethod = document.getElementById('newPaymentMethod').value;
                const bankInfo = document.getElementById('newPaymentBank').value;
                const transactionNo = document.getElementById('newPaymentTransaction').value;
                const remarks = document.getElementById('newPaymentRemarks').value;

                if (!paymentDate) {
                    alert('请选择收款日期');
                    return;
                }

                if (!paymentAmount || paymentAmount <= 0) {
                    alert('请输入有效的收款金额');
                    return;
                }

                if (!isSupabaseReady || !currentUser) {
                    alert('请先登录以添加收款记录');
                    return;
                }

                // 添加到Supabase
                const { data, error } = await supabase
                    .from('payment_records')
                    .insert([{
                        sales_record_id: currentSalesRecordId,
                        payment_date: paymentDate,
                        payment_amount: paymentAmount,
                        payment_method: paymentMethod,
                        bank_info: bankInfo,
                        transaction_no: transactionNo,
                        remarks: remarks,
                        created_by: currentUser.email
                    }]);

                if (error) {
                    console.error('添加收款记录失败:', error);
                    showToast('添加收款记录失败', 'error');
                    return;
                }

                // 清空表单
                clearPaymentForm();

                // 重新加载收款记录
                await loadPaymentRecords(currentSalesRecordId);

                // 刷新主表格（更新收款状态）
                loadTableData();

                showToast('收款记录添加成功', 'success');

            } catch (error) {
                console.error('添加收款记录失败:', error);
                showToast('添加收款记录失败', 'error');
            }
        }

        // 删除收款记录
        async function deletePaymentRecord(paymentId) {
            if (!confirm('确定要删除这条收款记录吗？')) {
                return;
            }

            try {
                if (!isSupabaseReady || !currentUser) {
                    alert('请先登录以删除收款记录');
                    return;
                }

                // 从Supabase删除（软删除）
                const { error } = await supabase
                    .from('payment_records')
                    .update({ is_deleted: true })
                    .eq('id', paymentId);

                if (error) {
                    console.error('删除收款记录失败:', error);
                    showToast('删除收款记录失败', 'error');
                    return;
                }

                // 重新加载收款记录
                await loadPaymentRecords(currentSalesRecordId);

                // 刷新主表格（更新收款状态）
                loadTableData();

                showToast('收款记录删除成功', 'success');

            } catch (error) {
                console.error('删除收款记录失败:', error);
                showToast('删除收款记录失败', 'error');
            }
        }

                // 重置所有设置
        function resetAllSettings() {
            if (confirm('⚠️ 确定要重置所有系统设置吗？\n\n这将会：\n• 恢复默认的业务类型、收入类型、客户来源\n• 清空所有销售人员设置\n• 不会删除销售记录数据\n\n此操作不可恢复！')) {
                if (confirm('🔴 最后确认：真的要重置所有设置吗？')) {
                    // 恢复默认设置
                    systemSettings = {
                        businessTypes: ['咨询服务', '咨询首款', '咨询尾款', '陪跑服务', '陪跑首付', '陪跑尾款', '培训服务', '培训占位', '其他'],
                        incomeTypes: ['押金', '培训收入', '日签收入', '英签收入', '美签收入', '加签收入', '澳签收入', '咨询收入', '日租收入', '其他收入'],
                        customerSources: ['自有客户', '运营线索', '运营转介绍', '客户转介绍', '管理内推'],
                        salespeople: []
                    };
                    
                    saveSystemSettings();
                    
                    // 同步到数据库 - 重置操作需要清空所有表
                    syncResetSettingsToDatabase();

                    updateFormOptions();
                    loadSettingsData();
                    
                    alert('✅ 系统设置已重置为默认状态！');
                }
            }
        }
        
        // 导出设置
        function exportSettings() {
            const settingsData = {
                timestamp: new Date().toISOString(),
                version: '1.3',
                settings: systemSettings
            };
            
            const blob = new Blob([JSON.stringify(settingsData, null, 2)], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `系统设置_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            alert('✅ 系统设置已导出！');
        }
        
        // 导入设置
        function importSettings() {
            const file = document.getElementById('importSettingsFile').files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);
                    
                    if (importedData.settings) {
                        if (confirm('确定要导入设置吗？这将覆盖当前所有系统设置！')) {
                            systemSettings = importedData.settings;
                            
                            // 确保必要字段存在
                            if (!systemSettings.businessTypes) systemSettings.businessTypes = [];
                            if (!systemSettings.incomeTypes) systemSettings.incomeTypes = [];
                            if (!systemSettings.customerSources) systemSettings.customerSources = [];
                            if (!systemSettings.salespeople) systemSettings.salespeople = [];
                            
                            saveSystemSettings();
                            
                            // 同步到数据库 - 导入操作需要同步所有设置
                            syncImportedSettingsToDatabase();

                            updateFormOptions();
                            loadSettingsData();
                            updateSalaryDetails();
                            
                            alert('✅ 设置导入成功！');
                        }
                    } else {
                        alert('❌ 导入文件格式错误！');
                    }
                } catch (error) {
                    console.error('导入失败:', error);
                    alert('❌ 导入失败，请检查文件格式！');
                }
            };
            reader.readAsText(file);
            
            // 清空文件选择
            document.getElementById('importSettingsFile').value = '';
        }

        // 数据同步检查和修复
        function syncDataConsistency() {
            console.log('=== 开始数据同步检查 ===');
            
            let hasChanges = false;
            let report = '📊 数据同步报告：\n\n';
            
            // 检查并修复销售记录中的无效客户来源
            let invalidSourceCount = 0;
            salesData.forEach(record => {
                if (record.customerSource && !systemSettings.customerSources.includes(record.customerSource)) {
                    console.log(`发现无效客户来源: ${record.customerSource}`);
                    record.customerSource = ''; // 清空无效的客户来源
                    invalidSourceCount++;
                    hasChanges = true;
                }
            });
            
            if (invalidSourceCount > 0) {
                report += `🔧 修复了 ${invalidSourceCount} 条记录的无效客户来源\n`;
            }
            
            // 检查并修复销售记录中的无效销售人员
            let invalidSalespersonCount = 0;
            const validSalespersonNames = systemSettings.salespeople.map(p => p.name).concat(['其他']);
            salesData.forEach(record => {
                if (record.salesperson && !validSalespersonNames.includes(record.salesperson)) {
                    console.log(`发现无效销售人员: ${record.salesperson}`);
                    record.salesperson = '其他'; // 设置为"其他"
                    invalidSalespersonCount++;
                    hasChanges = true;
                }
            });
            
            if (invalidSalespersonCount > 0) {
                report += `🔧 修复了 ${invalidSalespersonCount} 条记录的无效销售人员\n`;
            }
            
            // 检查并修复销售记录中的无效业务类型
            let invalidBusinessTypeCount = 0;
            salesData.forEach(record => {
                if (record.businessType && !systemSettings.businessTypes.includes(record.businessType)) {
                    console.log(`发现无效业务类型: ${record.businessType}`);
                    // 保留原值但添加到系统设置中
                    if (record.businessType.trim()) {
                        systemSettings.businessTypes.push(record.businessType);
                        invalidBusinessTypeCount++;
                        hasChanges = true;
                    }
                }
            });
            
            if (invalidBusinessTypeCount > 0) {
                report += `📝 发现并添加了 ${invalidBusinessTypeCount} 个新的业务类型\n`;
            }
            
            // 检查并修复销售记录中的无效收入类型
            let invalidIncomeTypeCount = 0;
            salesData.forEach(record => {
                if (record.incomeType && !systemSettings.incomeTypes.includes(record.incomeType)) {
                    console.log(`发现无效收入类型: ${record.incomeType}`);
                    // 保留原值但添加到系统设置中
                    if (record.incomeType.trim()) {
                        systemSettings.incomeTypes.push(record.incomeType);
                        invalidIncomeTypeCount++;
                        hasChanges = true;
                    }
                }
            });
            
            if (invalidIncomeTypeCount > 0) {
                report += `📝 发现并添加了 ${invalidIncomeTypeCount} 个新的收入类型\n`;
            }
            
            // 保存修复后的数据
            if (hasChanges) {
                localStorage.setItem('salesData', JSON.stringify(salesData));
                localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
                report += '\n✅ 所有数据不一致问题已修复并保存';
                console.log('数据同步修复完成');
            } else {
                report += '\n✨ 数据检查完成，未发现不一致问题';
                console.log('数据检查完成，无需修复');
            }
            
            return { hasChanges, report };
        }
        
        // 强制刷新所有数据
        function forceRefreshAll() {
            console.log('=== 强制刷新所有数据 ===');
            
            // 首先进行数据同步检查
            const syncResult = syncDataConsistency();
            
            // 重新读取localStorage中的数据
            const storedSettings = localStorage.getItem('systemSettings');
            console.log('localStorage中的systemSettings:', storedSettings);
            
            if (storedSettings) {
                try {
                    systemSettings = JSON.parse(storedSettings);
                    console.log('重新加载的设置:', systemSettings);
                } catch (error) {
                    console.error('解析localStorage数据失败:', error);
                }
            }
            
            // 重新读取销售数据
            const storedSalesData = localStorage.getItem('salesData');
            if (storedSalesData) {
                try {
                    salesData = JSON.parse(storedSalesData);
                    console.log('重新加载的销售数据:', salesData.length, '条记录');
                } catch (error) {
                    console.error('解析销售数据失败:', error);
                }
            }
            
            // 强制刷新表单选项
            updateFormOptions();
            
            // 强制刷新设置界面
            loadSettingsData();
            
            // 刷新表格数据
            loadTableData();
            updateStatistics();
            updateSalaryDetails();
            
            // 如果当前在统计分析标签，则更新统计
            if (document.getElementById('statistics').classList.contains('active')) {
                setTimeout(() => updateAllStatistics(), 50);
            }
            
            const message = syncResult.hasChanges ? 
                `✅ 强制刷新完成！\n\n${syncResult.report}` : 
                '✅ 强制刷新完成！数据状态良好。';
            
            alert(message);
        }
        


        // 测试阶梯式提成计算（用于验证计算是否正确）
        function testLadderCommission() {
            const testRates = [
                { minAmount: 0, rate: 0.06 },       // ≤ 3万元：6%
                { minAmount: 30000, rate: 0.15 },   // 3万-10万元：15%
                { minAmount: 100000, rate: 0.20 },  // 10万-20万元：20%
                { minAmount: 200000, rate: 0.25 }   // > 20万元：25%
            ];
            
            // 测试案例
            const testCases = [
                { sales: 30000, expected: 1800 },     // 3万元 = 3万×6% = 1800元
                { sales: 80000, expected: 9300 },     // 8万元 = 3万×6% + 5万×15% = 1800+7500 = 9300元
                { sales: 150000, expected: 22300 },   // 15万元 = 3万×6% + 7万×15% + 5万×20% = 1800+10500+10000 = 22300元
                { sales: 250000, expected: 44800 }    // 25万元 = 3万×6% + 7万×15% + 10万×20% + 5万×25% = 1800+10500+20000+12500 = 44800元
            ];
            
            console.log('=== 阶梯式提成计算测试 ===');
            testCases.forEach(testCase => {
                const calculated = calculateLadderCommission(testCase.sales, testRates);
                const isCorrect = Math.abs(calculated - testCase.expected) < 0.01;
                                 console.log(`销售额: ${testCase.sales}, 期望提成: ${testCase.expected}, 计算提成: ${calculated.toFixed(2)}, 结果: ${isCorrect ? '✅正确' : '❌错误'}`);
             });
        }
        
        // 独立的数据修复函数（用于按钮调用）
        async function fixDataConsistency() {
            if (confirm('🔧 确定要检查并修复数据一致性问题吗？\n\n此操作将：\n• 检查销售记录中的无效数据\n• 修复"未定义"显示问题\n• 自动创建缺失的关联数据\n• 确保数据完整性\n\n建议在修复前先备份数据。')) {

                try {
                    showToast('正在修复数据，请稍候...', 'info');

                    // 1. 先执行本地数据修复
                    const localResult = syncDataConsistency();

                    // 2. 如果连接到Supabase，执行数据库修复
                    if (isSupabaseReady && currentUser) {
                        await fixSupabaseDataRelations();
                    }

                    // 3. 修复后刷新所有界面
                    updateFormOptions();
                    loadSettingsData();
                    await loadSalesRecordsFromDatabase();
                    updateStatistics();
                    updateSalaryDetails();

                    // 如果当前在统计分析标签，则更新统计
                    if (document.getElementById('statistics').classList.contains('active')) {
                        setTimeout(() => updateAllStatistics(), 50);
                    }

                    showToast('数据修复完成！', 'success');
                    alert('✅ 数据修复完成！\n\n' + localResult.report + '\n\n请检查销售记录是否还有"未定义"显示。');

                } catch (error) {
                    console.error('数据修复失败:', error);
                    showToast('数据修复失败', 'error');
                    alert('❌ 数据修复失败：' + error.message);
                }
            }
        }

        // 修复Supabase数据库中的关联问题
        async function fixSupabaseDataRelations() {
            try {
                console.log('🔧 开始修复Supabase数据关联...');

                // 1. 首先确保基础数据表有数据
                await ensureBasicDataExists();

                // 2. 查询所有需要修复的销售记录
                const { data: recordsToFix, error: queryError } = await supabase
                    .from('sales_records')
                    .select('id, customer_source, business_type, income_type, salesperson, customer_source_id, business_type_id, income_type_id, salesperson_id')
                    .or('customer_source_id.is.null,business_type_id.is.null,income_type_id.is.null,salesperson_id.is.null');

                if (queryError) {
                    console.error('查询需要修复的记录失败:', queryError);
                    return;
                }

                console.log(`找到 ${recordsToFix?.length || 0} 条需要修复的记录`);

                if (!recordsToFix || recordsToFix.length === 0) {
                    console.log('没有需要修复的记录');
                    return;
                }

                let fixedCount = 0;

                for (const record of recordsToFix) {
                    const updates = {};

                    // 修复customer_source_id
                    if (!record.customer_source_id) {
                        if (record.customer_source) {
                            const sourceId = await findOrCreateId('customer_sources', record.customer_source);
                            if (sourceId) updates.customer_source_id = sourceId;
                        } else {
                            // 设置默认值
                            const defaultId = await findOrCreateId('customer_sources', '自有客户');
                            if (defaultId) updates.customer_source_id = defaultId;
                        }
                    }

                    // 修复business_type_id
                    if (!record.business_type_id) {
                        if (record.business_type) {
                            const typeId = await findOrCreateId('business_types', record.business_type);
                            if (typeId) updates.business_type_id = typeId;
                        } else {
                            // 设置默认值
                            const defaultId = await findOrCreateId('business_types', '其他');
                            if (defaultId) updates.business_type_id = defaultId;
                        }
                    }

                    // 修复income_type_id
                    if (!record.income_type_id) {
                        if (record.income_type) {
                            const incomeId = await findOrCreateId('income_types', record.income_type);
                            if (incomeId) updates.income_type_id = incomeId;
                        } else {
                            // 设置默认值
                            const defaultId = await findOrCreateId('income_types', '其他收入');
                            if (defaultId) updates.income_type_id = defaultId;
                        }
                    }

                    // 修复salesperson_id
                    if (!record.salesperson_id) {
                        if (record.salesperson) {
                            const salespersonId = await findOrCreateId('salespeople', record.salesperson);
                            if (salespersonId) updates.salesperson_id = salespersonId;
                        }
                    }

                    // 如果有需要更新的字段，执行更新
                    if (Object.keys(updates).length > 0) {
                        const { error: updateError } = await supabase
                            .from('sales_records')
                            .update(updates)
                            .eq('id', record.id);

                        if (updateError) {
                            console.error(`更新记录 ${record.id} 失败:`, updateError);
                        } else {
                            fixedCount++;
                            console.log(`✅ 修复记录 ${record.id}:`, updates);
                        }
                    }
                }

                console.log(`✅ Supabase数据修复完成，共修复 ${fixedCount} 条记录`);

            } catch (error) {
                console.error('修复Supabase数据关联失败:', error);
                throw error;
            }
        }

        // 确保基础数据表有必要的数据
        async function ensureBasicDataExists() {
            try {
                console.log('🔧 确保基础数据表有必要数据...');

                // 确保customer_sources有基础数据
                const customerSources = ['自有客户', '运营线索', '运营转介绍', '客户转介绍', '管理内推'];
                for (const source of customerSources) {
                    await findOrCreateId('customer_sources', source);
                }

                // 确保business_types有基础数据
                const businessTypes = ['咨询服务', '咨询首款', '咨询尾款', '陪跑服务', '陪跑首付', '陪跑尾款', '培训服务', '培训占位', '其他'];
                for (const type of businessTypes) {
                    await findOrCreateId('business_types', type);
                }

                // 确保income_types有基础数据
                const incomeTypes = ['押金', '培训收入', '日签收入', '英签收入', '美签收入', '加签收入', '澳签收入', '咨询收入', '日租收入', '其他收入'];
                for (const type of incomeTypes) {
                    await findOrCreateId('income_types', type);
                }

                console.log('✅ 基础数据检查完成');

            } catch (error) {
                console.error('确保基础数据失败:', error);
            }
        }

        // 强力修复"未定义"问题的专用函数
        async function forceFixUndefinedData() {
            if (confirm('⚡ 强力修复"未定义"问题\n\n此操作将：\n• 强制创建所有缺失的基础数据\n• 为所有销售记录设置正确的关联\n• 解决所有"未定义"显示问题\n• 重新加载所有数据\n\n确定要执行吗？')) {

                try {
                    showToast('正在强力修复"未定义"问题，请稍候...', 'info');

                    if (!isSupabaseReady || !currentUser) {
                        alert('❌ 请先登录Supabase才能执行修复');
                        return;
                    }

                    // 1. 强制确保所有基础数据存在
                    await forceCreateBasicData();

                    // 2. 强制修复所有销售记录的关联
                    await forceFixAllRecords();

                    // 3. 重新加载系统设置
                    await loadSystemSettingsFromNewArchitecture();

                    // 4. 重新加载销售记录
                    await loadSalesRecordsFromDatabase();

                    // 5. 刷新界面
                    updateFormOptions();
                    updateStatistics();
                    updateSalaryDetails();

                    showToast('强力修复完成！', 'success');
                    alert('✅ 强力修复完成！\n\n所有"未定义"问题应该已解决。\n请检查销售记录表格。');

                } catch (error) {
                    console.error('强力修复失败:', error);
                    showToast('强力修复失败', 'error');
                    alert('❌ 强力修复失败：' + error.message);
                }
            }
        }

        // 强制创建所有基础数据
        async function forceCreateBasicData() {
            console.log('⚡ 强制创建基础数据...');

            // 客户来源数据
            const customerSources = [
                { name: '自有客户', description: '公司自有客户资源' },
                { name: '运营线索', description: '运营部门提供的客户线索' },
                { name: '运营转介绍', description: '运营转介绍的客户' },
                { name: '客户转介绍', description: '现有客户推荐的新客户' },
                { name: '管理内推', description: '管理层内部推荐' }
            ];

            // 业务类型数据
            const businessTypes = [
                { name: '咨询服务', description: '客户咨询服务' },
                { name: '咨询首款', description: '咨询服务首期款项' },
                { name: '咨询尾款', description: '咨询服务尾款' },
                { name: '陪跑服务', description: '客户陪跑服务' },
                { name: '陪跑首付', description: '陪跑服务首付款' },
                { name: '陪跑尾款', description: '陪跑服务尾款' },
                { name: '培训服务', description: '培训服务费用' },
                { name: '培训占位', description: '培训占位费' },
                { name: '其他', description: '其他业务类型' }
            ];

            // 收入类型数据
            const incomeTypes = [
                { name: '押金', description: '客户押金' },
                { name: '培训收入', description: '培训服务收入' },
                { name: '日签收入', description: '日本签证服务收入' },
                { name: '英签收入', description: '英国签证服务收入' },
                { name: '美签收入', description: '美国签证服务收入' },
                { name: '加签收入', description: '加拿大签证服务收入' },
                { name: '澳签收入', description: '澳洲签证服务收入' },
                { name: '咨询收入', description: '咨询服务收入' },
                { name: '日租收入', description: '日租服务收入' },
                { name: '其他收入', description: '其他类型收入' }
            ];

            // 批量插入数据
            await batchInsertData('customer_sources', customerSources);
            await batchInsertData('business_types', businessTypes);
            await batchInsertData('income_types', incomeTypes);

            console.log('✅ 基础数据创建完成');
        }

        // 批量插入数据的辅助函数
        async function batchInsertData(tableName, dataArray) {
            for (const item of dataArray) {
                try {
                    const { error } = await supabase
                        .from(tableName)
                        .upsert({
                            name: item.name,
                            description: item.description,
                            is_active: true
                        }, {
                            onConflict: 'name'
                        });

                    if (error && !error.message.includes('duplicate')) {
                        console.error(`插入${tableName}数据失败:`, error);
                    }
                } catch (err) {
                    console.error(`插入${tableName}数据异常:`, err);
                }
            }
        }

        // 强制修复所有销售记录的关联
        async function forceFixAllRecords() {
            console.log('⚡ 强制修复所有销售记录关联...');

            try {
                // 获取所有销售记录
                const { data: allRecords, error } = await supabase
                    .from('sales_records')
                    .select('*');

                if (error) {
                    console.error('查询销售记录失败:', error);
                    return;
                }

                console.log(`找到 ${allRecords?.length || 0} 条销售记录需要检查`);

                let fixedCount = 0;
                let detailedLog = [];

                for (const record of allRecords || []) {
                    const updates = {};
                    const recordLog = { id: record.id, fixes: [] };

                    console.log(`🔍 检查记录 ${record.id}:`, {
                        customer_source: record.customer_source,
                        customer_source_id: record.customer_source_id,
                        business_type: record.business_type,
                        business_type_id: record.business_type_id,
                        income_type: record.income_type,
                        income_type_id: record.income_type_id,
                        salesperson: record.salesperson,
                        salesperson_id: record.salesperson_id
                    });

                    // 强制设置customer_source_id
                    if (!record.customer_source_id) {
                        let sourceId = null;
                        if (record.customer_source && record.customer_source !== 'undefined') {
                            sourceId = await getIdByName('customer_sources', record.customer_source);
                            console.log(`🔍 查找客户来源 "${record.customer_source}": ${sourceId}`);
                        }
                        if (!sourceId) {
                            sourceId = await getIdByName('customer_sources', '自有客户');
                            console.log(`🔍 使用默认客户来源 "自有客户": ${sourceId}`);
                        }
                        if (sourceId) {
                            updates.customer_source_id = sourceId;
                            recordLog.fixes.push(`customer_source_id: ${sourceId}`);
                        }
                    }

                    // 强制设置business_type_id
                    if (!record.business_type_id) {
                        let typeId = null;
                        if (record.business_type && record.business_type !== 'undefined') {
                            typeId = await getIdByName('business_types', record.business_type);
                            console.log(`🔍 查找业务类型 "${record.business_type}": ${typeId}`);
                        }
                        if (!typeId) {
                            typeId = await getIdByName('business_types', '其他');
                            console.log(`🔍 使用默认业务类型 "其他": ${typeId}`);
                        }
                        if (typeId) {
                            updates.business_type_id = typeId;
                            recordLog.fixes.push(`business_type_id: ${typeId}`);
                        }
                    }

                    // 强制设置income_type_id
                    if (!record.income_type_id) {
                        let incomeId = null;
                        if (record.income_type && record.income_type !== 'undefined') {
                            incomeId = await getIdByName('income_types', record.income_type);
                            console.log(`🔍 查找收入类型 "${record.income_type}": ${incomeId}`);
                        }
                        if (!incomeId) {
                            incomeId = await getIdByName('income_types', '其他收入');
                            console.log(`🔍 使用默认收入类型 "其他收入": ${incomeId}`);
                        }
                        if (incomeId) {
                            updates.income_type_id = incomeId;
                            recordLog.fixes.push(`income_type_id: ${incomeId}`);
                        }
                    }

                    // 强制设置salesperson_id
                    if (!record.salesperson_id && record.salesperson && record.salesperson !== 'undefined') {
                        let salespersonId = await getIdByName('salespeople', record.salesperson);
                        console.log(`🔍 查找销售人员 "${record.salesperson}": ${salespersonId}`);

                        if (!salespersonId) {
                            // 如果销售人员不存在，创建一个
                            console.log(`🔧 创建新销售人员: ${record.salesperson}`);
                            const { data: newSalesperson, error: createError } = await supabase
                                .from('salespeople')
                                .insert({ name: record.salesperson, is_active: true })
                                .select('id')
                                .single();

                            if (!createError && newSalesperson) {
                                salespersonId = newSalesperson.id;
                                console.log(`✅ 创建销售人员成功: ${salespersonId}`);
                            } else {
                                console.error(`❌ 创建销售人员失败:`, createError);
                            }
                        }
                        if (salespersonId) {
                            updates.salesperson_id = salespersonId;
                            recordLog.fixes.push(`salesperson_id: ${salespersonId}`);
                        }
                    }

                    // 执行更新
                    if (Object.keys(updates).length > 0) {
                        console.log(`🔧 更新记录 ${record.id}:`, updates);
                        const { error: updateError } = await supabase
                            .from('sales_records')
                            .update(updates)
                            .eq('id', record.id);

                        if (updateError) {
                            console.error(`❌ 更新记录 ${record.id} 失败:`, updateError);
                        } else {
                            fixedCount++;
                            detailedLog.push(recordLog);
                            console.log(`✅ 强制修复记录 ${record.id} 成功`);
                        }
                    } else {
                        console.log(`ℹ️ 记录 ${record.id} 无需修复`);
                    }
                }

                console.log(`✅ 强制修复完成，共修复 ${fixedCount} 条记录`);
                console.log('📋 详细修复日志:', detailedLog);

            } catch (error) {
                console.error('强制修复所有记录失败:', error);
                throw error;
            }
        }

        // 根据名称获取ID的辅助函数
        async function getIdByName(tableName, name) {
            if (!name || name.trim() === '' || name === 'undefined') {
                console.log(`⚠️ getIdByName: 无效的名称 "${name}"`);
                return null;
            }

            try {
                console.log(`🔍 getIdByName: 在表 ${tableName} 中查找 "${name}"`);
                const { data, error } = await supabase
                    .from(tableName)
                    .select('id, name')
                    .eq('name', name.trim())
                    .eq('is_active', true)
                    .maybeSingle();

                if (error) {
                    console.error(`❌ getIdByName 查询失败:`, error);
                    return null;
                }

                if (!data) {
                    console.log(`⚠️ getIdByName: 在表 ${tableName} 中未找到 "${name}"`);
                    return null;
                }

                console.log(`✅ getIdByName: 找到 "${name}" -> ID: ${data.id}`);
                return data.id;
            } catch (err) {
                console.error(`❌ getIdByName 异常:`, err);
                return null;
            }
        }
        
        // 图表内容切换函数
        function toggleCardContent(cardId) {
            const contentElement = document.getElementById(cardId + 'Content');
            const toggleButton = event.target;
            
            if (contentElement) {
                if (contentElement.style.display === 'none') {
                    contentElement.style.display = 'block';
                    toggleButton.textContent = '−';
                } else {
                    contentElement.style.display = 'none';
                    toggleButton.textContent = '+';
                }
            }
        }



        // 将必要函数挂载到全局
        window.toggleCardContent = toggleCardContent;
        window.syncDataConsistency = syncDataConsistency;
        window.fixDataConsistency = fixDataConsistency;
        window.forceFixUndefinedData = forceFixUndefinedData;
        window.forceRefreshPermissions = forceRefreshPermissions;
    </script>

    <!-- 喜报弹窗 -->
    <div id="celebrationModal" class="celebration-modal">
        <div class="celebration-content">
            <div class="celebration-header">
                <button class="celebration-close" onclick="closeCelebrationModal()">×</button>
                <button class="celebration-copy" onclick="copyModalImage()" title="复制战报图片">📋</button>
                <div class="celebration-main-title">战报</div>
                <div class="celebration-trophy">🏆</div>
                <h2 class="celebration-title">🎉 恭喜！销售记录添加成功</h2>
                <div class="celebration-motto" id="celebrationMotto">新征程·新跨越 邀您一起见证</div>
            </div>
            <div class="celebration-body">
                <div class="celebration-time" id="celebrationTime"></div>
                <div class="celebration-details" id="celebrationDetails"></div>
            </div>
        </div>
    </div>



    <!-- 页面底部署名 -->
    <footer style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); color: #333; text-align: center; padding: 20px; margin-top: 30px; border-top: 1px solid #e9ecef;">
        <div style="font-size: 14px; opacity: 0.9;">
            <p style="margin: 0; padding: 5px 0;">
                🏢 <strong>销售记录管理系统</strong> | 版本 1.3
            </p>
            <p style="margin: 0; padding: 5px 0; font-size: 12px;">
                由运营部 <strong>吴畏</strong> 制作编制 | 2025年6月15日
            </p>
            <p style="margin: 0; padding: 5px 0; font-size: 11px; opacity: 0.8;">
                专业的销售数据管理与统计分析平台
            </p>
        </div>
    </footer>
</body>
</html>
