# MCP浏览器工具安装验证脚本
Write-Host "🔍 MCP浏览器工具安装验证" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# 确保Node.js在PATH中
$env:PATH += ";C:\Program Files\nodejs"

# 1. 检查Node.js和npm
Write-Host "1️⃣ 检查Node.js和npm..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "   ✅ Node.js版本: $nodeVersion" -ForegroundColor Green
    Write-Host "   ✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Node.js或npm未找到" -ForegroundColor Red
    exit 1
}

# 2. 检查已安装的MCP包
Write-Host ""
Write-Host "2️⃣ 检查已安装的MCP包..." -ForegroundColor Yellow

$mcpPackages = @(
    "@playwright/mcp",
    "@executeautomation/playwright-mcp-server", 
    "@agentdeskai/browser-tools-mcp",
    "@winds-ai/frontend-development-mcp-tools"
)

foreach ($package in $mcpPackages) {
    npm list -g $package 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ $package - 已安装" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $package - 未安装" -ForegroundColor Red
    }
}

# 3. 检查Playwright浏览器
Write-Host ""
Write-Host "3️⃣ 检查Playwright浏览器..." -ForegroundColor Yellow
try {
    $playwrightPath = "$env:LOCALAPPDATA\ms-playwright"
    if (Test-Path $playwrightPath) {
        $browsers = Get-ChildItem $playwrightPath -Directory | Where-Object { $_.Name -like "*chromium*" }
        if ($browsers.Count -gt 0) {
            Write-Host "   ✅ Chromium浏览器已安装" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ Chromium浏览器未找到" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ⚠️ Playwright浏览器目录未找到" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ 检查Playwright浏览器失败" -ForegroundColor Red
}

# 4. 测试MCP服务器启动
Write-Host ""
Write-Host "4️⃣ 测试MCP服务器启动..." -ForegroundColor Yellow

# 测试Playwright MCP
Write-Host "   测试 @playwright/mcp..." -ForegroundColor Gray
npx @playwright/mcp --help 2>$null | Out-Null
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ @playwright/mcp - 可以启动" -ForegroundColor Green
} else {
    Write-Host "   ❌ @playwright/mcp - 启动失败" -ForegroundColor Red
}

# 5. 检查配置文件
Write-Host ""
Write-Host "5️⃣ 检查配置文件..." -ForegroundColor Yellow
if (Test-Path "mcp-config.json") {
    Write-Host "   ✅ mcp-config.json - 存在" -ForegroundColor Green
} else {
    Write-Host "   ❌ mcp-config.json - 不存在" -ForegroundColor Red
}

if (Test-Path "启动MCP服务器.ps1") {
    Write-Host "   ✅ 启动MCP服务器.ps1 - 存在" -ForegroundColor Green
} else {
    Write-Host "   ❌ 启动MCP服务器.ps1 - 不存在" -ForegroundColor Red
}

# 6. 显示使用说明
Write-Host ""
Write-Host "🎯 使用说明:" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host "1. 启动MCP服务器:" -ForegroundColor White
Write-Host "   .\启动MCP服务器.ps1" -ForegroundColor Gray
Write-Host ""
Write-Host "2. 手动启动特定服务器:" -ForegroundColor White
Write-Host "   npx @playwright/mcp --headless --port 3001" -ForegroundColor Gray
Write-Host "   npx @executeautomation/playwright-mcp-server" -ForegroundColor Gray
Write-Host "   npx @agentdeskai/browser-tools-mcp" -ForegroundColor Gray
Write-Host "   npx @winds-ai/frontend-development-mcp-tools" -ForegroundColor Gray
Write-Host ""
Write-Host "3. 在AI工具中连接MCP服务器:" -ForegroundColor White
Write-Host "   - 使用配置文件: mcp-config.json" -ForegroundColor Gray
Write-Host "   - 或直接连接到端口: localhost:3001" -ForegroundColor Gray
Write-Host ""

# 7. 显示安装总结
Write-Host "📋 安装总结:" -ForegroundColor Magenta
Write-Host "================================" -ForegroundColor Magenta
Write-Host "✅ Node.js和npm已安装并配置" -ForegroundColor Green
Write-Host "✅ 4个MCP浏览器工具包已安装:" -ForegroundColor Green
Write-Host "   - @playwright/mcp (官方Playwright)" -ForegroundColor Gray
Write-Host "   - @executeautomation/playwright-mcp-server (增强版)" -ForegroundColor Gray
Write-Host "   - @agentdeskai/browser-tools-mcp (浏览器工具)" -ForegroundColor Gray
Write-Host "   - @winds-ai/frontend-development-mcp-tools (前端开发)" -ForegroundColor Gray
Write-Host "✅ Playwright Chromium浏览器已安装" -ForegroundColor Green
Write-Host "✅ 配置文件和启动脚本已创建" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 MCP浏览器工具安装完成！" -ForegroundColor Green
Write-Host "现在您可以在AI工具中使用这些MCP服务器来自动化浏览器操作了！" -ForegroundColor Yellow

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
