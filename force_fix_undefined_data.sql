-- =====================================================
-- 强力修复"未定义"问题的SQL脚本
-- 专门解决关联字段显示未定义的问题
-- =====================================================

-- 开始事务
BEGIN;

-- 1. 首先确保基础表有默认数据
INSERT INTO customer_sources (name, description, is_active) VALUES
('自有客户', '公司自有客户资源', true),
('运营线索', '运营部门提供的客户线索', true),
('运营转介绍', '运营转介绍的客户', true),
('客户转介绍', '现有客户推荐的新客户', true),
('管理内推', '管理层内部推荐', true)
ON CONFLICT (name) DO NOTHING;

INSERT INTO business_types (name, description, is_active) VALUES
('咨询服务', '客户咨询服务', true),
('咨询首款', '咨询服务首期款项', true),
('咨询尾款', '咨询服务尾款', true),
('陪跑服务', '客户陪跑服务', true),
('陪跑首付', '陪跑服务首付款', true),
('陪跑尾款', '陪跑服务尾款', true),
('培训服务', '培训服务费用', true),
('培训占位', '培训占位费', true),
('其他', '其他业务类型', true)
ON CONFLICT (name) DO NOTHING;

INSERT INTO income_types (name, description, is_active) VALUES
('押金', '客户押金', true),
('培训收入', '培训服务收入', true),
('日签收入', '日本签证服务收入', true),
('英签收入', '英国签证服务收入', true),
('美签收入', '美国签证服务收入', true),
('加签收入', '加拿大签证服务收入', true),
('澳签收入', '澳洲签证服务收入', true),
('咨询收入', '咨询服务收入', true),
('日租收入', '日租服务收入', true),
('其他收入', '其他类型收入', true)
ON CONFLICT (name) DO NOTHING;

-- 2. 从sales_records中提取所有唯一的销售人员并添加到salespeople表
INSERT INTO salespeople (name, is_active)
SELECT DISTINCT salesperson, true
FROM sales_records 
WHERE salesperson IS NOT NULL 
AND salesperson != '' 
AND salesperson != '未定义'
AND NOT EXISTS (
    SELECT 1 FROM salespeople 
    WHERE salespeople.name = sales_records.salesperson
);

-- 3. 强制更新所有sales_records的关联ID
-- 更新customer_source_id
UPDATE sales_records 
SET customer_source_id = (
    SELECT cs.id 
    FROM customer_sources cs 
    WHERE cs.name = sales_records.customer_source 
    AND cs.is_active = true 
    LIMIT 1
)
WHERE customer_source_id IS NULL 
AND customer_source IS NOT NULL 
AND customer_source != '';

-- 更新business_type_id
UPDATE sales_records 
SET business_type_id = (
    SELECT bt.id 
    FROM business_types bt 
    WHERE bt.name = sales_records.business_type 
    AND bt.is_active = true 
    LIMIT 1
)
WHERE business_type_id IS NULL 
AND business_type IS NOT NULL 
AND business_type != '';

-- 更新income_type_id
UPDATE sales_records 
SET income_type_id = (
    SELECT it.id 
    FROM income_types it 
    WHERE it.name = sales_records.income_type 
    AND it.is_active = true 
    LIMIT 1
)
WHERE income_type_id IS NULL 
AND income_type IS NOT NULL 
AND income_type != '';

-- 更新salesperson_id
UPDATE sales_records 
SET salesperson_id = (
    SELECT sp.id 
    FROM salespeople sp 
    WHERE sp.name = sales_records.salesperson 
    AND sp.is_active = true 
    LIMIT 1
)
WHERE salesperson_id IS NULL 
AND salesperson IS NOT NULL 
AND salesperson != '';

-- 4. 为仍然没有关联的记录设置默认值
-- 设置默认customer_source_id
UPDATE sales_records 
SET customer_source_id = (
    SELECT id FROM customer_sources 
    WHERE name = '自有客户' AND is_active = true 
    LIMIT 1
)
WHERE customer_source_id IS NULL;

-- 设置默认business_type_id
UPDATE sales_records 
SET business_type_id = (
    SELECT id FROM business_types 
    WHERE name = '其他' AND is_active = true 
    LIMIT 1
)
WHERE business_type_id IS NULL;

-- 设置默认income_type_id
UPDATE sales_records 
SET income_type_id = (
    SELECT id FROM income_types 
    WHERE name = '其他收入' AND is_active = true 
    LIMIT 1
)
WHERE income_type_id IS NULL;

-- 5. 重新创建或更新sales_records_detail视图
DROP VIEW IF EXISTS sales_records_detail;

CREATE VIEW sales_records_detail AS
SELECT 
    sr.id,
    sr.frontend_id,
    sr.date,
    sr.customer,
    COALESCE(cs.name, '未知来源') as customer_source,
    COALESCE(bt.name, '未知业务') as business_type,
    COALESCE(it.name, '未知收入') as income_type,
    COALESCE(sp.name, '未知销售') as salesperson,
    sr.amount,
    sr.contract_amount,
    sr.total_received,
    sr.payment_status,
    sr.payment_count,
    sr.last_payment_date,
    sr.remarks,
    sr.user_id,
    sr.created_by,
    sr.created_at,
    sr.updated_at
FROM sales_records sr
LEFT JOIN customer_sources cs ON sr.customer_source_id = cs.id
LEFT JOIN business_types bt ON sr.business_type_id = bt.id
LEFT JOIN income_types it ON sr.income_type_id = it.id
LEFT JOIN salespeople sp ON sr.salesperson_id = sp.id
ORDER BY sr.created_at DESC;

-- 6. 验证修复结果
SELECT '=== 修复结果验证 ===' as section;

SELECT 
    '修复后的sales_records关联状态' as check_item,
    COUNT(*) as total_records,
    COUNT(customer_source_id) as has_customer_source_id,
    COUNT(business_type_id) as has_business_type_id,
    COUNT(income_type_id) as has_income_type_id,
    COUNT(salesperson_id) as has_salesperson_id
FROM sales_records;

-- 7. 显示修复后的数据样本
SELECT 
    '修复后的数据样本' as section,
    customer,
    customer_source,
    business_type,
    income_type,
    salesperson,
    amount,
    contract_amount
FROM sales_records_detail 
LIMIT 5;

-- 提交事务
COMMIT;

-- 显示完成信息
SELECT '✅ 强力修复完成！所有"未定义"问题应该已解决。请刷新前端页面查看效果。' as message;
