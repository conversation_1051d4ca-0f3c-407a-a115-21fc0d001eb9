// =====================================================
// 最终验证脚本 - 验证所有修复是否生效
// 在浏览器控制台中运行此脚本
// =====================================================

console.log('🎯 启动最终验证脚本...');

class 最终验证器 {
    constructor() {
        this.测试结果 = [];
        this.问题列表 = [];
        this.修复建议 = [];
    }

    记录结果(测试名, 状态, 详情 = '') {
        const 结果 = { 测试名, 状态, 详情, 时间: new Date().toISOString() };
        this.测试结果.push(结果);
        
        const 图标 = { 'PASS': '✅', 'FAIL': '❌', 'WARN': '⚠️' };
        console.log(`${图标[状态]} ${测试名}: ${详情}`);
        
        if (状态 === 'FAIL') {
            this.问题列表.push(`${测试名}: ${详情}`);
        }
    }

    添加修复建议(建议) {
        this.修复建议.push(建议);
        console.log(`💡 修复建议: ${建议}`);
    }

    // 1. 验证基础环境
    验证基础环境() {
        console.log('\n🔍 1. 验证基础环境...');
        
        // 检查关键变量
        if (typeof supabase !== 'undefined') {
            this.记录结果('Supabase连接', 'PASS', 'Supabase客户端已加载');
        } else {
            this.记录结果('Supabase连接', 'FAIL', 'Supabase客户端未找到');
        }

        if (typeof salesData !== 'undefined' && Array.isArray(salesData)) {
            this.记录结果('销售数据', 'PASS', `salesData数组存在，包含${salesData.length}条记录`);
        } else {
            this.记录结果('销售数据', 'FAIL', 'salesData未定义或不是数组');
        }

        if (typeof currentUser !== 'undefined' && currentUser) {
            this.记录结果('用户认证', 'PASS', `当前用户: ${currentUser.email}`);
        } else {
            this.记录结果('用户认证', 'FAIL', '用户未登录');
        }

        // 检查关键函数
        const 关键函数 = ['loadTableData', 'openPaymentModal', 'loadPaymentRecords', 'addPaymentRecord'];
        关键函数.forEach(函数名 => {
            if (typeof window[函数名] === 'function') {
                this.记录结果(`函数${函数名}`, 'PASS', '函数存在');
            } else {
                this.记录结果(`函数${函数名}`, 'FAIL', '函数不存在');
            }
        });
    }

    // 2. 验证数据结构
    验证数据结构() {
        console.log('\n🔍 2. 验证数据结构...');
        
        if (!salesData || salesData.length === 0) {
            this.记录结果('数据结构验证', 'WARN', '没有销售数据可验证');
            return;
        }

        const 示例记录 = salesData[0];
        const 必需字段 = ['id', 'databaseId', 'customer', 'contractAmount'];
        
        必需字段.forEach(字段 => {
            if (示例记录.hasOwnProperty(字段) && 示例记录[字段] !== undefined) {
                this.记录结果(`字段${字段}`, 'PASS', `值: ${示例记录[字段]}`);
            } else {
                this.记录结果(`字段${字段}`, 'FAIL', '字段缺失或为undefined');
            }
        });

        // 检查ID映射
        let ID映射正常 = 0;
        let ID映射异常 = 0;
        
        salesData.forEach(记录 => {
            if (记录.databaseId && 记录.databaseId !== 记录.id) {
                ID映射正常++;
            } else {
                ID映射异常++;
            }
        });

        if (ID映射正常 > 0) {
            this.记录结果('ID映射', 'PASS', `${ID映射正常}条记录有正确的数据库ID映射`);
        } else {
            this.记录结果('ID映射', 'FAIL', `${ID映射异常}条记录缺少数据库ID映射`);
            this.添加修复建议('运行ID映射修复脚本');
        }
    }

    // 3. 测试数据库连接
    async 测试数据库连接() {
        console.log('\n🔍 3. 测试数据库连接...');
        
        try {
            // 测试销售记录查询
            const 销售记录查询 = await supabase.from('sales_records').select('count', { count: 'exact' });
            if (销售记录查询.error) {
                this.记录结果('销售记录表', 'FAIL', `查询失败: ${销售记录查询.error.message}`);
            } else {
                this.记录结果('销售记录表', 'PASS', `表存在，总记录数: ${销售记录查询.count}`);
            }

            // 测试收款记录表
            const 收款记录查询 = await supabase.from('payment_records').select('count', { count: 'exact' });
            if (收款记录查询.error) {
                this.记录结果('收款记录表', 'FAIL', `查询失败: ${收款记录查询.error.message}`);
                this.添加修复建议('需要创建payment_records表');
            } else {
                this.记录结果('收款记录表', 'PASS', `表存在，总记录数: ${收款记录查询.count}`);
            }

        } catch (error) {
            this.记录结果('数据库连接测试', 'FAIL', `异常: ${error.message}`);
        }
    }

    // 4. 测试收款功能
    async 测试收款功能() {
        console.log('\n🔍 4. 测试收款功能...');
        
        if (!salesData || salesData.length === 0) {
            this.记录结果('收款功能测试', 'WARN', '没有销售数据可测试');
            return;
        }

        const 测试记录 = salesData[0];
        
        // 测试收款记录查询
        try {
            if (!测试记录.databaseId) {
                this.记录结果('收款查询前提', 'FAIL', '测试记录缺少数据库ID');
                return;
            }

            const 收款查询 = await supabase
                .from('payment_records')
                .select('*')
                .eq('sales_record_id', 测试记录.databaseId);

            if (收款查询.error) {
                this.记录结果('收款记录查询', 'FAIL', `查询失败: ${收款查询.error.message}`);
            } else {
                this.记录结果('收款记录查询', 'PASS', `查询成功，找到${收款查询.data.length}条记录`);
                
                // 验证字段结构
                if (收款查询.data.length > 0) {
                    const 收款记录 = 收款查询.data[0];
                    const 收款字段 = ['id', 'sales_record_id', 'payment_date', 'amount', 'payment_method'];
                    
                    收款字段.forEach(字段 => {
                        if (收款记录.hasOwnProperty(字段)) {
                            this.记录结果(`收款字段${字段}`, 'PASS', '字段存在');
                        } else {
                            this.记录结果(`收款字段${字段}`, 'FAIL', '字段缺失');
                        }
                    });
                }
            }

        } catch (error) {
            this.记录结果('收款功能测试异常', 'FAIL', error.message);
        }
    }

    // 5. 测试收款弹窗
    async 测试收款弹窗() {
        console.log('\n🔍 5. 测试收款弹窗...');
        
        if (!salesData || salesData.length === 0) {
            this.记录结果('收款弹窗测试', 'WARN', '没有销售数据可测试');
            return;
        }

        const 测试记录 = salesData[0];
        
        try {
            // 检查弹窗元素是否存在
            const 弹窗元素 = document.getElementById('paymentModal');
            if (弹窗元素) {
                this.记录结果('收款弹窗元素', 'PASS', '弹窗元素存在');
            } else {
                this.记录结果('收款弹窗元素', 'FAIL', '弹窗元素不存在');
                return;
            }

            // 测试打开弹窗
            if (typeof openPaymentModal === 'function') {
                await openPaymentModal(测试记录.id);
                
                // 检查弹窗是否显示
                setTimeout(() => {
                    if (弹窗元素.style.display === 'block') {
                        this.记录结果('收款弹窗打开', 'PASS', '弹窗成功打开');
                        
                        // 检查弹窗内容
                        const 客户名元素 = document.getElementById('contractCustomer');
                        const 合同金额元素 = document.getElementById('contractAmount');
                        
                        if (客户名元素 && 客户名元素.textContent === 测试记录.customer) {
                            this.记录结果('弹窗客户信息', 'PASS', '客户信息正确');
                        } else {
                            this.记录结果('弹窗客户信息', 'FAIL', '客户信息不正确');
                        }

                        if (合同金额元素 && 合同金额元素.textContent.includes(测试记录.contractAmount)) {
                            this.记录结果('弹窗合同金额', 'PASS', '合同金额正确');
                        } else {
                            this.记录结果('弹窗合同金额', 'FAIL', '合同金额不正确');
                        }
                        
                        // 关闭弹窗
                        if (typeof closePaymentModal === 'function') {
                            closePaymentModal();
                        }
                    } else {
                        this.记录结果('收款弹窗打开', 'FAIL', '弹窗未显示');
                    }
                }, 1000);
                
            } else {
                this.记录结果('收款弹窗函数', 'FAIL', 'openPaymentModal函数不存在');
            }

        } catch (error) {
            this.记录结果('收款弹窗测试异常', 'FAIL', error.message);
        }
    }

    // 6. 生成最终报告
    生成最终报告() {
        console.log('\n📋 ===== 最终验证报告 =====');
        
        const 通过数 = this.测试结果.filter(r => r.状态 === 'PASS').length;
        const 失败数 = this.测试结果.filter(r => r.状态 === 'FAIL').length;
        const 警告数 = this.测试结果.filter(r => r.状态 === 'WARN').length;
        
        console.log(`📊 测试统计:`);
        console.log(`   总测试数: ${this.测试结果.length}`);
        console.log(`   ✅ 通过: ${通过数}`);
        console.log(`   ❌ 失败: ${失败数}`);
        console.log(`   ⚠️ 警告: ${警告数}`);
        
        const 成功率 = ((通过数 / this.测试结果.length) * 100).toFixed(1);
        console.log(`   🎯 成功率: ${成功率}%`);
        
        if (this.问题列表.length > 0) {
            console.log(`\n❌ 发现的问题:`);
            this.问题列表.forEach((问题, 索引) => {
                console.log(`   ${索引 + 1}. ${问题}`);
            });
        }
        
        if (this.修复建议.length > 0) {
            console.log(`\n💡 修复建议:`);
            this.修复建议.forEach((建议, 索引) => {
                console.log(`   ${索引 + 1}. ${建议}`);
            });
        }
        
        if (失败数 === 0) {
            console.log('\n🎉 所有测试通过！系统功能正常！');
        } else if (失败数 <= 2) {
            console.log('\n⚠️ 发现少量问题，建议按修复建议进行修复');
        } else {
            console.log('\n❌ 发现多个问题，需要进行系统性修复');
        }
        
        return {
            总数: this.测试结果.length,
            通过: 通过数,
            失败: 失败数,
            警告: 警告数,
            成功率: 成功率,
            问题列表: this.问题列表,
            修复建议: this.修复建议
        };
    }

    // 主验证流程
    async 开始最终验证() {
        console.log('🚀 开始最终系统验证...');
        console.log('=====================================');
        
        this.验证基础环境();
        this.验证数据结构();
        await this.测试数据库连接();
        await this.测试收款功能();
        await this.测试收款弹窗();
        
        const 报告 = this.生成最终报告();
        
        console.log('\n🎯 验证完成！');
        return 报告;
    }
}

// 创建验证器实例
const 验证器 = new 最终验证器();

// 导出到全局
window.最终验证器 = 验证器;
window.开始最终验证 = () => 验证器.开始最终验证();

console.log('🎯 最终验证脚本已加载');
console.log('运行命令: 开始最终验证()');

// 自动开始验证
验证器.开始最终验证();
